var s=Object.defineProperty;var i=(r,t)=>s(r,"name",{value:t,configurable:!0});import{r as p}from"../../register-DRJeZPTf.mjs";import"../../get-pipe-path-BHW2eJdv.mjs";import{r as n,i as c,c as u}from"../../register-DqTWLVYd.mjs";import"../../require-CLFX0w80.mjs";import{i as g,e as d}from"../../node-features-Bp94mr_V.mjs";import"node:module";import"node:worker_threads";import"node:url";import"module";import"node:path";import"../../temporary-directory-CwHp0_NW.mjs";import"node:os";import"get-tsconfig";import"node:fs";import"../../index-7AaEi15b.mjs";import"esbuild";import"node:crypto";import"../../client-BQVF1NaW.mjs";import"node:net";import"node:util";import"../../index-gbaejti9.mjs";const f=i((r,t)=>{if(!t||typeof t=="object"&&!t.parentURL)throw new Error("The current file path (import.meta.url) must be provided in the second argument of tsImport()");const e=typeof t=="string",o=e?t:t.parentURL,m=Date.now().toString(),a=n({namespace:m});return!g(d)&&!c.test(r)&&u.test(r)?Promise.resolve(a.require(r,o)):p({namespace:m,...e?{}:t}).import(r,o)},"tsImport");export{p as register,f as tsImport};
