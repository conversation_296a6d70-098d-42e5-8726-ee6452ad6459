var K=Object.defineProperty;var m=(t,e)=>K(t,"name",{value:e,configurable:!0});import{isMainThread as V}from"node:worker_threads";import{i as p,a as Y,e as T,l as w,r as Z,m as x}from"../node-features-Bp94mr_V.mjs";import{r as tt}from"../register-DRJeZPTf.mjs";import"../get-pipe-path-BHW2eJdv.mjs";import"node:module";import u from"node:path";import{fileURLToPath as N,pathToFileURL as _}from"node:url";import"get-tsconfig";import{l as J,t as y,f as U,b as h,d as l,e as et,g as d,h as C,j as W,k,m as A,n as st,o as I,p as at,q as M}from"../register-DqTWLVYd.mjs";import $ from"node:fs";import"esbuild";import"node:crypto";import{t as D,a as j,i as q,b as B,r as rt}from"../index-7AaEi15b.mjs";import{p as Q}from"../client-BQVF1NaW.mjs";import"../require-CLFX0w80.mjs";import{readFile as R}from"node:fs/promises";import"module";import"../temporary-directory-CwHp0_NW.mjs";import"node:os";import"node:util";import"../index-gbaejti9.mjs";import"node:net";const f={active:!0},ot=m(async t=>{if(!t)throw new Error(`tsx must be loaded with --import instead of --loader
The --loader flag was deprecated in Node v20.6.0 and v18.19.0`);f.namespace=t.namespace,t.tsconfig!==!1&&J(t.tsconfig??process.env.TSX_TSCONFIG_PATH),t.port&&(f.port=t.port,t.port.on("message",e=>{e==="deactivate"&&(f.active=!1,t.port.postMessage({type:"deactivated"}))}))},"initialize"),nt=m(()=>(J(process.env.TSX_TSCONFIG_PATH),"process.setSourceMapsEnabled(true);"),"globalPreload"),P=new Map,it=m(async t=>{if(P.has(t))return P.get(t);if(!await $.promises.access(t).then(()=>!0,()=>!1)){P.set(t,void 0);return}const o=await $.promises.readFile(t,"utf8");try{const r=JSON.parse(o);return P.set(t,r),r}catch{throw new Error(`Error parsing: ${t}`)}},"readPackageJson"),ct=m(async t=>{let e=new URL("package.json",t);for(;!e.pathname.endsWith("/node_modules/package.json");){const o=N(e),r=await it(o);if(r)return r;const s=e;if(e=new URL("../package.json",e),e.pathname===s.pathname)break}},"findPackageJson"),mt=m(async t=>(await ct(t))?.type??"commonjs","getPackageType"),dt=m(t=>{[t]=t.split("?");const e=u.extname(t);if(e===".mts")return"module";if(e===".cts")return"commonjs"},"getFormatFromExtension"),ft=m(t=>{const e=dt(t);if(e)return e;if(y.test(t))return mt(t)},"getFormatFromFileUrl"),v="tsx-namespace=",E=m(t=>{const e=t.indexOf(v);if(e===-1)return;const o=t[e-1];if(o!=="?"&&o!=="&")return;const r=e+v.length,s=t.indexOf("&",r);return s===-1?t.slice(r):t.slice(r,s)},"getNamespace"),S=m(t=>{if(!t.includes("%3F"))return t;const[e,o]=t.split("%3F",2),r=decodeURIComponent(o);return`${e}?${r}`},"decodeCjsQuery"),G=p(Y)?"importAttributes":"importAssertions";let b=m(async(t,e,o)=>{if(!f.active)return o(t,e);t=S(t);const r=E(t);if(f.namespace&&f.namespace!==r)return o(t,e);if(f.port){const a=new URL(t);a.searchParams.delete("tsx-namespace"),f.port.postMessage({type:"load",url:a.toString()})}Q.send&&Q.send({type:"dependency",path:t});const s=t.startsWith(U)?N(t):t;if(e.format==="module-json"){const a=await R(new URL(t),"utf8"),c=await D(a,s,{tsconfigRaw:u.isAbsolute(s)?h?.(s):void 0});return{shortCircuit:!0,format:"module",source:l(c)}}if(e.format==="commonjs-json"){const a=await R(new URL(t),"utf8"),c=j(a,s,{tsconfigRaw:u.isAbsolute(s)?h?.(s):void 0});return c.code+=`
0 && (module.exports = ${JSON.stringify(Object.fromEntries(Object.keys(JSON.parse(a)).map(g=>[g,0])))});`,{shortCircuit:!0,format:"commonjs",source:l(c)}}if((e.format===void 0||e.format==="commonjs"||e.format==="commonjs-typescript")&&p(T)&&t.startsWith("file:")&&s.endsWith(".js")){const a=await R(new URL(t),"utf8");if(q(a)){const c=j(a,t,{tsconfigRaw:h?.(s)});if(p(w))return{shortCircuit:!0,format:"commonjs",source:l(c)};const g=r?`${s}?namespace=${encodeURIComponent(r)}`:s;return{shortCircuit:!0,format:"commonjs",responseURL:`data:text/javascript,${encodeURIComponent(c.code)}?filePath=${encodeURIComponent(g)}`}}}if(et.test(t)){let a=e[G];a||(a={},e[G]=a),a.type||(a.type="json")}const i=await o(t,e);if(d("loaded by next loader",{url:t,loaded:i}),p(w)&&i.format==="commonjs"&&s.endsWith(".cjs")){let a=await R(new URL(t),"utf8");const c=B(s,a);return c&&(a=l(c)),i.source=a,i.shortCircuit=!0,i}if(i.format==="commonjs"&&p(T)&&i.responseURL?.startsWith("file:")&&!s.endsWith(".cjs")){const a=await R(new URL(t),"utf8");if(!s.endsWith(".js")||q(a)){const c=j(a,t,{tsconfigRaw:h?.(s)});if(p(w))i.source=l(c);else{const g=r?`${s}?namespace=${encodeURIComponent(r)}`:s;i.responseURL=`data:text/javascript,${encodeURIComponent(c.code)}?filePath=${encodeURIComponent(g)}`}return d("returning CJS export annotation",i),i}}if(!i.source)return i;const n=i.source.toString();if(p(Z)&&i.format==="json"){const a=j(n,s,{tsconfigRaw:u.isAbsolute(s)?h?.(s):void 0});return a.code+=`
0 && (module.exports = ${JSON.stringify(Object.fromEntries(Object.keys(JSON.parse(n)).map(c=>[c,0])))});`,{format:"commonjs",source:l(a)}}if(i.format==="json"||y.test(t)){const a=await D(n,s,{tsconfigRaw:u.isAbsolute(s)?h?.(s):void 0});return{format:"module",source:l(a)}}if(i.format==="module"){const a=B(s,n);a&&(i.source=l(a))}return i},"load");if(C){const t=b;b=m(async(e,o,r)=>{d("load",{url:e,context:o});const s=await t(e,o,r);return d("loaded",{url:e,result:s}),s},"load")}const H=m(t=>{if(t.url)return t.url;const e=t.message.match(/^Cannot find module '([^']+)'/);if(e){const[,r]=e;return r}const o=t.message.match(/^Cannot find package '([^']+)'/);if(o){const[,r]=o;if(!u.isAbsolute(r))return;const s=_(r);if(s.pathname.endsWith("/")&&(s.pathname+="package.json"),s.pathname.endsWith("/package.json")){const i=rt(s);if(i?.main)return new URL(i.main,s).toString()}else return s.toString()}},"getMissingPathFromNotFound"),L=m(async(t,e,o,r)=>{const s=st(t);if(d("resolveExtensions",{url:t,context:e,throwError:r,tryPaths:s}),!s)return;let i;for(const n of s)try{return await o(n,e)}catch(a){const{code:c}=a;if(c!=="ERR_MODULE_NOT_FOUND"&&c!=="ERR_PACKAGE_PATH_NOT_EXPORTED")throw a;i=a}if(r)throw i},"resolveExtensions"),pt=m(async(t,e,o)=>{if(d("resolveBase",{specifier:t,context:e,specifierStartsWithFileUrl:t.startsWith(U),isRelativePath:I(t),specifierNodeModules:t.includes(at),tsExtensionsPattern:y.test(e.parentURL),allowJs:M}),(t.startsWith(U)||I(t))&&!t.includes("/node_modules/")&&(y.test(e.parentURL)||M)){const r=await L(t,e,o);if(d("resolveBase resolved",{specifier:t,context:e,resolved:r}),r)return r}try{return await o(t,e)}catch(r){if(d("resolveBase error",{specifier:t,context:e,error:r}),r instanceof Error){const s=r;if(s.code==="ERR_MODULE_NOT_FOUND"){const i=H(s);if(i){const n=await L(i,e,o);if(n)return n}}}throw r}},"resolveBase"),X=m(async(t,e,o)=>{if(d("resolveDirectory",{specifier:t,context:e,isDirectory:A.test(t)}),(t==="."||t===".."||t.endsWith("/.."))&&(t+="/"),A.test(t)){const r=new URL(t,e.parentURL);return r.pathname=u.join(r.pathname,"index"),await L(r.toString(),e,o,!0)}try{return await pt(t,e,o)}catch(r){if(r instanceof Error){d("resolveDirectory error",{specifier:t,context:e,error:r});const s=r;if(s.code==="ERR_UNSUPPORTED_DIR_IMPORT"){const i=H(s);if(i)try{return await L(`${i}/index`,e,o,!0)}catch(n){const a=n,{message:c}=a;throw a.message=a.message.replace(`${"/index".replace("/",u.sep)}'`,"'"),a.stack=a.stack.replace(c,a.message),a}}}throw r}},"resolveDirectory"),ut=m(async(t,e,o)=>{if(d("resolveTsPaths",{specifier:t,context:e,requestAcceptsQuery:W(t),tsconfigPathsMatcher:k,fromNodeModules:e.parentURL?.includes("/node_modules/")}),!W(t)&&k&&!e.parentURL?.includes("/node_modules/")){const r=k(t);d("resolveTsPaths",{possiblePaths:r});for(const s of r)try{return await X(_(s).toString(),e,o)}catch{}}return X(t,e,o)},"resolveTsPaths"),z="tsx://",F=new Map;let O=m(async(t,e,o)=>{if(!f.active||t.startsWith("node:"))return o(t,e);p(w)&&(t.includes("%3F")&&(t=S(t)),e.parentURL?.includes("%3F")&&(e.parentURL=S(e.parentURL)));let r=E(t)??(e.parentURL&&E(e.parentURL));if(f.namespace){let a;if(t.startsWith(z)){try{a=JSON.parse(t.slice(z.length))}catch{}a?.namespace&&(r=a.namespace)}if(f.namespace!==r)return o(t,e);a&&(t=a.specifier,e.parentURL=a.parentURL)}const[s,i]=t.split("?"),n=await ut(s,e,o);if(n.format==="builtin")return n;if(!n.format&&n.url.startsWith(U)&&(n.format=await ft(n.url)),i&&(n.url+=`?${i}`),r&&!n.url.includes(v)&&(n.url+=(n.url.includes("?")?"&":"?")+v+r),n.format==="commonjs-typescript"&&(n.format="commonjs"),n.format==="module-typescript"&&(n.format="module"),n.format==="module"&&e.parentURL&&F.get(e.parentURL)==="commonjs"&&(n.format="commonjs"),n.format==="json"&&e.parentURL){const a=F.get(e.parentURL);a==="commonjs"&&(n.format="commonjs-json"),a==="module"&&(n.format="module-json")}if(F.set(n.url,n.format),p([[20,11,0],[22,10,0]])&&n.format==="commonjs"&&n.url.includes("?")){const[a,c]=n.url.split("?");n.url=a+encodeURIComponent(`?${c}`)}return n},"resolve");if(C){const t=O;O=m(async(e,o,r)=>{d("resolve",{specifier:e,context:o});const s=await t(e,o,r);return d("resolved",{specifier:e,context:o,result:s}),s},"resolve")}p(x)&&V&&tt();export{nt as globalPreload,ot as initialize,b as load,O as resolve};
