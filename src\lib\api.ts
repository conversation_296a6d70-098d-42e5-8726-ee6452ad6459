// API client functions for VistaNotes

export interface Client {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth?: string;
  ehrId?: string;
  isActive: boolean;
  lastSession?: string;
  totalSessions: number;
  status: string;
}

export interface SessionNote {
  id: string;
  clientId: string;
  therapistId: string;
  sessionDate: string;
  sessionDuration?: number;
  noteType: string;
  subjective?: string;
  objective?: string;
  assessment?: string;
  plan?: string;
  interventions?: string;
  clientMood?: string;
  isFinalized: boolean;
  createdAt: string;
  updatedAt: string;
  client?: {
    id: string;
    firstName: string;
    lastName: string;
  };
  multimedia?: MultimediaFile[];
}

export interface MultimediaFile {
  id: string;
  sessionNoteId: string;
  fileName: string;
  originalName: string;
  fileType: 'IMAGE' | 'VIDEO' | 'AUDIO';
  fileSize: number;
  s3Key: string;
  s3Bucket: string;
  description?: string;
  aiDescription?: string;
  uploadedAt: string;
}

export interface CreateSessionNoteRequest {
  clientId: string;
  sessionDate: string;
  sessionDuration?: number;
  noteType?: string;
  subjective?: string;
  objective?: string;
  assessment?: string;
  plan?: string;
  interventions?: string;
  clientMood?: string;
  isFinalized?: boolean;
}

// Client API functions
export async function fetchClients(params?: {
  search?: string;
  status?: string;
  limit?: number;
}): Promise<Client[]> {
  const searchParams = new URLSearchParams();
  
  if (params?.search) searchParams.set('search', params.search);
  if (params?.status) searchParams.set('status', params.status);
  if (params?.limit) searchParams.set('limit', params.limit.toString());

  const response = await fetch(`/api/clients?${searchParams}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch clients');
  }
  
  return response.json();
}

export async function createClient(data: {
  firstName: string;
  lastName: string;
  dateOfBirth?: string;
  ehrId?: string;
}): Promise<Client> {
  const response = await fetch('/api/clients', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    throw new Error('Failed to create client');
  }
  
  return response.json();
}

// Session Notes API functions
export async function fetchSessionNotes(params?: {
  clientId?: string;
  status?: string;
  limit?: number;
}): Promise<SessionNote[]> {
  const searchParams = new URLSearchParams();
  
  if (params?.clientId) searchParams.set('clientId', params.clientId);
  if (params?.status) searchParams.set('status', params.status);
  if (params?.limit) searchParams.set('limit', params.limit.toString());

  const response = await fetch(`/api/notes?${searchParams}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch session notes');
  }
  
  return response.json();
}

export async function fetchSessionNote(id: string): Promise<SessionNote> {
  const response = await fetch(`/api/notes/${id}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch session note');
  }
  
  return response.json();
}

export async function createSessionNote(data: CreateSessionNoteRequest): Promise<SessionNote> {
  const response = await fetch('/api/notes', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    throw new Error('Failed to create session note');
  }
  
  return response.json();
}

export async function updateSessionNote(id: string, data: Partial<CreateSessionNoteRequest>): Promise<SessionNote> {
  const response = await fetch(`/api/notes/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    throw new Error('Failed to update session note');
  }
  
  return response.json();
}

export async function deleteSessionNote(id: string): Promise<void> {
  const response = await fetch(`/api/notes/${id}`, {
    method: 'DELETE',
  });
  
  if (!response.ok) {
    throw new Error('Failed to delete session note');
  }
}

// Multimedia API functions
export async function uploadMultimediaFile(
  file: File,
  sessionNoteId: string,
  description?: string
): Promise<MultimediaFile> {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('sessionNoteId', sessionNoteId);
  if (description) formData.append('description', description);

  const response = await fetch('/api/multimedia/upload', {
    method: 'POST',
    body: formData,
  });
  
  if (!response.ok) {
    throw new Error('Failed to upload file');
  }
  
  return response.json();
}

export async function fetchMultimediaFiles(sessionNoteId: string): Promise<MultimediaFile[]> {
  const response = await fetch(`/api/multimedia/upload?sessionNoteId=${sessionNoteId}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch multimedia files');
  }
  
  return response.json();
}
