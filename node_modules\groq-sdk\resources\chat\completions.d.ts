import { APIResource } from "../../resource.js";
import * as Core from "../../core.js";
import * as ChatCompletionsAPI from "./completions.js";
import * as CompletionsAPI from "../completions.js";
import * as Shared from "../shared.js";
import { Stream } from "../../lib/streaming.js";
export declare class Completions extends APIResource {
    /**
     * Creates a model response for the given chat conversation.
     *
     * @example
     * ```ts
     * const chatCompletion = await client.chat.completions.create(
     *   {
     *     messages: [{ content: 'content', role: 'system' }],
     *     model: 'meta-llama/llama-4-scout-17b-16e-instruct',
     *   },
     * );
     * ```
     */
    create(body: ChatCompletionCreateParamsNonStreaming, options?: Core.RequestOptions): Core.APIPromise<ChatCompletion>;
    create(body: ChatCompletionCreateParamsStreaming, options?: Core.RequestOptions): Core.APIPromise<Stream<ChatCompletionChunk>>;
    create(body: ChatCompletionCreateParamsBase, options?: Core.RequestOptions): Core.APIPromise<Stream<ChatCompletionChunk> | ChatCompletion>;
}
/**
 * Represents a chat completion response returned by model, based on the provided
 * input.
 */
export interface ChatCompletion {
    /**
     * A unique identifier for the chat completion.
     */
    id: string;
    /**
     * A list of chat completion choices. Can be more than one if `n` is greater
     * than 1.
     */
    choices: Array<ChatCompletion.Choice>;
    /**
     * The Unix timestamp (in seconds) of when the chat completion was created.
     */
    created: number;
    /**
     * The model used for the chat completion.
     */
    model: string;
    /**
     * The object type, which is always `chat.completion`.
     */
    object: 'chat.completion';
    /**
     * This fingerprint represents the backend configuration that the model runs with.
     *
     * Can be used in conjunction with the `seed` request parameter to understand when
     * backend changes have been made that might impact determinism.
     */
    system_fingerprint?: string;
    /**
     * Usage statistics for the completion request.
     */
    usage?: CompletionsAPI.CompletionUsage;
    /**
     * Detailed usage breakdown by model when multiple models are used in the request
     * for compound AI systems.
     */
    usage_breakdown?: ChatCompletion.UsageBreakdown;
}
export declare namespace ChatCompletion {
    interface Choice {
        /**
         * The reason the model stopped generating tokens. This will be `stop` if the model
         * hit a natural stop point or a provided stop sequence, `length` if the maximum
         * number of tokens specified in the request was reached, `tool_calls` if the model
         * called a tool, or `function_call` (deprecated) if the model called a function.
         */
        finish_reason: 'stop' | 'length' | 'tool_calls' | 'function_call';
        /**
         * The index of the choice in the list of choices.
         */
        index: number;
        /**
         * Log probability information for the choice.
         */
        logprobs: Choice.Logprobs | null;
        /**
         * A chat completion message generated by the model.
         */
        message: ChatCompletionsAPI.ChatCompletionMessage;
    }
    namespace Choice {
        /**
         * Log probability information for the choice.
         */
        interface Logprobs {
            /**
             * A list of message content tokens with log probability information.
             */
            content: Array<ChatCompletionsAPI.ChatCompletionTokenLogprob> | null;
        }
    }
    /**
     * Detailed usage breakdown by model when multiple models are used in the request
     * for compound AI systems.
     */
    interface UsageBreakdown {
        /**
         * List of models used in the request and their individual usage statistics
         */
        models: Array<UsageBreakdown.Model>;
    }
    namespace UsageBreakdown {
        interface Model {
            /**
             * The name/identifier of the model used
             */
            model: string;
            /**
             * Usage statistics for the completion request.
             */
            usage: CompletionsAPI.CompletionUsage;
        }
    }
}
export interface ChatCompletionAssistantMessageParam {
    /**
     * The role of the messages author, in this case `assistant`.
     */
    role: 'assistant';
    /**
     * The contents of the assistant message. Required unless `tool_calls` or
     * `function_call` is specified.
     */
    content?: string | null;
    /**
     * @deprecated Deprecated and replaced by `tool_calls`. The name and arguments of a
     * function that should be called, as generated by the model.
     */
    function_call?: ChatCompletionAssistantMessageParam.FunctionCall;
    /**
     * An optional name for the participant. Provides the model information to
     * differentiate between participants of the same role.
     */
    name?: string;
    /**
     * The tool calls generated by the model, such as function calls.
     */
    tool_calls?: Array<ChatCompletionMessageToolCall>;
}
export declare namespace ChatCompletionAssistantMessageParam {
    /**
     * @deprecated Deprecated and replaced by `tool_calls`. The name and arguments of a
     * function that should be called, as generated by the model.
     */
    interface FunctionCall {
        /**
         * The arguments to call the function with, as generated by the model in JSON
         * format. Note that the model does not always generate valid JSON, and may
         * hallucinate parameters not defined by your function schema. Validate the
         * arguments in your code before calling your function.
         */
        arguments?: string;
        /**
         * The name of the function to call.
         */
        name?: string;
    }
}
/**
 * Represents a streamed chunk of a chat completion response returned by model,
 * based on the provided input.
 */
export interface ChatCompletionChunk {
    /**
     * A unique identifier for the chat completion. Each chunk has the same ID.
     */
    id: string;
    /**
     * A list of chat completion choices. Can contain more than one elements if `n` is
     * greater than 1.
     */
    choices: Array<ChatCompletionChunk.Choice>;
    /**
     * The Unix timestamp (in seconds) of when the chat completion was created. Each
     * chunk has the same timestamp.
     */
    created: number;
    /**
     * The model to generate the completion.
     */
    model: string;
    /**
     * The object type, which is always `chat.completion.chunk`.
     */
    object: 'chat.completion.chunk';
    /**
     * This fingerprint represents the backend configuration that the model runs with.
     * Can be used in conjunction with the `seed` request parameter to understand when
     * backend changes have been made that might impact determinism.
     */
    system_fingerprint?: string;
    x_groq?: ChatCompletionChunk.XGroq;
}
export declare namespace ChatCompletionChunk {
    interface Choice {
        /**
         * A chat completion delta generated by streamed model responses.
         */
        delta: Choice.Delta;
        /**
         * The reason the model stopped generating tokens. This will be `stop` if the model
         * hit a natural stop point or a provided stop sequence, `length` if the maximum
         * number of tokens specified in the request was reached, `tool_calls` if the model
         * called a tool, or `function_call` (deprecated) if the model called a function.
         */
        finish_reason: 'stop' | 'length' | 'tool_calls' | 'function_call' | null;
        /**
         * The index of the choice in the list of choices.
         */
        index: number;
        /**
         * Log probability information for the choice.
         */
        logprobs?: Choice.Logprobs | null;
    }
    namespace Choice {
        /**
         * A chat completion delta generated by streamed model responses.
         */
        interface Delta {
            /**
             * The contents of the chunk message.
             */
            content?: string | null;
            /**
             * A list of tools that were executed during the chat completion for compound AI
             * systems.
             */
            executed_tools?: Array<Delta.ExecutedTool>;
            /**
             * @deprecated Deprecated and replaced by `tool_calls`. The name and arguments of a
             * function that should be called, as generated by the model.
             */
            function_call?: Delta.FunctionCall;
            /**
             * The model's reasoning for a response. Only available for reasoning models when
             * requests parameter reasoning_format has value `parsed.
             */
            reasoning?: string | null;
            /**
             * The role of the author of this message.
             */
            role?: 'system' | 'user' | 'assistant' | 'tool';
            tool_calls?: Array<Delta.ToolCall>;
        }
        namespace Delta {
            interface ExecutedTool {
                /**
                 * The arguments passed to the tool in JSON format.
                 */
                arguments: string;
                /**
                 * The index of the executed tool.
                 */
                index: number;
                /**
                 * The type of tool that was executed.
                 */
                type: string;
                /**
                 * Array of code execution results
                 */
                code_results?: Array<ExecutedTool.CodeResult>;
                /**
                 * The output returned by the tool.
                 */
                output?: string | null;
                /**
                 * The search results returned by the tool, if applicable.
                 */
                search_results?: ExecutedTool.SearchResults | null;
            }
            namespace ExecutedTool {
                interface CodeResult {
                    chart?: CodeResult.Chart;
                    /**
                     * Array of charts from a superchart
                     */
                    charts?: Array<CodeResult.Chart>;
                    /**
                     * Base64 encoded PNG image output from code execution
                     */
                    png?: string;
                    /**
                     * The text version of the code execution result
                     */
                    text?: string;
                }
                namespace CodeResult {
                    interface Chart {
                        /**
                         * The chart elements (data series, points, etc.)
                         */
                        elements: Array<Chart.Element>;
                        /**
                         * The type of chart
                         */
                        type: 'bar' | 'box_and_whisker' | 'line' | 'pie' | 'scatter' | 'superchart' | 'unknown';
                        /**
                         * The title of the chart
                         */
                        title?: string;
                        /**
                         * The label for the x-axis
                         */
                        x_label?: string;
                        /**
                         * The scale type for the x-axis
                         */
                        x_scale?: string;
                        /**
                         * The labels for the x-axis ticks
                         */
                        x_tick_labels?: Array<string>;
                        /**
                         * The tick values for the x-axis
                         */
                        x_ticks?: Array<number>;
                        /**
                         * The unit for the x-axis
                         */
                        x_unit?: string;
                        /**
                         * The label for the y-axis
                         */
                        y_label?: string;
                        /**
                         * The scale type for the y-axis
                         */
                        y_scale?: string;
                        /**
                         * The labels for the y-axis ticks
                         */
                        y_tick_labels?: Array<string>;
                        /**
                         * The tick values for the y-axis
                         */
                        y_ticks?: Array<number>;
                        /**
                         * The unit for the y-axis
                         */
                        y_unit?: string;
                    }
                    namespace Chart {
                        interface Element {
                            /**
                             * The label for this chart element
                             */
                            label: string;
                            /**
                             * The angle for this element
                             */
                            angle?: number;
                            /**
                             * The group this element belongs to
                             */
                            group?: string;
                            /**
                             * The points for this element
                             */
                            points?: Array<Array<number>>;
                            /**
                             * The radius for this element
                             */
                            radius?: number;
                            /**
                             * The value for this element
                             */
                            value?: number;
                        }
                    }
                    interface Chart {
                        /**
                         * The chart elements (data series, points, etc.)
                         */
                        elements: Array<Chart.Element>;
                        /**
                         * The type of chart
                         */
                        type: 'bar' | 'box_and_whisker' | 'line' | 'pie' | 'scatter' | 'superchart' | 'unknown';
                        /**
                         * The title of the chart
                         */
                        title?: string;
                        /**
                         * The label for the x-axis
                         */
                        x_label?: string;
                        /**
                         * The scale type for the x-axis
                         */
                        x_scale?: string;
                        /**
                         * The labels for the x-axis ticks
                         */
                        x_tick_labels?: Array<string>;
                        /**
                         * The tick values for the x-axis
                         */
                        x_ticks?: Array<number>;
                        /**
                         * The unit for the x-axis
                         */
                        x_unit?: string;
                        /**
                         * The label for the y-axis
                         */
                        y_label?: string;
                        /**
                         * The scale type for the y-axis
                         */
                        y_scale?: string;
                        /**
                         * The labels for the y-axis ticks
                         */
                        y_tick_labels?: Array<string>;
                        /**
                         * The tick values for the y-axis
                         */
                        y_ticks?: Array<number>;
                        /**
                         * The unit for the y-axis
                         */
                        y_unit?: string;
                    }
                    namespace Chart {
                        interface Element {
                            /**
                             * The label for this chart element
                             */
                            label: string;
                            /**
                             * The angle for this element
                             */
                            angle?: number;
                            /**
                             * The group this element belongs to
                             */
                            group?: string;
                            /**
                             * The points for this element
                             */
                            points?: Array<Array<number>>;
                            /**
                             * The radius for this element
                             */
                            radius?: number;
                            /**
                             * The value for this element
                             */
                            value?: number;
                        }
                    }
                }
                /**
                 * The search results returned by the tool, if applicable.
                 */
                interface SearchResults {
                    /**
                     * List of image URLs returned by the search
                     */
                    images?: Array<string>;
                    /**
                     * List of search results
                     */
                    results?: Array<SearchResults.Result>;
                }
                namespace SearchResults {
                    interface Result {
                        /**
                         * The content of the search result
                         */
                        content?: string;
                        /**
                         * The relevance score of the search result
                         */
                        score?: number;
                        /**
                         * The title of the search result
                         */
                        title?: string;
                        /**
                         * The URL of the search result
                         */
                        url?: string;
                    }
                }
            }
            /**
             * @deprecated Deprecated and replaced by `tool_calls`. The name and arguments of a
             * function that should be called, as generated by the model.
             */
            interface FunctionCall {
                /**
                 * The arguments to call the function with, as generated by the model in JSON
                 * format. Note that the model does not always generate valid JSON, and may
                 * hallucinate parameters not defined by your function schema. Validate the
                 * arguments in your code before calling your function.
                 */
                arguments?: string;
                /**
                 * The name of the function to call.
                 */
                name?: string;
            }
            interface ToolCall {
                index: number;
                /**
                 * The ID of the tool call.
                 */
                id?: string;
                function?: ToolCall.Function;
                /**
                 * The type of the tool. Currently, only `function` is supported.
                 */
                type?: 'function';
            }
            namespace ToolCall {
                interface Function {
                    /**
                     * The arguments to call the function with, as generated by the model in JSON
                     * format. Note that the model does not always generate valid JSON, and may
                     * hallucinate parameters not defined by your function schema. Validate the
                     * arguments in your code before calling your function.
                     */
                    arguments?: string;
                    /**
                     * The name of the function to call.
                     */
                    name?: string;
                }
            }
        }
        /**
         * Log probability information for the choice.
         */
        interface Logprobs {
            /**
             * A list of message content tokens with log probability information.
             */
            content: Array<ChatCompletionsAPI.ChatCompletionTokenLogprob> | null;
        }
    }
    interface XGroq {
        /**
         * A groq request ID which can be used by to refer to a specific request to groq
         * support Only sent with the first chunk
         */
        id?: string;
        /**
         * An error string indicating why a stream was stopped early
         */
        error?: string;
        /**
         * Usage information for the stream. Only sent in the final chunk
         */
        usage?: CompletionsAPI.CompletionUsage;
        /**
         * Detailed usage breakdown by model when multiple models are used in the request
         * for compound AI systems. Only sent in the final chunk
         */
        usage_breakdown?: XGroq.UsageBreakdown;
    }
    namespace XGroq {
        /**
         * Detailed usage breakdown by model when multiple models are used in the request
         * for compound AI systems. Only sent in the final chunk
         */
        interface UsageBreakdown {
            /**
             * List of models used in the request and their individual usage statistics
             */
            models: Array<UsageBreakdown.Model>;
        }
        namespace UsageBreakdown {
            interface Model {
                /**
                 * The name/identifier of the model used
                 */
                model: string;
                /**
                 * Usage statistics for the completion request.
                 */
                usage: CompletionsAPI.CompletionUsage;
            }
        }
    }
}
export type ChatCompletionContentPart = ChatCompletionContentPartText | ChatCompletionContentPartImage;
export interface ChatCompletionContentPartImage {
    image_url: ChatCompletionContentPartImage.ImageURL;
    /**
     * The type of the content part.
     */
    type: 'image_url';
}
export declare namespace ChatCompletionContentPartImage {
    interface ImageURL {
        /**
         * Either a URL of the image or the base64 encoded image data.
         */
        url: string;
        /**
         * Specifies the detail level of the image.
         */
        detail?: 'auto' | 'low' | 'high';
    }
}
export interface ChatCompletionContentPartText {
    /**
     * The text content.
     */
    text: string;
    /**
     * The type of the content part.
     */
    type: 'text';
}
/**
 * Specifying a particular function via `{"name": "my_function"}` forces the model
 * to call that function.
 */
export interface ChatCompletionFunctionCallOption {
    /**
     * The name of the function to call.
     */
    name: string;
}
/**
 * @deprecated
 */
export interface ChatCompletionFunctionMessageParam {
    /**
     * The contents of the function message.
     */
    content: string | null;
    /**
     * The name of the function to call.
     */
    name: string;
    /**
     * The role of the messages author, in this case `function`.
     */
    role: 'function';
}
/**
 * A chat completion message generated by the model.
 */
export interface ChatCompletionMessage {
    /**
     * The contents of the message.
     */
    content: string | null;
    /**
     * The role of the author of this message.
     */
    role: 'assistant';
    /**
     * A list of tools that were executed during the chat completion for compound AI
     * systems.
     */
    executed_tools?: Array<ChatCompletionMessage.ExecutedTool>;
    /**
     * @deprecated Deprecated and replaced by `tool_calls`. The name and arguments of a
     * function that should be called, as generated by the model.
     */
    function_call?: ChatCompletionMessage.FunctionCall;
    /**
     * The model's reasoning for a response. Only available for reasoning models when
     * requests parameter reasoning_format has value `parsed.
     */
    reasoning?: string | null;
    /**
     * The tool calls generated by the model, such as function calls.
     */
    tool_calls?: Array<ChatCompletionMessageToolCall>;
}
export declare namespace ChatCompletionMessage {
    interface ExecutedTool {
        /**
         * The arguments passed to the tool in JSON format.
         */
        arguments: string;
        /**
         * The index of the executed tool.
         */
        index: number;
        /**
         * The type of tool that was executed.
         */
        type: string;
        /**
         * Array of code execution results
         */
        code_results?: Array<ExecutedTool.CodeResult>;
        /**
         * The output returned by the tool.
         */
        output?: string | null;
        /**
         * The search results returned by the tool, if applicable.
         */
        search_results?: ExecutedTool.SearchResults | null;
    }
    namespace ExecutedTool {
        interface CodeResult {
            chart?: CodeResult.Chart;
            /**
             * Array of charts from a superchart
             */
            charts?: Array<CodeResult.Chart>;
            /**
             * Base64 encoded PNG image output from code execution
             */
            png?: string;
            /**
             * The text version of the code execution result
             */
            text?: string;
        }
        namespace CodeResult {
            interface Chart {
                /**
                 * The chart elements (data series, points, etc.)
                 */
                elements: Array<Chart.Element>;
                /**
                 * The type of chart
                 */
                type: 'bar' | 'box_and_whisker' | 'line' | 'pie' | 'scatter' | 'superchart' | 'unknown';
                /**
                 * The title of the chart
                 */
                title?: string;
                /**
                 * The label for the x-axis
                 */
                x_label?: string;
                /**
                 * The scale type for the x-axis
                 */
                x_scale?: string;
                /**
                 * The labels for the x-axis ticks
                 */
                x_tick_labels?: Array<string>;
                /**
                 * The tick values for the x-axis
                 */
                x_ticks?: Array<number>;
                /**
                 * The unit for the x-axis
                 */
                x_unit?: string;
                /**
                 * The label for the y-axis
                 */
                y_label?: string;
                /**
                 * The scale type for the y-axis
                 */
                y_scale?: string;
                /**
                 * The labels for the y-axis ticks
                 */
                y_tick_labels?: Array<string>;
                /**
                 * The tick values for the y-axis
                 */
                y_ticks?: Array<number>;
                /**
                 * The unit for the y-axis
                 */
                y_unit?: string;
            }
            namespace Chart {
                interface Element {
                    /**
                     * The label for this chart element
                     */
                    label: string;
                    /**
                     * The angle for this element
                     */
                    angle?: number;
                    /**
                     * The group this element belongs to
                     */
                    group?: string;
                    /**
                     * The points for this element
                     */
                    points?: Array<Array<number>>;
                    /**
                     * The radius for this element
                     */
                    radius?: number;
                    /**
                     * The value for this element
                     */
                    value?: number;
                }
            }
            interface Chart {
                /**
                 * The chart elements (data series, points, etc.)
                 */
                elements: Array<Chart.Element>;
                /**
                 * The type of chart
                 */
                type: 'bar' | 'box_and_whisker' | 'line' | 'pie' | 'scatter' | 'superchart' | 'unknown';
                /**
                 * The title of the chart
                 */
                title?: string;
                /**
                 * The label for the x-axis
                 */
                x_label?: string;
                /**
                 * The scale type for the x-axis
                 */
                x_scale?: string;
                /**
                 * The labels for the x-axis ticks
                 */
                x_tick_labels?: Array<string>;
                /**
                 * The tick values for the x-axis
                 */
                x_ticks?: Array<number>;
                /**
                 * The unit for the x-axis
                 */
                x_unit?: string;
                /**
                 * The label for the y-axis
                 */
                y_label?: string;
                /**
                 * The scale type for the y-axis
                 */
                y_scale?: string;
                /**
                 * The labels for the y-axis ticks
                 */
                y_tick_labels?: Array<string>;
                /**
                 * The tick values for the y-axis
                 */
                y_ticks?: Array<number>;
                /**
                 * The unit for the y-axis
                 */
                y_unit?: string;
            }
            namespace Chart {
                interface Element {
                    /**
                     * The label for this chart element
                     */
                    label: string;
                    /**
                     * The angle for this element
                     */
                    angle?: number;
                    /**
                     * The group this element belongs to
                     */
                    group?: string;
                    /**
                     * The points for this element
                     */
                    points?: Array<Array<number>>;
                    /**
                     * The radius for this element
                     */
                    radius?: number;
                    /**
                     * The value for this element
                     */
                    value?: number;
                }
            }
        }
        /**
         * The search results returned by the tool, if applicable.
         */
        interface SearchResults {
            /**
             * List of image URLs returned by the search
             */
            images?: Array<string>;
            /**
             * List of search results
             */
            results?: Array<SearchResults.Result>;
        }
        namespace SearchResults {
            interface Result {
                /**
                 * The content of the search result
                 */
                content?: string;
                /**
                 * The relevance score of the search result
                 */
                score?: number;
                /**
                 * The title of the search result
                 */
                title?: string;
                /**
                 * The URL of the search result
                 */
                url?: string;
            }
        }
    }
    /**
     * @deprecated Deprecated and replaced by `tool_calls`. The name and arguments of a
     * function that should be called, as generated by the model.
     */
    interface FunctionCall {
        /**
         * The arguments to call the function with, as generated by the model in JSON
         * format. Note that the model does not always generate valid JSON, and may
         * hallucinate parameters not defined by your function schema. Validate the
         * arguments in your code before calling your function.
         */
        arguments: string;
        /**
         * The name of the function to call.
         */
        name: string;
    }
}
export type ChatCompletionMessageParam = ChatCompletionSystemMessageParam | ChatCompletionUserMessageParam | ChatCompletionAssistantMessageParam | ChatCompletionToolMessageParam | ChatCompletionFunctionMessageParam;
export interface ChatCompletionMessageToolCall {
    /**
     * The ID of the tool call.
     */
    id: string;
    /**
     * The function that the model called.
     */
    function: ChatCompletionMessageToolCall.Function;
    /**
     * The type of the tool. Currently, only `function` is supported.
     */
    type: 'function';
}
export declare namespace ChatCompletionMessageToolCall {
    /**
     * The function that the model called.
     */
    interface Function {
        /**
         * The arguments to call the function with, as generated by the model in JSON
         * format. Note that the model does not always generate valid JSON, and may
         * hallucinate parameters not defined by your function schema. Validate the
         * arguments in your code before calling your function.
         */
        arguments: string;
        /**
         * The name of the function to call.
         */
        name: string;
    }
}
/**
 * Specifies a tool the model should use. Use to force the model to call a specific
 * function.
 */
export interface ChatCompletionNamedToolChoice {
    function: ChatCompletionNamedToolChoice.Function;
    /**
     * The type of the tool. Currently, only `function` is supported.
     */
    type: 'function';
}
export declare namespace ChatCompletionNamedToolChoice {
    interface Function {
        /**
         * The name of the function to call.
         */
        name: string;
    }
}
/**
 * The role of the author of a message
 */
export type ChatCompletionRole = 'system' | 'user' | 'assistant' | 'tool' | 'function';
export interface ChatCompletionSystemMessageParam {
    /**
     * The contents of the system message.
     */
    content: string;
    /**
     * The role of the messages author, in this case `system`.
     */
    role: 'system';
    /**
     * An optional name for the participant. Provides the model information to
     * differentiate between participants of the same role.
     */
    name?: string;
}
export interface ChatCompletionTokenLogprob {
    /**
     * The token.
     */
    token: string;
    /**
     * A list of integers representing the UTF-8 bytes representation of the token.
     * Useful in instances where characters are represented by multiple tokens and
     * their byte representations must be combined to generate the correct text
     * representation. Can be `null` if there is no bytes representation for the token.
     */
    bytes: Array<number> | null;
    /**
     * The log probability of this token, if it is within the top 20 most likely
     * tokens. Otherwise, the value `-9999.0` is used to signify that the token is very
     * unlikely.
     */
    logprob: number;
    /**
     * List of the most likely tokens and their log probability, at this token
     * position. In rare cases, there may be fewer than the number of requested
     * `top_logprobs` returned.
     */
    top_logprobs: Array<ChatCompletionTokenLogprob.TopLogprob>;
}
export declare namespace ChatCompletionTokenLogprob {
    interface TopLogprob {
        /**
         * The token.
         */
        token: string;
        /**
         * A list of integers representing the UTF-8 bytes representation of the token.
         * Useful in instances where characters are represented by multiple tokens and
         * their byte representations must be combined to generate the correct text
         * representation. Can be `null` if there is no bytes representation for the token.
         */
        bytes: Array<number> | null;
        /**
         * The log probability of this token, if it is within the top 20 most likely
         * tokens. Otherwise, the value `-9999.0` is used to signify that the token is very
         * unlikely.
         */
        logprob: number;
    }
}
export interface ChatCompletionTool {
    function: Shared.FunctionDefinition;
    /**
     * The type of the tool. Currently, only `function` is supported.
     */
    type: 'function';
}
/**
 * Controls which (if any) tool is called by the model. `none` means the model will
 * not call any tool and instead generates a message. `auto` means the model can
 * pick between generating a message or calling one or more tools. `required` means
 * the model must call one or more tools. Specifying a particular tool via
 * `{"type": "function", "function": {"name": "my_function"}}` forces the model to
 * call that tool.
 *
 * `none` is the default when no tools are present. `auto` is the default if tools
 * are present.
 */
export type ChatCompletionToolChoiceOption = 'none' | 'auto' | 'required' | ChatCompletionNamedToolChoice;
export interface ChatCompletionToolMessageParam {
    /**
     * The contents of the tool message.
     */
    content: string;
    /**
     * The role of the messages author, in this case `tool`.
     */
    role: 'tool';
    /**
     * Tool call that this message is responding to.
     */
    tool_call_id: string;
}
export interface ChatCompletionUserMessageParam {
    /**
     * The contents of the user message.
     */
    content: string | Array<ChatCompletionContentPart>;
    /**
     * The role of the messages author, in this case `user`.
     */
    role: 'user';
    /**
     * An optional name for the participant. Provides the model information to
     * differentiate between participants of the same role.
     */
    name?: string;
}
export type ChatCompletionCreateParams = ChatCompletionCreateParamsNonStreaming | ChatCompletionCreateParamsStreaming;
export interface ChatCompletionCreateParamsBase {
    /**
     * A list of messages comprising the conversation so far.
     */
    messages: Array<ChatCompletionMessageParam>;
    /**
     * ID of the model to use. For details on which models are compatible with the Chat
     * API, see available [models](https://console.groq.com/docs/models)
     */
    model: (string & {}) | 'gemma2-9b-it' | 'llama-3.3-70b-versatile' | 'llama-3.1-8b-instant' | 'llama-guard-3-8b' | 'llama3-70b-8192' | 'llama3-8b-8192';
    /**
     * @deprecated Deprecated: Use search_settings.exclude_domains instead. A list of
     * domains to exclude from the search results when the model uses a web search
     * tool.
     */
    exclude_domains?: Array<string> | null;
    /**
     * Number between -2.0 and 2.0. Positive values penalize new tokens based on their
     * existing frequency in the text so far, decreasing the model's likelihood to
     * repeat the same line verbatim.
     */
    frequency_penalty?: number | null;
    /**
     * @deprecated Deprecated in favor of `tool_choice`.
     *
     * Controls which (if any) function is called by the model. `none` means the model
     * will not call a function and instead generates a message. `auto` means the model
     * can pick between generating a message or calling a function. Specifying a
     * particular function via `{"name": "my_function"}` forces the model to call that
     * function.
     *
     * `none` is the default when no functions are present. `auto` is the default if
     * functions are present.
     */
    function_call?: 'none' | 'auto' | 'required' | ChatCompletionFunctionCallOption | null;
    /**
     * @deprecated Deprecated in favor of `tools`.
     *
     * A list of functions the model may generate JSON inputs for.
     */
    functions?: Array<CompletionCreateParams.Function> | null;
    /**
     * @deprecated Deprecated: Use search_settings.include_domains instead. A list of
     * domains to include in the search results when the model uses a web search tool.
     */
    include_domains?: Array<string> | null;
    /**
     * This is not yet supported by any of our models. Modify the likelihood of
     * specified tokens appearing in the completion.
     */
    logit_bias?: Record<string, number> | null;
    /**
     * This is not yet supported by any of our models. Whether to return log
     * probabilities of the output tokens or not. If true, returns the log
     * probabilities of each output token returned in the `content` of `message`.
     */
    logprobs?: boolean | null;
    /**
     * The maximum number of tokens that can be generated in the chat completion. The
     * total length of input tokens and generated tokens is limited by the model's
     * context length.
     */
    max_completion_tokens?: number | null;
    /**
     * @deprecated Deprecated in favor of `max_completion_tokens`. The maximum number
     * of tokens that can be generated in the chat completion. The total length of
     * input tokens and generated tokens is limited by the model's context length.
     */
    max_tokens?: number | null;
    /**
     * This parameter is not currently supported.
     */
    metadata?: Record<string, string> | null;
    /**
     * How many chat completion choices to generate for each input message. Note that
     * the current moment, only n=1 is supported. Other values will result in a 400
     * response.
     */
    n?: number | null;
    /**
     * Whether to enable parallel function calling during tool use.
     */
    parallel_tool_calls?: boolean | null;
    /**
     * Number between -2.0 and 2.0. Positive values penalize new tokens based on
     * whether they appear in the text so far, increasing the model's likelihood to
     * talk about new topics.
     */
    presence_penalty?: number | null;
    /**
     * Specifies how to output reasoning tokens
     */
    reasoning_format?: 'hidden' | 'raw' | 'parsed' | null;
    /**
     * An object specifying the format that the model must output. Setting to
     * `{ "type": "json_schema", "json_schema": {...} }` enables Structured Outputs
     * which ensures the model will match your supplied JSON schema. json_schema
     * response format is only supported on llama 4 models. Setting to
     * `{ "type": "json_object" }` enables the older JSON mode, which ensures the
     * message the model generates is valid JSON. Using `json_schema` is preferred for
     * models that support it.
     */
    response_format?: CompletionCreateParams.ResponseFormatText | CompletionCreateParams.ResponseFormatJsonSchema | CompletionCreateParams.ResponseFormatJsonObject | null;
    /**
     * Settings for web search functionality when the model uses a web search tool.
     */
    search_settings?: CompletionCreateParams.SearchSettings | null;
    /**
     * If specified, our system will make a best effort to sample deterministically,
     * such that repeated requests with the same `seed` and parameters should return
     * the same result. Determinism is not guaranteed, and you should refer to the
     * `system_fingerprint` response parameter to monitor changes in the backend.
     */
    seed?: number | null;
    /**
     * The service tier to use for the request. Defaults to `on_demand`.
     *
     * - `auto` will automatically select the highest tier available within the rate
     *   limits of your organization.
     * - `flex` uses the flex tier, which will succeed or fail quickly.
     */
    service_tier?: 'auto' | 'on_demand' | 'flex' | null;
    /**
     * Up to 4 sequences where the API will stop generating further tokens. The
     * returned text will not contain the stop sequence.
     */
    stop?: string | null | Array<string>;
    /**
     * This parameter is not currently supported.
     */
    store?: boolean | null;
    /**
     * If set, partial message deltas will be sent. Tokens will be sent as data-only
     * [server-sent events](https://developer.mozilla.org/en-US/docs/Web/API/Server-sent_events/Using_server-sent_events#Event_stream_format)
     * as they become available, with the stream terminated by a `data: [DONE]`
     * message. [Example code](/docs/text-chat#streaming-a-chat-completion).
     */
    stream?: boolean | null;
    /**
     * What sampling temperature to use, between 0 and 2. Higher values like 0.8 will
     * make the output more random, while lower values like 0.2 will make it more
     * focused and deterministic. We generally recommend altering this or top_p but not
     * both.
     */
    temperature?: number | null;
    /**
     * Controls which (if any) tool is called by the model. `none` means the model will
     * not call any tool and instead generates a message. `auto` means the model can
     * pick between generating a message or calling one or more tools. `required` means
     * the model must call one or more tools. Specifying a particular tool via
     * `{"type": "function", "function": {"name": "my_function"}}` forces the model to
     * call that tool.
     *
     * `none` is the default when no tools are present. `auto` is the default if tools
     * are present.
     */
    tool_choice?: ChatCompletionToolChoiceOption | null;
    /**
     * A list of tools the model may call. Currently, only functions are supported as a
     * tool. Use this to provide a list of functions the model may generate JSON inputs
     * for. A max of 128 functions are supported.
     */
    tools?: Array<ChatCompletionTool> | null;
    /**
     * This is not yet supported by any of our models. An integer between 0 and 20
     * specifying the number of most likely tokens to return at each token position,
     * each with an associated log probability. `logprobs` must be set to `true` if
     * this parameter is used.
     */
    top_logprobs?: number | null;
    /**
     * An alternative to sampling with temperature, called nucleus sampling, where the
     * model considers the results of the tokens with top_p probability mass. So 0.1
     * means only the tokens comprising the top 10% probability mass are considered. We
     * generally recommend altering this or temperature but not both.
     */
    top_p?: number | null;
    /**
     * A unique identifier representing your end-user, which can help us monitor and
     * detect abuse.
     */
    user?: string | null;
}
export declare namespace CompletionCreateParams {
    /**
     * @deprecated
     */
    interface Function {
        /**
         * The name of the function to be called. Must be a-z, A-Z, 0-9, or contain
         * underscores and dashes, with a maximum length of 64.
         */
        name: string;
        /**
         * A description of what the function does, used by the model to choose when and
         * how to call the function.
         */
        description?: string;
        /**
         * The parameters the functions accepts, described as a JSON Schema object. See the
         * docs on [tool use](/docs/tool-use) for examples, and the
         * [JSON Schema reference](https://json-schema.org/understanding-json-schema/) for
         * documentation about the format.
         *
         * Omitting `parameters` defines a function with an empty parameter list.
         */
        parameters?: Shared.FunctionParameters;
    }
    /**
     * Default response format. Used to generate text responses.
     */
    interface ResponseFormatText {
        /**
         * The type of response format being defined. Always `text`.
         */
        type: 'text';
    }
    /**
     * JSON Schema response format. Used to generate structured JSON responses.
     */
    interface ResponseFormatJsonSchema {
        /**
         * Structured Outputs configuration options, including a JSON Schema.
         */
        json_schema: ResponseFormatJsonSchema.JsonSchema;
        /**
         * The type of response format being defined. Always `json_schema`.
         */
        type: 'json_schema';
    }
    namespace ResponseFormatJsonSchema {
        /**
         * Structured Outputs configuration options, including a JSON Schema.
         */
        interface JsonSchema {
            /**
             * The name of the response format. Must be a-z, A-Z, 0-9, or contain underscores
             * and dashes, with a maximum length of 64.
             */
            name: string;
            /**
             * A description of what the response format is for, used by the model to determine
             * how to respond in the format.
             */
            description?: string;
            /**
             * The schema for the response format, described as a JSON Schema object. Learn how
             * to build JSON schemas [here](https://json-schema.org/).
             */
            schema?: Record<string, unknown>;
            /**
             * Whether to enable strict schema adherence when generating the output. If set to
             * true, the model will always follow the exact schema defined in the `schema`
             * field. Only a subset of JSON Schema is supported when `strict` is `true`.
             */
            strict?: boolean | null;
        }
    }
    /**
     * JSON object response format. An older method of generating JSON responses. Using
     * `json_schema` is recommended for models that support it. Note that the model
     * will not generate JSON without a system or user message instructing it to do so.
     */
    interface ResponseFormatJsonObject {
        /**
         * The type of response format being defined. Always `json_object`.
         */
        type: 'json_object';
    }
    /**
     * Settings for web search functionality when the model uses a web search tool.
     */
    interface SearchSettings {
        /**
         * A list of domains to exclude from the search results.
         */
        exclude_domains?: Array<string> | null;
        /**
         * A list of domains to include in the search results.
         */
        include_domains?: Array<string> | null;
        /**
         * Whether to include images in the search results.
         */
        include_images?: boolean | null;
    }
}
export interface ChatCompletionCreateParamsNonStreaming extends ChatCompletionCreateParamsBase {
    /**
     * If set, partial message deltas will be sent. Tokens will be sent as data-only
     * [server-sent events](https://developer.mozilla.org/en-US/docs/Web/API/Server-sent_events/Using_server-sent_events#Event_stream_format)
     * as they become available, with the stream terminated by a `data: [DONE]`
     * message. [Example code](/docs/text-chat#streaming-a-chat-completion).
     */
    stream?: false | null;
}
export interface ChatCompletionCreateParamsStreaming extends ChatCompletionCreateParamsBase {
    /**
     * If set, partial message deltas will be sent. Tokens will be sent as data-only
     * [server-sent events](https://developer.mozilla.org/en-US/docs/Web/API/Server-sent_events/Using_server-sent_events#Event_stream_format)
     * as they become available, with the stream terminated by a `data: [DONE]`
     * message. [Example code](/docs/text-chat#streaming-a-chat-completion).
     */
    stream: true;
}
export declare namespace Completions {
    export { type ChatCompletion as ChatCompletion, type ChatCompletionAssistantMessageParam as ChatCompletionAssistantMessageParam, type ChatCompletionChunk as ChatCompletionChunk, type ChatCompletionContentPart as ChatCompletionContentPart, type ChatCompletionContentPartImage as ChatCompletionContentPartImage, type ChatCompletionContentPartText as ChatCompletionContentPartText, type ChatCompletionFunctionCallOption as ChatCompletionFunctionCallOption, type ChatCompletionFunctionMessageParam as ChatCompletionFunctionMessageParam, type ChatCompletionMessage as ChatCompletionMessage, type ChatCompletionMessageParam as ChatCompletionMessageParam, type ChatCompletionMessageToolCall as ChatCompletionMessageToolCall, type ChatCompletionNamedToolChoice as ChatCompletionNamedToolChoice, type ChatCompletionRole as ChatCompletionRole, type ChatCompletionSystemMessageParam as ChatCompletionSystemMessageParam, type ChatCompletionTokenLogprob as ChatCompletionTokenLogprob, type ChatCompletionTool as ChatCompletionTool, type ChatCompletionToolChoiceOption as ChatCompletionToolChoiceOption, type ChatCompletionToolMessageParam as ChatCompletionToolMessageParam, type ChatCompletionUserMessageParam as ChatCompletionUserMessageParam, type CompletionCreateParams as CompletionCreateParams, };
}
//# sourceMappingURL=completions.d.ts.map