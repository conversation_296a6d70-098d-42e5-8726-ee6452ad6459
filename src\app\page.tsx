import Link from "next/link";
import { FileText, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-gray-900">VistaNotes</h1>
                <p className="text-sm text-gray-600">Creative Arts Therapy Documentation</p>
              </div>
            </div>
            <nav className="hidden md:flex space-x-8">
              <Link href="/dashboard" className="text-gray-500 hover:text-gray-900">
                Dashboard
              </Link>
              <Link href="/clients" className="text-gray-500 hover:text-gray-900">
                Clients
              </Link>
              <Link href="/notes" className="text-gray-500 hover:text-gray-900">
                Session Notes
              </Link>
              <Link href="/auth/signin" className="text-indigo-600 hover:text-indigo-700 font-medium">
                Sign In
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Streamline Your Creative Arts Therapy Documentation
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            AI-powered tools to help Creative Arts Therapists efficiently translate
            non-verbal therapeutic data into comprehensive clinical documentation.
          </p>
          <Link
            href="/dashboard"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 transition-colors"
          >
            Get Started
          </Link>
        </div>

        {/* Feature Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <FileText className="h-12 w-12 text-indigo-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              AI-Powered Notes
            </h3>
            <p className="text-gray-600">
              Generate clinical descriptions and interpretations from multimedia content
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <Users className="h-12 w-12 text-indigo-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Client Management
            </h3>
            <p className="text-gray-600">
              Organize and track client information with secure, HIPAA-compliant storage
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <BarChart3 className="h-12 w-12 text-indigo-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Progress Tracking
            </h3>
            <p className="text-gray-600">
              Visualize client progress with custom metrics and longitudinal data
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <Settings className="h-12 w-12 text-indigo-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              EHR Integration
            </h3>
            <p className="text-gray-600">
              Seamlessly sync with existing Electronic Health Record systems
            </p>
          </div>
        </div>

        {/* MVP Features */}
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            MVP Features Available Now
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-green-100 rounded-full p-3 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <span className="text-2xl">📸</span>
              </div>
              <h4 className="text-lg font-semibold mb-2">Multimedia Capture</h4>
              <p className="text-gray-600">
                Upload and associate photos, videos, and audio recordings with session notes
              </p>
            </div>

            <div className="text-center">
              <div className="bg-blue-100 rounded-full p-3 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <span className="text-2xl">🤖</span>
              </div>
              <h4 className="text-lg font-semibold mb-2">AI Text Generation</h4>
              <p className="text-gray-600">
                Get AI-powered suggestions for clinical descriptions and interpretations
              </p>
            </div>

            <div className="text-center">
              <div className="bg-purple-100 rounded-full p-3 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <span className="text-2xl">📝</span>
              </div>
              <h4 className="text-lg font-semibold mb-2">SOAP Notes</h4>
              <p className="text-gray-600">
                Create structured session notes with integrated multimedia and AI assistance
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
