"use strict";var K=Object.defineProperty;var o=(s,e)=>K(s,"name",{value:e,configurable:!0});var Y=require("./get-pipe-path-BoR10qr8.cjs"),p=require("node:module"),d=require("node:path"),O=require("node:url"),b=require("get-tsconfig"),R=require("node:fs"),S=require("./index-gckBtVBf.cjs"),W=require("./client-D6NvIMSC.cjs"),V=require("node:util"),g=require("./index-BWFBUo6r.cjs");const D=o(s=>{if(!s.startsWith("data:text/javascript,"))return;const e=s.indexOf("?");if(e===-1)return;const n=new URLSearchParams(s.slice(e+1)).get("filePath");if(n)return n},"getOriginalFilePath"),C=o(s=>{const e=D(s);return e&&(p._cache[e]=p._cache[s],delete p._cache[s],s=e),s},"interopCjsExports"),Z=o(s=>{const e=s.indexOf(":");if(e!==-1)return s.slice(0,e)},"getScheme"),J=o(s=>s[0]==="."&&(s[1]==="/"||s[1]==="."||s[2]==="/"),"isRelativePath"),j=o(s=>J(s)||d.isAbsolute(s),"isFilePath"),q=o(s=>{if(j(s))return!0;const e=Z(s);return e&&e!=="node"},"requestAcceptsQuery"),v="file://",N=/\.([cm]?ts|[tj]sx)($|\?)/,ee=/[/\\].+\.(?:cts|cjs)(?:$|\?)/,se=/\.json($|\?)/,_=/\/(?:$|\?)/,te=/^(?:@[^/]+\/)?[^/\\]+$/,w=`${d.sep}node_modules${d.sep}`;exports.fileMatcher=void 0,exports.tsconfigPathsMatcher=void 0,exports.allowJs=!1;const Q=o(s=>{let e=null;if(s){const r=d.resolve(s);e={path:r,config:b.parseTsconfig(r)}}else{try{e=b.getTsconfig()}catch{}if(!e)return}exports.fileMatcher=b.createFilesMatcher(e),exports.tsconfigPathsMatcher=b.createPathsMatcher(e),exports.allowJs=e?.config.compilerOptions?.allowJs??!1},"loadTsconfig"),T=o(s=>Array.from(s).length>0?`?${s.toString()}`:"","urlSearchParamsStringify"),ne=`
//# sourceMappingURL=data:application/json;base64,`,A=o(()=>process.sourceMapsEnabled??!0,"shouldApplySourceMap"),F=o(({code:s,map:e})=>s+ne+Buffer.from(JSON.stringify(e),"utf8").toString("base64"),"inlineSourceMap"),$=process.env.TSX_DEBUG;$&&(g.options.enabled=!0,g.options.supportLevel=3);const I=o(s=>(...e)=>{if(!$)return;const r=`${g.bgGray(` tsx P${process.pid} `)} ${s}`,n=e.map(t=>typeof t=="string"?t:V.inspect(t,{colors:!0})).join(" ");R.writeSync(1,`${r} ${n}
`)},"createLog"),x=I(g.bgLightYellow(g.black(" CJS "))),re=I(g.bgBlue(" ESM ")),ae=[".cts",".mts",".ts",".tsx",".jsx"],oe=[".js",".cjs",".mjs"],k=[".ts",".tsx",".jsx"],U=o((s,e,r,n)=>{const t=Object.getOwnPropertyDescriptor(s,e);t?.set?s[e]=r:(!t||t.configurable)&&Object.defineProperty(s,e,{value:r,enumerable:t?.enumerable||n?.enumerable,writable:n?.writable??(t?t.writable:!0),configurable:n?.configurable??(t?t.configurable:!0)})},"safeSet"),ce=o((s,e,r)=>{const n=e[".js"],t=o((a,c)=>{if(s.enabled===!1)return n(a,c);const[i,f]=c.split("?");if((new URLSearchParams(f).get("namespace")??void 0)!==r)return n(a,c);x("load",{filePath:c}),a.id.startsWith("data:text/javascript,")&&(a.path=d.dirname(i)),W.parent?.send&&W.parent.send({type:"dependency",path:i});const u=ae.some(h=>i.endsWith(h)),P=oe.some(h=>i.endsWith(h));if(!u&&!P)return n(a,i);let m=R.readFileSync(i,"utf8");if(i.endsWith(".cjs")){const h=S.transformDynamicImport(c,m);h&&(m=A()?F(h):h.code)}else if(u||S.isESM(m)){const h=S.transformSync(m,c,{tsconfigRaw:exports.fileMatcher?.(i)});m=A()?F(h):h.code}x("loaded",{filePath:i}),a._compile(m,i)},"transformer");U(e,".js",t);for(const a of k)U(e,a,t,{enumerable:!r,writable:!0,configurable:!0});return U(e,".mjs",t,{writable:!0,configurable:!0}),()=>{e[".js"]===t&&(e[".js"]=n);for(const a of[...k,".mjs"])e[a]===t&&delete e[a]}},"createExtensions"),ie=o(s=>e=>{if((e==="."||e===".."||e.endsWith("/.."))&&(e+="/"),_.test(e)){let r=d.join(e,"index.js");e.startsWith("./")&&(r=`./${r}`);try{return s(r)}catch{}}try{return s(e)}catch(r){const n=r;if(n.code==="MODULE_NOT_FOUND")try{return s(`${e}${d.sep}index.js`)}catch{}throw n}},"createImplicitResolver"),B=[".js",".json"],G=[".ts",".tsx",".jsx"],le=[...G,...B],fe=[...B,...G],y=Object.create(null);y[".js"]=[".ts",".tsx",".js",".jsx"],y[".jsx"]=[".tsx",".ts",".jsx",".js"],y[".cjs"]=[".cts"],y[".mjs"]=[".mts"];const X=o(s=>{const e=s.split("?"),r=e[1]?`?${e[1]}`:"",[n]=e,t=d.extname(n),a=[],c=y[t];if(c){const f=n.slice(0,-t.length);a.push(...c.map(l=>f+l+r))}const i=!(s.startsWith(v)||j(n))||n.includes(w)||n.includes("/node_modules/")?fe:le;return a.push(...i.map(f=>n+f+r)),a},"mapTsExtensions"),M=o((s,e,r)=>{if(x("resolveTsFilename",{request:e,isDirectory:_.test(e),isTsParent:r,allowJs:exports.allowJs}),_.test(e)||!r&&!exports.allowJs)return;const n=X(e);if(n)for(const t of n)try{return s(t)}catch(a){const{code:c}=a;if(c!=="MODULE_NOT_FOUND"&&c!=="ERR_PACKAGE_PATH_NOT_EXPORTED")throw a}},"resolveTsFilename"),he=o((s,e)=>r=>{if(x("resolveTsFilename",{request:r,isTsParent:e,isFilePath:j(r)}),j(r)){const n=M(s,r,e);if(n)return n}try{return s(r)}catch(n){const t=n;if(t.code==="MODULE_NOT_FOUND"){if(t.path){const c=t.message.match(/^Cannot find module '([^']+)'$/);if(c){const f=c[1],l=M(s,f,e);if(l)return l}const i=t.message.match(/^Cannot find module '([^']+)'. Please verify that the package.json has a valid "main" entry$/);if(i){const f=i[1],l=M(s,f,e);if(l)return l}}const a=M(s,r,e);if(a)return a}throw t}},"createTsExtensionResolver"),z="at cjsPreparseModuleExports (node:internal",de=o(s=>{const e=s.stack.split(`
`).slice(1);return e[1].includes(z)||e[2].includes(z)},"isFromCjsLexer"),me=o((s,e)=>{const r=s.split("?"),n=new URLSearchParams(r[1]);if(e?.filename){const t=D(e.filename);let a;if(t){const f=t.split("?"),l=f[0];a=f[1],e.filename=l,e.path=d.dirname(l),e.paths=p._nodeModulePaths(e.path),p._cache[l]=e}a||(a=e.filename.split("?")[1]);const i=new URLSearchParams(a).get("namespace");i&&n.append("namespace",i)}return[r[0],n,(t,a)=>(d.isAbsolute(t)&&!t.endsWith(".json")&&!t.endsWith(".node")&&!(a===0&&de(new Error))&&(t+=T(n)),t)]},"preserveQuery"),pe=o((s,e,r)=>{if(s.startsWith(v)&&(s=O.fileURLToPath(s)),exports.tsconfigPathsMatcher&&!j(s)&&!e?.filename?.includes(w)){const n=exports.tsconfigPathsMatcher(s);for(const t of n)try{return r(t)}catch{}}return r(s)},"resolveTsPaths"),ue=o((s,e,r)=>(n,t,...a)=>{if(s.enabled===!1)return e(n,t,...a);n=C(n);const[c,i,f]=me(n,t);if((i.get("namespace")??void 0)!==r)return e(n,t,...a);x("resolve",{request:n,parent:t?.filename??t,restOfArgs:a});let l=o(P=>e(P,t,...a),"nextResolveSimple");l=he(l,!!(r||t?.filename&&N.test(t.filename))),l=ie(l);const u=f(pe(c,t,l),a.length);return x("resolved",{request:n,resolved:u}),u},"createResolveFilename"),H=o((s,e)=>{if(!e)throw new Error("The current file path (__filename or import.meta.url) must be provided in the second argument of tsx.require()");return s.startsWith(".")?((typeof e=="string"&&e.startsWith(v)||e instanceof URL)&&(e=O.fileURLToPath(e)),d.resolve(d.dirname(e),s)):s},"resolveContext"),Pe=o(s=>{const{sourceMapsEnabled:e}=process,r={enabled:!0};Q(process.env.TSX_TSCONFIG_PATH),process.setSourceMapsEnabled(!0);const n=p._resolveFilename,t=ue(r,n,s?.namespace);p._resolveFilename=t;const a=ce(r,p._extensions,s?.namespace),c=o(()=>{e===!1&&process.setSourceMapsEnabled(!1),r.enabled=!1,p._resolveFilename===t&&(p._resolveFilename=n),a()},"unregister");if(s?.namespace){const i=o((l,u)=>{const P=H(l,u),[m,h]=P.split("?"),E=new URLSearchParams(h);return s.namespace&&!m.startsWith("node:")&&E.set("namespace",s.namespace),Y.require(m+T(E))},"scopedRequire");c.require=i;const f=o((l,u,P)=>{const m=H(l,u),[h,E]=m.split("?"),L=new URLSearchParams(E);return s.namespace&&!h.startsWith("node:")&&L.set("namespace",s.namespace),t(h+T(L),module,!1,P)},"scopedResolve");c.resolve=f,c.unregister=c}return c},"register");exports.cjsExtensionPattern=ee,exports.debugEnabled=$,exports.fileUrlPrefix=v,exports.inlineSourceMap=F,exports.interopCjsExports=C,exports.isBarePackageNamePattern=te,exports.isDirectoryPattern=_,exports.isJsonPattern=se,exports.isRelativePath=J,exports.loadTsconfig=Q,exports.logEsm=re,exports.mapTsExtensions=X,exports.nodeModulesPath=w,exports.register=Pe,exports.requestAcceptsQuery=q,exports.tsExtensionsPattern=N;
