// Test authentication flow and protected endpoints
const baseUrl = 'http://localhost:3000';

async function testAuthenticatedFlow() {
  console.log('🔐 Testing Authentication Flow\n');
  
  try {
    // Step 1: Try to access protected endpoint without auth
    console.log('1. Testing protected endpoint without auth...');
    const unauthedResponse = await fetch(`${baseUrl}/api/clients`);
    console.log(`   Status: ${unauthedResponse.status} (Expected: 401)`);
    
    // Step 2: Test credentials authentication
    console.log('\n2. Testing credentials authentication...');
    const authResponse = await fetch(`${baseUrl}/api/auth/callback/credentials`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        email: '<EMAIL>',
        password: 'password123',
        csrfToken: 'test', // In real app, this would be from the form
        callbackUrl: `${baseUrl}/dashboard`,
        json: 'true'
      })
    });
    
    console.log(`   Auth Status: ${authResponse.status}`);
    
    // Step 3: Test AI endpoint (should work even without auth for testing)
    console.log('\n3. Testing AI suggestions endpoint...');
    const aiResponse = await fetch(`${baseUrl}/api/ai/suggestions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'OBJECTIVE',
        context: 'Client created a colorful painting during art therapy session',
        mediaType: 'IMAGE'
      })
    });
    
    const aiData = await aiResponse.json();
    console.log(`   AI Status: ${aiResponse.status}`);
    console.log(`   AI Response: ${aiData.suggestion?.substring(0, 100)}...`);
    
    // Step 4: Test health endpoint
    console.log('\n4. Testing system health...');
    const healthResponse = await fetch(`${baseUrl}/api/health`);
    const healthData = await healthResponse.json();
    console.log(`   Health Status: ${healthResponse.status}`);
    console.log(`   Services Status:`, healthData.services);
    
    console.log('\n✅ Authentication Flow Test Complete');
    
    // Summary
    console.log('\n📊 Test Results:');
    console.log(`- Protected routes: ${unauthedResponse.status === 401 ? '✅ Properly secured' : '❌ Security issue'}`);
    console.log(`- AI suggestions: ${aiResponse.status === 200 ? '✅ Working (fallback)' : '❌ Not working'}`);
    console.log(`- System health: ${healthResponse.status === 200 ? '✅ Healthy' : '❌ Issues detected'}`);
    console.log(`- Database: ${healthData.database === 'connected' ? '✅ Connected' : '❌ Connection issues'}`);
    
    console.log('\n🔑 API Key Status:');
    console.log(`- OpenAI: ${healthData.services.openai === 'configured' ? '✅ Configured' : '⚠️ Missing (using fallback)'}`);
    console.log(`- AWS: ${healthData.services.aws === 'configured' ? '✅ Configured' : '⚠️ Missing (using local storage)'}`);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testAuthenticatedFlow();
