// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String?
  role          UserRole  @default(CAT)
  passwordHash  String
  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // Relations
  sessionNotes  SessionNote[]
  auditLogs     AuditLog[]
  
  @@map("users")
}

model Client {
  id            String    @id @default(cuid())
  firstName     String
  lastName      String
  dateOfBirth   DateTime?
  ehrId         String?   @unique // External EHR system ID
  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // Relations
  sessionNotes  SessionNote[]
  
  @@map("clients")
}

model SessionNote {
  id            String    @id @default(cuid())
  clientId      String
  therapistId   String
  sessionDate   DateTime
  noteType      NoteType  @default(SOAP)
  
  // SOAP Note Structure
  subjective    String?   // Client's reported experience
  objective     String?   // Therapist's observations
  assessment    String?   // Clinical assessment
  plan          String?   // Treatment plan
  
  // Additional fields
  sessionDuration Int?    // Duration in minutes
  interventions   String? // Interventions used
  clientMood      String? // Client's mood/presentation
  
  isFinalized   Boolean   @default(false)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // Relations
  client        Client    @relation(fields: [clientId], references: [id])
  therapist     User      @relation(fields: [therapistId], references: [id])
  multimedia    MultimediaFile[]
  aiSuggestions AiSuggestion[]
  
  @@map("session_notes")
}

model MultimediaFile {
  id            String    @id @default(cuid())
  sessionNoteId String
  fileName      String
  originalName  String
  fileType      MediaType
  fileSize      Int       // Size in bytes
  s3Key         String    // AWS S3 object key
  s3Bucket      String    // AWS S3 bucket name
  description   String?   // User-provided description
  aiDescription String?   // AI-generated description
  uploadedAt    DateTime  @default(now())
  
  // Relations
  sessionNote   SessionNote @relation(fields: [sessionNoteId], references: [id], onDelete: Cascade)
  
  @@map("multimedia_files")
}

model AiSuggestion {
  id            String    @id @default(cuid())
  sessionNoteId String
  multimediaId  String?   // Optional - if suggestion is for specific media
  suggestionType SuggestionType
  originalText  String?   // Original text that prompted the suggestion
  suggestedText String    // AI-generated suggestion
  isAccepted    Boolean   @default(false)
  isRejected    Boolean   @default(false)
  createdAt     DateTime  @default(now())
  
  // Relations
  sessionNote   SessionNote @relation(fields: [sessionNoteId], references: [id], onDelete: Cascade)
  
  @@map("ai_suggestions")
}

model AuditLog {
  id            String    @id @default(cuid())
  userId        String
  action        String    // Action performed (CREATE, UPDATE, DELETE, VIEW)
  resourceType  String    // Type of resource (SessionNote, MultimediaFile, etc.)
  resourceId    String    // ID of the resource
  details       Json?     // Additional details about the action
  ipAddress     String?
  userAgent     String?
  timestamp     DateTime  @default(now())
  
  // Relations
  user          User      @relation(fields: [userId], references: [id])
  
  @@map("audit_logs")
}

// Enums
enum UserRole {
  CAT           // Creative Arts Therapist
  PSYCHIATRIST
  CASE_MANAGER
  ADMIN
}

enum NoteType {
  SOAP
  DAP
  BIRP
  GIRP
}

enum MediaType {
  IMAGE
  VIDEO
  AUDIO
}

enum SuggestionType {
  DESCRIPTION   // Describing what's in the media
  INTERPRETATION // Clinical interpretation
  OBJECTIVE     // For objective section of SOAP note
  ASSESSMENT    // For assessment section
  PLAN          // For plan section
}
