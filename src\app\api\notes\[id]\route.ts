import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const note = await db.sessionNote.findFirst({
      where: {
        id: params.id,
        therapistId: session.user.id, // Ensure user can only access their own notes
      },
      include: {
        client: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            dateOfBirth: true,
          }
        },
        multimedia: {
          orderBy: {
            uploadedAt: 'asc'
          }
        },
        aiSuggestions: {
          orderBy: {
            createdAt: 'desc'
          }
        },
        therapist: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      }
    });

    if (!note) {
      return NextResponse.json({ error: 'Session note not found' }, { status: 404 });
    }

    return NextResponse.json(note);
  } catch (error) {
    console.error('Error fetching session note:', error);
    return NextResponse.json(
      { error: 'Failed to fetch session note' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      sessionDate,
      sessionDuration,
      noteType,
      subjective,
      objective,
      assessment,
      plan,
      interventions,
      clientMood,
      isFinalized
    } = body;

    // Check if note exists and belongs to user
    const existingNote = await db.sessionNote.findFirst({
      where: {
        id: params.id,
        therapistId: session.user.id,
      }
    });

    if (!existingNote) {
      return NextResponse.json({ error: 'Session note not found' }, { status: 404 });
    }

    // Update the session note
    const updatedNote = await db.sessionNote.update({
      where: {
        id: params.id,
      },
      data: {
        sessionDate: sessionDate ? new Date(sessionDate) : undefined,
        sessionDuration: sessionDuration ? parseInt(sessionDuration) : undefined,
        noteType,
        subjective,
        objective,
        assessment,
        plan,
        interventions,
        clientMood,
        isFinalized,
        updatedAt: new Date(),
      },
      include: {
        client: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          }
        },
        multimedia: true,
      }
    });

    // Log the action for audit trail
    await db.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'UPDATE',
        resourceType: 'SessionNote',
        resourceId: params.id,
        details: {
          isFinalized,
          noteType,
          fieldsUpdated: Object.keys(body),
        },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      }
    });

    return NextResponse.json(updatedNote);
  } catch (error) {
    console.error('Error updating session note:', error);
    return NextResponse.json(
      { error: 'Failed to update session note' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if note exists and belongs to user
    const existingNote = await db.sessionNote.findFirst({
      where: {
        id: params.id,
        therapistId: session.user.id,
      }
    });

    if (!existingNote) {
      return NextResponse.json({ error: 'Session note not found' }, { status: 404 });
    }

    // Delete the session note (cascade will handle multimedia and AI suggestions)
    await db.sessionNote.delete({
      where: {
        id: params.id,
      }
    });

    // Log the action for audit trail
    await db.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'DELETE',
        resourceType: 'SessionNote',
        resourceId: params.id,
        details: {
          clientId: existingNote.clientId,
          noteType: existingNote.noteType,
        },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      }
    });

    return NextResponse.json({ message: 'Session note deleted successfully' });
  } catch (error) {
    console.error('Error deleting session note:', error);
    return NextResponse.json(
      { error: 'Failed to delete session note' },
      { status: 500 }
    );
  }
}
