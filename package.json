{"name": "vistanotes", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma migrate reset --force", "build:analyze": "ANALYZE=true npm run build", "build:production": "NODE_ENV=production npm run build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "docker:build": "docker build -t vistanotes .", "docker:run": "docker run -p 3000:3000 vistanotes", "deploy:production": "./scripts/migrate-to-production.sh", "health:check": "curl http://localhost:3000/api/health", "logs:app": "docker-compose logs -f app", "logs:db": "docker-compose logs -f db", "backup:db": "docker-compose exec backup pg_dump -h db -U vistanotes vistanotes > backups/backup_$(date +%Y%m%d_%H%M%S).sql"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@hookform/resolvers": "^5.1.0", "@prisma/client": "^6.9.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-tabs": "^1.1.12", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "groq-sdk": "^0.24.0", "lucide-react": "^0.513.0", "next": "15.3.3", "next-auth": "^4.24.11", "openai": "^5.3.0", "prisma": "^6.9.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "recharts": "^2.15.3", "tailwind-merge": "^3.3.0", "zod": "^3.25.56"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tsx": "^4.20.1", "typescript": "^5"}}