{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VistaNotes/src/lib/ai.ts"], "sourcesContent": ["import OpenAI from 'openai'\nimport Groq from 'groq-sdk'\n\nexport interface AiSuggestionRequest {\n  type: 'DESCRIPTION' | 'INTERPRETATION' | 'OBJECTIVE' | 'ASSESSMENT' | 'PLAN'\n  context: string\n  mediaType?: 'IMAGE' | 'VIDEO' | 'AUDIO'\n  clientAge?: number\n  sessionGoals?: string\n  clientHistory?: string\n  sessionDuration?: number\n}\n\nexport interface AiSuggestionResponse {\n  suggestion: string\n  confidence: number\n  alternatives?: string[]\n}\n\n// Initialize AI clients\nconst openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY,\n})\n\nconst groq = new Groq({\n  apiKey: process.env.GROQ_API_KEY,\n})\n\n// AI Provider configuration\nconst AI_PROVIDER = process.env.AI_PROVIDER || 'groq'\n\nexport async function generateAiSuggestion(\n  request: AiSuggestionRequest\n): Promise<AiSuggestionResponse> {\n  try {\n    const response = await fetch('/api/ai/suggestions', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(request),\n    })\n\n    if (!response.ok) {\n      throw new Error('Failed to generate AI suggestion')\n    }\n\n    return await response.json()\n  } catch (error) {\n    console.error('Error generating AI suggestion:', error)\n    throw new Error('AI service temporarily unavailable')\n  }\n}\n\nexport async function generateAISuggestion(\n  request: AiSuggestionRequest\n): Promise<AiSuggestionResponse> {\n  if (AI_PROVIDER === 'groq') {\n    return generateGroqSuggestion(request)\n  } else {\n    return generateOpenAISuggestion(request)\n  }\n}\n\nexport async function generateGroqSuggestion(\n  request: AiSuggestionRequest\n): Promise<AiSuggestionResponse> {\n  try {\n    const prompt = buildPrompt(request)\n\n    const completion = await groq.chat.completions.create({\n      model: \"llama-3.1-8b-instant\", // Current fast and reliable model\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are an expert Creative Arts Therapist assistant. Generate professional, clinical documentation that follows healthcare standards. Be concise, objective, and therapeutically appropriate. Focus on observable behaviors, emotional expression, and therapeutic progress.\"\n        },\n        {\n          role: \"user\",\n          content: prompt\n        }\n      ],\n      max_tokens: 300,\n      temperature: 0.7,\n    })\n\n    const suggestion = completion.choices[0]?.message?.content || \"Unable to generate suggestion\"\n\n    return {\n      suggestion: suggestion.trim(),\n      confidence: 0.88, // Groq models are very good\n      alternatives: []\n    }\n  } catch (error) {\n    console.error('Groq API error:', error)\n    // Fallback to mock response if Groq fails\n    return generateMockResponse(request)\n  }\n}\n\nexport async function generateOpenAISuggestion(\n  request: AiSuggestionRequest\n): Promise<AiSuggestionResponse> {\n  try {\n    const prompt = buildPrompt(request)\n\n    const completion = await openai.chat.completions.create({\n      model: \"gpt-3.5-turbo\",\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are an expert Creative Arts Therapist assistant. Generate professional, clinical documentation that follows healthcare standards. Be concise, objective, and therapeutically appropriate. Focus on observable behaviors, emotional expression, and therapeutic progress.\"\n        },\n        {\n          role: \"user\",\n          content: prompt\n        }\n      ],\n      max_tokens: 300,\n      temperature: 0.7,\n    })\n\n    const suggestion = completion.choices[0]?.message?.content || \"Unable to generate suggestion\"\n\n    return {\n      suggestion: suggestion.trim(),\n      confidence: 0.85,\n      alternatives: []\n    }\n  } catch (error) {\n    console.error('OpenAI API error:', error)\n    // Fallback to mock response if OpenAI fails\n    return generateMockResponse(request)\n  }\n}\n\nexport async function analyzeImageWithAI(\n  imageBase64: string,\n  context?: string\n): Promise<AiSuggestionResponse> {\n  try {\n    // Note: GPT-4 Vision requires special access. For now, using text-based analysis\n    // When GPT-4 Vision access is available, uncomment the vision API call below\n\n    /*\n    const completion = await openai.chat.completions.create({\n      model: \"gpt-4-vision-preview\",\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are an expert Creative Arts Therapist analyzing client artwork. Provide professional, clinical observations about the visual elements, emotional expression, and therapeutic significance. Focus on colors, forms, composition, technique, and potential symbolic meaning.\"\n        },\n        {\n          role: \"user\",\n          content: [\n            {\n              type: \"text\",\n              text: `Analyze this artwork created during a Creative Arts Therapy session. ${context ? `Context: ${context}` : ''} Provide a clinical description suitable for therapy documentation.`\n            },\n            {\n              type: \"image_url\",\n              image_url: {\n                url: `data:image/jpeg;base64,${imageBase64}`\n              }\n            }\n          ]\n        }\n      ],\n      max_tokens: 400,\n      temperature: 0.6,\n    })\n\n    const analysis = completion.choices[0]?.message?.content || \"Unable to analyze image\"\n\n    return {\n      suggestion: analysis.trim(),\n      confidence: 0.80,\n      alternatives: []\n    }\n    */\n\n    // For now, use context-based analysis with GPT-3.5-turbo\n    const completion = await openai.chat.completions.create({\n      model: \"gpt-3.5-turbo\",\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are an expert Creative Arts Therapist. Based on the provided context about client artwork, generate professional clinical observations about the creative process, emotional expression, and therapeutic significance.\"\n        },\n        {\n          role: \"user\",\n          content: `Analyze artwork created during a Creative Arts Therapy session. Context: ${context || 'Client created visual artwork during therapy session'}. Provide a clinical description focusing on the creative process, potential emotional expression, and therapeutic significance for documentation purposes.`\n        }\n      ],\n      max_tokens: 300,\n      temperature: 0.6,\n    })\n\n    const analysis = completion.choices[0]?.message?.content || \"Unable to analyze artwork\"\n\n    return {\n      suggestion: analysis.trim(),\n      confidence: 0.75,\n      alternatives: []\n    }\n  } catch (error) {\n    console.error('OpenAI Vision API error:', error)\n    // Fallback to text-based analysis\n    return {\n      suggestion: \"Artwork demonstrates client's creative engagement and emotional expression through visual media. The creative process shows therapeutic value and potential for emotional processing. Further detailed analysis available with enhanced image processing capabilities.\",\n      confidence: 0.60,\n      alternatives: []\n    }\n  }\n}\n\nfunction buildPrompt(request: AiSuggestionRequest): string {\n  const { type, context, mediaType, clientAge, sessionGoals, clientHistory, sessionDuration } = request\n\n  let prompt = `Generate a ${type.toLowerCase()} section for a Creative Arts Therapy SOAP note.\\n\\n`\n\n  if (context) {\n    prompt += `Session Context: ${context}\\n`\n  }\n\n  if (mediaType) {\n    prompt += `Media Type: ${mediaType} therapy session\\n`\n  }\n\n  if (clientAge) {\n    prompt += `Client Age: ${clientAge}\\n`\n  }\n\n  if (sessionDuration) {\n    prompt += `Session Duration: ${sessionDuration} minutes\\n`\n  }\n\n  if (sessionGoals) {\n    prompt += `Session Goals: ${sessionGoals}\\n`\n  }\n\n  if (clientHistory) {\n    prompt += `Relevant History: ${clientHistory}\\n`\n  }\n\n  prompt += `\\nPlease provide a professional ${type.toLowerCase()} entry that is:\\n`\n\n  switch (type) {\n    case 'OBJECTIVE':\n      prompt += \"- Factual and observable\\n- Describes client behaviors and engagement\\n- Notes creative process and artistic choices\\n- Includes duration and participation level\"\n      break\n    case 'ASSESSMENT':\n      prompt += \"- Clinical interpretation of observations\\n- Progress toward therapeutic goals\\n- Emotional and psychological insights\\n- Therapeutic relationship quality\"\n      break\n    case 'PLAN':\n      prompt += \"- Specific next steps and interventions\\n- Goals for upcoming sessions\\n- Recommendations for treatment\\n- Any referrals or consultations needed\"\n      break\n    case 'DESCRIPTION':\n      prompt += \"- Detailed description of creative work\\n- Colors, forms, techniques used\\n- Artistic process and engagement\\n- Observable emotional expression\"\n      break\n    case 'INTERPRETATION':\n      prompt += \"- Therapeutic meaning of creative expression\\n- Symbolic or emotional significance\\n- Connection to treatment goals\\n- Clinical insights\"\n      break\n  }\n\n  return prompt\n}\n\nfunction generateMockResponse(request: AiSuggestionRequest): AiSuggestionResponse {\n  const mockResponses = {\n    OBJECTIVE: \"Client engaged actively in art-making process, demonstrating sustained attention for 30 minutes. Selected vibrant colors and created abstract forms with confident brushstrokes. Maintained appropriate eye contact and responded verbally to therapeutic prompts throughout session.\",\n    ASSESSMENT: \"Client shows continued progress in emotional expression through creative media. Increased confidence in artistic choices and willingness to discuss emotional content of artwork. Mood appears stable with positive engagement in therapeutic process.\",\n    PLAN: \"Continue weekly art therapy sessions focusing on emotional expression through color and form. Introduce new media (clay work) to explore tactile processing. Schedule follow-up assessment in 4 weeks to evaluate progress toward treatment goals.\",\n    DESCRIPTION: \"Client created mixed-media artwork featuring bold, expressive brushstrokes in warm colors. The composition shows dynamic movement with overlapping forms and varied textures, suggesting emotional engagement and creative exploration.\",\n    INTERPRETATION: \"The client's use of warm, vibrant colors may indicate improved mood and emotional accessibility. Bold, confident brushstrokes suggest increased self-assurance and willingness to take creative risks in the therapeutic space.\"\n  }\n\n  return {\n    suggestion: mockResponses[request.type] || \"AI-generated clinical suggestion based on session context.\",\n    confidence: 0.80,\n    alternatives: []\n  }\n}\n\nexport function getPromptTemplate(type: string, mediaType?: string): string {\n  const templates = {\n    DESCRIPTION: {\n      IMAGE: \"Describe this artwork created during a creative arts therapy session. Focus on visual elements like colors, shapes, composition, and artistic techniques used. Keep the description clinical and objective.\",\n      VIDEO: \"Describe the movement and creative expression observed in this video from a creative arts therapy session. Focus on body language, movement patterns, and creative process.\",\n      AUDIO: \"Describe the musical or vocal expression captured in this audio recording from a creative arts therapy session. Focus on rhythm, tone, emotional expression, and creative elements.\",\n      DEFAULT: \"Provide a clinical description of the creative expression observed during this therapy session.\"\n    },\n    INTERPRETATION: {\n      IMAGE: \"Provide a clinical interpretation of this artwork, considering potential emotional expression, therapeutic themes, and symbolic elements that may be relevant for treatment planning.\",\n      VIDEO: \"Analyze the movement and creative expression for therapeutic insights, considering emotional regulation, self-expression, and progress indicators.\",\n      AUDIO: \"Interpret the musical/vocal expression for therapeutic significance, considering emotional state, communication patterns, and creative engagement.\",\n      DEFAULT: \"Provide a clinical interpretation of the creative expression and its therapeutic significance.\"\n    },\n    OBJECTIVE: \"Generate objective observations for a SOAP note based on the creative arts therapy session described. Focus on observable behaviors, creative process, and client engagement.\",\n    ASSESSMENT: \"Provide a clinical assessment based on the creative arts therapy session observations. Consider therapeutic progress, emotional regulation, and treatment goals.\",\n    PLAN: \"Suggest treatment plan elements based on the creative arts therapy session. Include specific interventions, goals, and recommendations for future sessions.\"\n  }\n\n  if (type in templates && typeof templates[type] === 'object') {\n    return templates[type][mediaType || 'DEFAULT']\n  }\n  \n  return templates[type] || templates.DESCRIPTION.DEFAULT\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AACA;;;AAkBA,wBAAwB;AACxB,MAAM,SAAS,IAAI,wKAAA,CAAA,UAAM,CAAC;IACxB,QAAQ,QAAQ,GAAG,CAAC,cAAc;AACpC;AAEA,MAAM,OAAO,IAAI,uJAAA,CAAA,UAAI,CAAC;IACpB,QAAQ,QAAQ,GAAG,CAAC,YAAY;AAClC;AAEA,4BAA4B;AAC5B,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAExC,eAAe,qBACpB,OAA4B;IAE5B,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,uBAAuB;YAClD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,eAAe,qBACpB,OAA4B;IAE5B,IAAI,gBAAgB,QAAQ;QAC1B,OAAO,uBAAuB;IAChC,OAAO;QACL,OAAO,yBAAyB;IAClC;AACF;AAEO,eAAe,uBACpB,OAA4B;IAE5B,IAAI;QACF,MAAM,SAAS,YAAY;QAE3B,MAAM,aAAa,MAAM,KAAK,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACpD,OAAO;YACP,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,YAAY;YACZ,aAAa;QACf;QAEA,MAAM,aAAa,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;QAE9D,OAAO;YACL,YAAY,WAAW,IAAI;YAC3B,YAAY;YACZ,cAAc,EAAE;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,0CAA0C;QAC1C,OAAO,qBAAqB;IAC9B;AACF;AAEO,eAAe,yBACpB,OAA4B;IAE5B,IAAI;QACF,MAAM,SAAS,YAAY;QAE3B,MAAM,aAAa,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACtD,OAAO;YACP,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,YAAY;YACZ,aAAa;QACf;QAEA,MAAM,aAAa,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;QAE9D,OAAO;YACL,YAAY,WAAW,IAAI;YAC3B,YAAY;YACZ,cAAc,EAAE;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,4CAA4C;QAC5C,OAAO,qBAAqB;IAC9B;AACF;AAEO,eAAe,mBACpB,WAAmB,EACnB,OAAgB;IAEhB,IAAI;QACF,iFAAiF;QACjF,6EAA6E;QAE7E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmCA,GAEA,yDAAyD;QACzD,MAAM,aAAa,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACtD,OAAO;YACP,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS,CAAC,yEAAyE,EAAE,WAAW,uDAAuD,2JAA2J,CAAC;gBACrT;aACD;YACD,YAAY;YACZ,aAAa;QACf;QAEA,MAAM,WAAW,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;QAE5D,OAAO;YACL,YAAY,SAAS,IAAI;YACzB,YAAY;YACZ,cAAc,EAAE;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,kCAAkC;QAClC,OAAO;YACL,YAAY;YACZ,YAAY;YACZ,cAAc,EAAE;QAClB;IACF;AACF;AAEA,SAAS,YAAY,OAA4B;IAC/C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG;IAE9F,IAAI,SAAS,CAAC,WAAW,EAAE,KAAK,WAAW,GAAG,mDAAmD,CAAC;IAElG,IAAI,SAAS;QACX,UAAU,CAAC,iBAAiB,EAAE,QAAQ,EAAE,CAAC;IAC3C;IAEA,IAAI,WAAW;QACb,UAAU,CAAC,YAAY,EAAE,UAAU,kBAAkB,CAAC;IACxD;IAEA,IAAI,WAAW;QACb,UAAU,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;IACxC;IAEA,IAAI,iBAAiB;QACnB,UAAU,CAAC,kBAAkB,EAAE,gBAAgB,UAAU,CAAC;IAC5D;IAEA,IAAI,cAAc;QAChB,UAAU,CAAC,eAAe,EAAE,aAAa,EAAE,CAAC;IAC9C;IAEA,IAAI,eAAe;QACjB,UAAU,CAAC,kBAAkB,EAAE,cAAc,EAAE,CAAC;IAClD;IAEA,UAAU,CAAC,gCAAgC,EAAE,KAAK,WAAW,GAAG,iBAAiB,CAAC;IAElF,OAAQ;QACN,KAAK;YACH,UAAU;YACV;QACF,KAAK;YACH,UAAU;YACV;QACF,KAAK;YACH,UAAU;YACV;QACF,KAAK;YACH,UAAU;YACV;QACF,KAAK;YACH,UAAU;YACV;IACJ;IAEA,OAAO;AACT;AAEA,SAAS,qBAAqB,OAA4B;IACxD,MAAM,gBAAgB;QACpB,WAAW;QACX,YAAY;QACZ,MAAM;QACN,aAAa;QACb,gBAAgB;IAClB;IAEA,OAAO;QACL,YAAY,aAAa,CAAC,QAAQ,IAAI,CAAC,IAAI;QAC3C,YAAY;QACZ,cAAc,EAAE;IAClB;AACF;AAEO,SAAS,kBAAkB,IAAY,EAAE,SAAkB;IAChE,MAAM,YAAY;QAChB,aAAa;YACX,OAAO;YACP,OAAO;YACP,OAAO;YACP,SAAS;QACX;QACA,gBAAgB;YACd,OAAO;YACP,OAAO;YACP,OAAO;YACP,SAAS;QACX;QACA,WAAW;QACX,YAAY;QACZ,MAAM;IACR;IAEA,IAAI,QAAQ,aAAa,OAAO,SAAS,CAAC,KAAK,KAAK,UAAU;QAC5D,OAAO,SAAS,CAAC,KAAK,CAAC,aAAa,UAAU;IAChD;IAEA,OAAO,SAAS,CAAC,KAAK,IAAI,UAAU,WAAW,CAAC,OAAO;AACzD", "debugId": null}}, {"offset": {"line": 402, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VistaNotes/src/app/api/ai/suggestions/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { generateAISuggestion, AiSuggestionRequest } from '@/lib/ai';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { type, context, mediaType, clientAge, sessionGoals, clientHistory, sessionDuration } = body;\n\n    // Validate required fields\n    if (!type) {\n      return NextResponse.json(\n        { error: 'Missing required field: type' },\n        { status: 400 }\n      );\n    }\n\n    // Build the AI request\n    const aiRequest: AiSuggestionRequest = {\n      type,\n      context: context || '',\n      mediaType,\n      clientAge,\n      sessionGoals,\n      clientHistory,\n      sessionDuration\n    };\n\n    // Use AI API (Groq or OpenAI with fallback to mock)\n    const response = await generateAISuggestion(aiRequest);\n\n    return NextResponse.json({\n      suggestion: response.suggestion,\n      confidence: response.confidence,\n      alternatives: response.alternatives\n    });\n\n  } catch (error) {\n    console.error('Error generating AI suggestion:', error);\n    return NextResponse.json(\n      { error: 'Failed to generate AI suggestion' },\n      { status: 500 }\n    );\n  }\n}\n\nasync function generateMockAIResponse(\n  type: string, \n  context: string, \n  mediaType?: string\n): Promise<{\n  suggestion: string;\n  confidence: number;\n  alternatives?: string[];\n}> {\n  // Simulate API delay\n  await new Promise(resolve => setTimeout(resolve, 1000));\n\n  const responses = {\n    DESCRIPTION: {\n      IMAGE: {\n        suggestion: \"Client created a mixed-media artwork featuring bold, expressive brushstrokes in warm colors (reds, oranges, yellows). The composition shows dynamic movement with overlapping forms and varied textures. The client demonstrated sustained focus and deliberate color choices throughout the 25-minute creative process.\",\n        confidence: 0.85,\n        alternatives: [\n          \"Artwork displays vibrant color palette with energetic brushwork, suggesting positive emotional engagement and creative expression.\",\n          \"Client's painting demonstrates confident mark-making and intentional composition, indicating therapeutic progress in self-expression.\"\n        ]\n      },\n      VIDEO: {\n        suggestion: \"Client engaged in fluid movement therapy, demonstrating progressive relaxation and increased range of motion. Initial movements were tentative and restricted, evolving to more expansive and confident gestures. Client maintained appropriate spatial awareness and responded positively to musical cues.\",\n        confidence: 0.82,\n        alternatives: [\n          \"Movement session showed gradual increase in client's comfort level and willingness to explore physical expression.\",\n          \"Client demonstrated improved body awareness and emotional regulation through structured movement activities.\"\n        ]\n      },\n      AUDIO: {\n        suggestion: \"Client's musical expression included rhythmic drumming with steady tempo and occasional dynamic variations. Demonstrated improved coordination and timing compared to previous sessions. Client verbalized positive feelings about the musical experience and showed increased confidence in creative expression.\",\n        confidence: 0.88,\n        alternatives: [\n          \"Audio recording captures client's growing musical confidence and improved rhythmic stability.\",\n          \"Musical session demonstrates client's enhanced focus and emotional regulation through sound exploration.\"\n        ]\n      }\n    },\n    INTERPRETATION: {\n      IMAGE: {\n        suggestion: \"The client's use of warm, vibrant colors may indicate improved mood and emotional accessibility. The bold, confident brushstrokes suggest increased self-assurance and willingness to take creative risks. The central composition with radiating elements could represent the client's growing sense of personal agency and expanding social connections.\",\n        confidence: 0.78,\n        alternatives: [\n          \"Artwork suggests positive therapeutic progress with increased emotional expression and creative confidence.\",\n          \"Color choices and composition indicate client's developing sense of empowerment and emotional regulation.\"\n        ]\n      },\n      DEFAULT: {\n        suggestion: \"Client's creative expression demonstrates therapeutic progress in emotional regulation and self-awareness. The willingness to engage in creative risk-taking suggests increased confidence and trust in the therapeutic process. Observable improvements in focus and sustained attention indicate positive response to creative arts interventions.\",\n        confidence: 0.80\n      }\n    },\n    OBJECTIVE: {\n      suggestion: \"Client arrived punctually and engaged cooperatively in session activities. Demonstrated sustained attention for 35 minutes during art-making process. Made appropriate eye contact and responded verbally to therapeutic prompts. Exhibited calm demeanor with no observable signs of distress. Completed creative task independently with minimal guidance.\",\n      confidence: 0.90,\n      alternatives: [\n        \"Client showed active participation and positive engagement throughout the 45-minute session with appropriate social interaction.\",\n        \"Observable behaviors included focused attention, cooperative attitude, and willingness to discuss creative process and emotional content.\"\n      ]\n    },\n    ASSESSMENT: {\n      suggestion: \"Client continues to demonstrate progress in emotional expression and self-regulation through creative arts interventions. Increased confidence in artistic choices and willingness to discuss emotional content of creative work indicates positive therapeutic alliance. Current presentation suggests stable mood with continued engagement in treatment goals.\",\n      confidence: 0.85,\n      alternatives: [\n        \"Client shows sustained improvement in creative self-expression and emotional processing capabilities.\",\n        \"Therapeutic progress evident in client's increased openness and willingness to explore emotional themes through art.\"\n      ]\n    },\n    PLAN: {\n      suggestion: \"Continue weekly creative arts therapy sessions focusing on emotional expression through visual media. Introduce new artistic materials (clay, pastels) to expand creative vocabulary. Schedule follow-up assessment in 4 weeks to evaluate progress toward treatment goals. Consider group therapy referral to enhance social skills development.\",\n      confidence: 0.87,\n      alternatives: [\n        \"Maintain current treatment frequency with gradual introduction of more challenging creative tasks to build confidence.\",\n        \"Continue individual sessions while exploring opportunities for peer interaction through structured creative activities.\"\n      ]\n    }\n  };\n\n  // Get response based on type and media type\n  const typeResponses = responses[type as keyof typeof responses];\n  if (!typeResponses) {\n    return {\n      suggestion: \"AI-generated clinical suggestion based on session context and observations.\",\n      confidence: 0.75\n    };\n  }\n\n  if (typeof typeResponses === 'object' && 'suggestion' in typeResponses) {\n    return typeResponses;\n  }\n\n  const mediaResponse = typeResponses[mediaType as keyof typeof typeResponses] || typeResponses['DEFAULT'];\n  return mediaResponse || {\n    suggestion: \"AI-generated clinical suggestion based on session context and observations.\",\n    confidence: 0.75\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG;QAE9F,2BAA2B;QAC3B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+B,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,uBAAuB;QACvB,MAAM,YAAiC;YACrC;YACA,SAAS,WAAW;YACpB;YACA;YACA;YACA;YACA;QACF;QAEA,oDAAoD;QACpD,MAAM,WAAW,MAAM,CAAA,GAAA,kHAAA,CAAA,uBAAoB,AAAD,EAAE;QAE5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,YAAY,SAAS,UAAU;YAC/B,YAAY,SAAS,UAAU;YAC/B,cAAc,SAAS,YAAY;QACrC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAmC,GAC5C;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,eAAe,uBACb,IAAY,EACZ,OAAe,EACf,SAAkB;IAMlB,qBAAqB;IACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;IAEjD,MAAM,YAAY;QAChB,aAAa;YACX,OAAO;gBACL,YAAY;gBACZ,YAAY;gBACZ,cAAc;oBACZ;oBACA;iBACD;YACH;YACA,OAAO;gBACL,YAAY;gBACZ,YAAY;gBACZ,cAAc;oBACZ;oBACA;iBACD;YACH;YACA,OAAO;gBACL,YAAY;gBACZ,YAAY;gBACZ,cAAc;oBACZ;oBACA;iBACD;YACH;QACF;QACA,gBAAgB;YACd,OAAO;gBACL,YAAY;gBACZ,YAAY;gBACZ,cAAc;oBACZ;oBACA;iBACD;YACH;YACA,SAAS;gBACP,YAAY;gBACZ,YAAY;YACd;QACF;QACA,WAAW;YACT,YAAY;YACZ,YAAY;YACZ,cAAc;gBACZ;gBACA;aACD;QACH;QACA,YAAY;YACV,YAAY;YACZ,YAAY;YACZ,cAAc;gBACZ;gBACA;aACD;QACH;QACA,MAAM;YACJ,YAAY;YACZ,YAAY;YACZ,cAAc;gBACZ;gBACA;aACD;QACH;IACF;IAEA,4CAA4C;IAC5C,MAAM,gBAAgB,SAAS,CAAC,KAA+B;IAC/D,IAAI,CAAC,eAAe;QAClB,OAAO;YACL,YAAY;YACZ,YAAY;QACd;IACF;IAEA,IAAI,OAAO,kBAAkB,YAAY,gBAAgB,eAAe;QACtE,OAAO;IACT;IAEA,MAAM,gBAAgB,aAAa,CAAC,UAAwC,IAAI,aAAa,CAAC,UAAU;IACxG,OAAO,iBAAiB;QACtB,YAAY;QACZ,YAAY;IACd;AACF", "debugId": null}}]}