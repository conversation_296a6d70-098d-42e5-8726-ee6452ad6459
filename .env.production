# Production Environment Configuration for VistaNotes

# Application Settings
NODE_ENV="production"
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-production-secret-key-here"

# Database Configuration
# For production, use PostgreSQL or similar
DATABASE_URL="postgresql://username:password@host:port/vistanotes?schema=public"

# AI API Configuration
GROQ_API_KEY="your-groq-api-key"
AI_PROVIDER="groq"

# AWS Configuration (for file storage)
AWS_REGION="us-east-1"
AWS_ACCESS_KEY_ID="your-production-access-key"
AWS_SECRET_ACCESS_KEY="your-production-secret-key"
AWS_S3_BUCKET="vistanotes-production-media"

# Security Settings
ENCRYPTION_KEY="your-32-character-encryption-key"
SESSION_TIMEOUT="3600" # 1 hour in seconds

# Monitoring & Logging
LOG_LEVEL="info"
SENTRY_DSN="your-sentry-dsn-for-error-tracking"

# Rate Limiting
RATE_LIMIT_REQUESTS="100"
RATE_LIMIT_WINDOW="900" # 15 minutes in seconds

# HIPAA Compliance
AUDIT_LOG_RETENTION_DAYS="2555" # 7 years
DATA_ENCRYPTION_AT_REST="true"
SECURE_HEADERS="true"

# Performance
REDIS_URL="redis://localhost:6379" # For caching
CDN_URL="https://cdn.your-domain.com"

# Backup Configuration
BACKUP_SCHEDULE="0 2 * * *" # Daily at 2 AM
BACKUP_RETENTION_DAYS="90"

# Email Configuration (for notifications)
SMTP_HOST="smtp.your-provider.com"
SMTP_PORT="587"
SMTP_USER="your-smtp-user"
SMTP_PASS="your-smtp-password"
FROM_EMAIL="<EMAIL>"

# Feature Flags
ENABLE_ANALYTICS="true"
ENABLE_PATIENT_PORTAL="false"
ENABLE_TELETHERAPY="false"
ENABLE_ADVANCED_METRICS="true"
