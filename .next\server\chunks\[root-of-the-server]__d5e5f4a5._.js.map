{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VistaNotes/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const db =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = db\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,KACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VistaNotes/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from \"next-auth\"\nimport Cred<PERSON><PERSON><PERSON><PERSON>ider from \"next-auth/providers/credentials\"\nimport { PrismaAdapter } from \"@auth/prisma-adapter\"\nimport { db } from \"@/lib/db\"\nimport bcrypt from \"bcryptjs\"\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(db) as any,\n  session: {\n    strategy: \"jwt\",\n  },\n  pages: {\n    signIn: \"/auth/signin\",\n    signUp: \"/auth/signup\",\n  },\n  providers: [\n    CredentialsProvider({\n      name: \"credentials\",\n      credentials: {\n        email: { label: \"Email\", type: \"email\" },\n        password: { label: \"Password\", type: \"password\" }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await db.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.passwordHash\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          role: user.role,\n        }\n      }\n    })\n  ],\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        return {\n          ...token,\n          role: user.role,\n        }\n      }\n      return token\n    },\n    async session({ session, token }) {\n      return {\n        ...session,\n        user: {\n          ...session.user,\n          id: token.sub,\n          role: token.role,\n        }\n      }\n    },\n  },\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,kHAAA,CAAA,KAAE;IACzB,SAAS;QACP,UAAU;IACZ;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;IACV;IACA,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,kHAAA,CAAA,KAAE,CAAC,IAAI,CAAC,UAAU,CAAC;oBACpC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,YAAY;gBAGnB,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,OAAO;oBACL,GAAG,KAAK;oBACR,MAAM,KAAK,IAAI;gBACjB;YACF;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,OAAO;gBACL,GAAG,OAAO;gBACV,MAAM;oBACJ,GAAG,QAAQ,IAAI;oBACf,IAAI,MAAM,GAAG;oBACb,MAAM,MAAM,IAAI;gBAClB;YACF;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VistaNotes/src/app/api/clients/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth';\nimport { db } from '@/lib/db';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n    \n    if (!session?.user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    }\n\n    const { searchParams } = new URL(request.url);\n    const search = searchParams.get('search');\n    const status = searchParams.get('status');\n    const limit = parseInt(searchParams.get('limit') || '50');\n\n    const where: any = {\n      isActive: status === 'inactive' ? false : true,\n    };\n\n    if (search) {\n      where.OR = [\n        { firstName: { contains: search, mode: 'insensitive' } },\n        { lastName: { contains: search, mode: 'insensitive' } },\n      ];\n    }\n\n    const clients = await db.client.findMany({\n      where,\n      include: {\n        sessionNotes: {\n          select: {\n            id: true,\n            sessionDate: true,\n            isFinalized: true,\n          },\n          orderBy: {\n            sessionDate: 'desc'\n          },\n          take: 1, // Get most recent session\n        },\n        _count: {\n          select: {\n            sessionNotes: true,\n          }\n        }\n      },\n      orderBy: [\n        { lastName: 'asc' },\n        { firstName: 'asc' }\n      ],\n      take: limit,\n    });\n\n    // Transform the data to include computed fields\n    const clientsWithStats = clients.map(client => ({\n      ...client,\n      lastSession: client.sessionNotes[0]?.sessionDate || null,\n      totalSessions: client._count.sessionNotes,\n      status: client.isActive ? 'Active' : 'Inactive',\n    }));\n\n    return NextResponse.json(clientsWithStats);\n  } catch (error) {\n    console.error('Error fetching clients:', error);\n    return NextResponse.json(\n      { error: 'Failed to fetch clients' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n    \n    if (!session?.user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    }\n\n    const body = await request.json();\n    const { firstName, lastName, dateOfBirth, ehrId } = body;\n\n    // Validate required fields\n    if (!firstName || !lastName) {\n      return NextResponse.json(\n        { error: 'Missing required fields: firstName and lastName' },\n        { status: 400 }\n      );\n    }\n\n    // Check if EHR ID is unique (if provided)\n    if (ehrId) {\n      const existingClient = await db.client.findUnique({\n        where: { ehrId }\n      });\n\n      if (existingClient) {\n        return NextResponse.json(\n          { error: 'Client with this EHR ID already exists' },\n          { status: 400 }\n        );\n      }\n    }\n\n    // Create the client\n    const client = await db.client.create({\n      data: {\n        firstName,\n        lastName,\n        dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : null,\n        ehrId,\n      }\n    });\n\n    // Log the action for audit trail\n    await db.auditLog.create({\n      data: {\n        userId: session.user.id,\n        action: 'CREATE',\n        resourceType: 'Client',\n        resourceId: client.id,\n        details: {\n          firstName,\n          lastName,\n          ehrId,\n        },\n        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',\n        userAgent: request.headers.get('user-agent') || 'unknown',\n      }\n    });\n\n    return NextResponse.json(client, { status: 201 });\n  } catch (error) {\n    console.error('Error creating client:', error);\n    return NextResponse.json(\n      { error: 'Failed to create client' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QAEpD,MAAM,QAAa;YACjB,UAAU,WAAW,aAAa,QAAQ;QAC5C;QAEA,IAAI,QAAQ;YACV,MAAM,EAAE,GAAG;gBACT;oBAAE,WAAW;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBACvD;oBAAE,UAAU;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;aACvD;QACH;QAEA,MAAM,UAAU,MAAM,kHAAA,CAAA,KAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;YACvC;YACA,SAAS;gBACP,cAAc;oBACZ,QAAQ;wBACN,IAAI;wBACJ,aAAa;wBACb,aAAa;oBACf;oBACA,SAAS;wBACP,aAAa;oBACf;oBACA,MAAM;gBACR;gBACA,QAAQ;oBACN,QAAQ;wBACN,cAAc;oBAChB;gBACF;YACF;YACA,SAAS;gBACP;oBAAE,UAAU;gBAAM;gBAClB;oBAAE,WAAW;gBAAM;aACpB;YACD,MAAM;QACR;QAEA,gDAAgD;QAChD,MAAM,mBAAmB,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;gBAC9C,GAAG,MAAM;gBACT,aAAa,OAAO,YAAY,CAAC,EAAE,EAAE,eAAe;gBACpD,eAAe,OAAO,MAAM,CAAC,YAAY;gBACzC,QAAQ,OAAO,QAAQ,GAAG,WAAW;YACvC,CAAC;QAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA0B,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG;QAEpD,2BAA2B;QAC3B,IAAI,CAAC,aAAa,CAAC,UAAU;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkD,GAC3D;gBAAE,QAAQ;YAAI;QAElB;QAEA,0CAA0C;QAC1C,IAAI,OAAO;YACT,MAAM,iBAAiB,MAAM,kHAAA,CAAA,KAAE,CAAC,MAAM,CAAC,UAAU,CAAC;gBAChD,OAAO;oBAAE;gBAAM;YACjB;YAEA,IAAI,gBAAgB;gBAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAyC,GAClD;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,oBAAoB;QACpB,MAAM,SAAS,MAAM,kHAAA,CAAA,KAAE,CAAC,MAAM,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ;gBACA;gBACA,aAAa,cAAc,IAAI,KAAK,eAAe;gBACnD;YACF;QACF;QAEA,iCAAiC;QACjC,MAAM,kHAAA,CAAA,KAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;YACvB,MAAM;gBACJ,QAAQ,QAAQ,IAAI,CAAC,EAAE;gBACvB,QAAQ;gBACR,cAAc;gBACd,YAAY,OAAO,EAAE;gBACrB,SAAS;oBACP;oBACA;oBACA;gBACF;gBACA,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB;gBACrD,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;YAClD;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,QAAQ;YAAE,QAAQ;QAAI;IACjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA0B,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}