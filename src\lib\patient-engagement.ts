// Patient engagement system for automated check-ins and communications

export interface CheckInTemplate {
  id: string;
  name: string;
  description: string;
  questions: CheckInQuestion[];
  frequency: 'daily' | 'weekly' | 'biweekly' | 'monthly';
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CheckInQuestion {
  id: string;
  text: string;
  type: 'scale' | 'choice' | 'text' | 'boolean';
  required: boolean;
  options?: string[]; // For choice questions
  scaleMin?: number;  // For scale questions
  scaleMax?: number;  // For scale questions
  order: number;
}

export interface CheckInResponse {
  id: string;
  clientId: string;
  templateId: string;
  responses: QuestionResponse[];
  completedAt: Date;
  sentAt: Date;
  remindersSent: number;
}

export interface QuestionResponse {
  questionId: string;
  value: string | number | boolean;
  textValue?: string;
}

export interface EducationalContent {
  id: string;
  title: string;
  description: string;
  content: string;
  contentType: 'article' | 'video' | 'audio' | 'exercise';
  tags: string[];
  targetAudience: string[];
  estimatedDuration: number; // in minutes
  isActive: boolean;
  createdAt: Date;
}

export interface ClientEngagementPlan {
  id: string;
  clientId: string;
  checkInTemplates: string[]; // Template IDs
  educationalContent: string[]; // Content IDs
  communicationPreferences: {
    method: 'email' | 'sms' | 'portal';
    frequency: 'immediate' | 'daily' | 'weekly';
    timeOfDay?: string; // HH:MM format
    timezone?: string;
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

class PatientEngagementService {
  // Default check-in templates for Creative Arts Therapy
  private defaultTemplates: Omit<CheckInTemplate, 'id' | 'createdAt' | 'updatedAt'>[] = [
    {
      name: "Weekly Wellness Check",
      description: "General wellness and mood assessment",
      frequency: "weekly",
      isActive: true,
      questions: [
        {
          id: "mood",
          text: "How would you rate your overall mood this week?",
          type: "scale",
          required: true,
          scaleMin: 1,
          scaleMax: 10,
          order: 1
        },
        {
          id: "creative_engagement",
          text: "How much did you engage in creative activities this week?",
          type: "scale",
          required: true,
          scaleMin: 1,
          scaleMax: 10,
          order: 2
        },
        {
          id: "coping_strategies",
          text: "Did you use any coping strategies we discussed in therapy?",
          type: "boolean",
          required: true,
          order: 3
        },
        {
          id: "challenges",
          text: "What was the biggest challenge you faced this week?",
          type: "text",
          required: false,
          order: 4
        }
      ]
    },
    {
      name: "Art Therapy Progress",
      description: "Specific to art therapy interventions",
      frequency: "biweekly",
      isActive: true,
      questions: [
        {
          id: "art_creation",
          text: "How often did you create art outside of our sessions?",
          type: "choice",
          required: true,
          options: ["Not at all", "1-2 times", "3-4 times", "5+ times"],
          order: 1
        },
        {
          id: "emotional_expression",
          text: "How well were you able to express emotions through art?",
          type: "scale",
          required: true,
          scaleMin: 1,
          scaleMax: 10,
          order: 2
        },
        {
          id: "art_insights",
          text: "Did creating art help you gain any new insights about yourself?",
          type: "text",
          required: false,
          order: 3
        }
      ]
    }
  ];

  // Default educational content
  private defaultContent: Omit<EducationalContent, 'id' | 'createdAt'>[] = [
    {
      title: "Understanding Your Emotions Through Art",
      description: "Learn how creative expression can help you process and understand your emotions",
      content: "Art therapy provides a unique way to explore and express emotions that might be difficult to put into words...",
      contentType: "article",
      tags: ["emotions", "art therapy", "self-awareness"],
      targetAudience: ["anxiety", "depression", "trauma"],
      estimatedDuration: 5,
      isActive: true
    },
    {
      title: "Mindful Art Creation Exercise",
      description: "A guided exercise for creating art mindfully",
      content: "Find a quiet space and gather simple art materials. Begin by taking three deep breaths...",
      contentType: "exercise",
      tags: ["mindfulness", "art creation", "relaxation"],
      targetAudience: ["anxiety", "stress"],
      estimatedDuration: 15,
      isActive: true
    },
    {
      title: "Color and Mood Connection",
      description: "Understanding how colors relate to emotions and mood",
      content: "Colors have a profound impact on our emotions and can be powerful tools in therapy...",
      contentType: "article",
      tags: ["color therapy", "mood", "emotions"],
      targetAudience: ["depression", "mood disorders"],
      estimatedDuration: 7,
      isActive: true
    }
  ];

  // Generate personalized check-in based on client's therapy goals
  generatePersonalizedCheckIn(
    clientGoals: string[],
    recentSessions: any[],
    baseTemplate: CheckInTemplate
  ): CheckInTemplate {
    const personalizedQuestions = [...baseTemplate.questions];

    // Add goal-specific questions
    if (clientGoals.includes("emotional regulation")) {
      personalizedQuestions.push({
        id: "emotional_regulation",
        text: "How well were you able to manage difficult emotions this week?",
        type: "scale",
        required: true,
        scaleMin: 1,
        scaleMax: 10,
        order: personalizedQuestions.length + 1
      });
    }

    if (clientGoals.includes("self-expression")) {
      personalizedQuestions.push({
        id: "self_expression",
        text: "How comfortable did you feel expressing yourself this week?",
        type: "scale",
        required: true,
        scaleMin: 1,
        scaleMax: 10,
        order: personalizedQuestions.length + 1
      });
    }

    // Add questions based on recent session content
    const recentInterventions = recentSessions
      .flatMap(session => session.interventions?.split(',') || [])
      .map(i => i.trim().toLowerCase());

    if (recentInterventions.includes("music therapy")) {
      personalizedQuestions.push({
        id: "music_engagement",
        text: "Did you listen to or create music this week?",
        type: "boolean",
        required: false,
        order: personalizedQuestions.length + 1
      });
    }

    return {
      ...baseTemplate,
      questions: personalizedQuestions.sort((a, b) => a.order - b.order)
    };
  }

  // Recommend educational content based on client profile
  recommendContent(
    clientDiagnosis: string[],
    recentResponses: CheckInResponse[],
    completedContent: string[]
  ): EducationalContent[] {
    const recommendations: EducationalContent[] = [];

    // Filter content by target audience
    const relevantContent = this.defaultContent.filter(content =>
      content.targetAudience.some(audience => 
        clientDiagnosis.some(diagnosis => 
          diagnosis.toLowerCase().includes(audience.toLowerCase())
        )
      )
    );

    // Analyze recent check-in responses for additional recommendations
    const recentMoodScores = recentResponses
      .flatMap(response => response.responses)
      .filter(r => r.questionId === 'mood')
      .map(r => Number(r.value))
      .slice(-5); // Last 5 responses

    const averageMood = recentMoodScores.length > 0 
      ? recentMoodScores.reduce((sum, score) => sum + score, 0) / recentMoodScores.length
      : 5;

    // Recommend content based on mood trends
    if (averageMood < 4) {
      recommendations.push(...relevantContent.filter(c => 
        c.tags.includes('mood') || c.tags.includes('depression')
      ));
    }

    if (averageMood > 7) {
      recommendations.push(...relevantContent.filter(c => 
        c.tags.includes('self-awareness') || c.tags.includes('growth')
      ));
    }

    // Filter out already completed content
    return recommendations
      .filter(content => !completedContent.includes(content.id))
      .slice(0, 3); // Limit to 3 recommendations
  }

  // Schedule check-in delivery
  scheduleCheckIn(
    clientId: string,
    templateId: string,
    engagementPlan: ClientEngagementPlan
  ): Date {
    const now = new Date();
    const { frequency, timeOfDay, timezone } = engagementPlan.communicationPreferences;

    let scheduledDate = new Date(now);

    // Calculate next delivery date based on frequency
    switch (frequency) {
      case 'daily':
        scheduledDate.setDate(now.getDate() + 1);
        break;
      case 'weekly':
        scheduledDate.setDate(now.getDate() + 7);
        break;
      default:
        scheduledDate.setDate(now.getDate() + 1);
    }

    // Set specific time if provided
    if (timeOfDay) {
      const [hours, minutes] = timeOfDay.split(':').map(Number);
      scheduledDate.setHours(hours, minutes, 0, 0);
    }

    return scheduledDate;
  }

  // Generate engagement analytics
  generateEngagementAnalytics(
    clientId: string,
    responses: CheckInResponse[],
    timeRange: 'week' | 'month' | 'quarter' = 'month'
  ) {
    const now = new Date();
    const cutoffDate = new Date();

    switch (timeRange) {
      case 'week':
        cutoffDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        cutoffDate.setMonth(now.getMonth() - 1);
        break;
      case 'quarter':
        cutoffDate.setMonth(now.getMonth() - 3);
        break;
    }

    const relevantResponses = responses.filter(r => 
      new Date(r.completedAt) >= cutoffDate
    );

    const totalSent = responses.filter(r => 
      new Date(r.sentAt) >= cutoffDate
    ).length;

    const responseRate = totalSent > 0 
      ? (relevantResponses.length / totalSent) * 100 
      : 0;

    const averageResponseTime = relevantResponses.length > 0
      ? relevantResponses.reduce((sum, r) => {
          const responseTime = new Date(r.completedAt).getTime() - new Date(r.sentAt).getTime();
          return sum + responseTime;
        }, 0) / relevantResponses.length / (1000 * 60 * 60) // Convert to hours
      : 0;

    // Analyze mood trends
    const moodResponses = relevantResponses
      .flatMap(r => r.responses)
      .filter(r => r.questionId === 'mood')
      .map(r => ({ value: Number(r.value), date: r.questionId }));

    const moodTrend = moodResponses.length > 1
      ? moodResponses[moodResponses.length - 1].value - moodResponses[0].value
      : 0;

    return {
      timeRange,
      responseRate: Math.round(responseRate),
      averageResponseTime: Math.round(averageResponseTime * 10) / 10,
      totalResponses: relevantResponses.length,
      moodTrend: Math.round(moodTrend * 10) / 10,
      engagementLevel: responseRate > 70 ? 'high' : responseRate > 40 ? 'medium' : 'low'
    };
  }
}

export const patientEngagement = new PatientEngagementService();
