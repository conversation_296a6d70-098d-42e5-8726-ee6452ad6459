"use client";

import { useState, useRef } from "react";
import Link from "next/link";
import {
  ArrowLeft,
  Upload,
  Image as ImageIcon,
  Video,
  Music,
  Sparkles,
  Save,
  Eye,
  X,
  Loader2,
  CheckCircle
} from "lucide-react";

interface MultimediaFile {
  id: string;
  file: File;
  type: 'image' | 'video' | 'audio';
  preview?: string;
  aiDescription?: string;
  isGeneratingAI?: boolean;
}

export default function NewSessionNote() {
  const [selectedClient, setSelectedClient] = useState("");
  const [sessionDate, setSessionDate] = useState(new Date().toISOString().split('T')[0]);
  const [sessionDuration, setSessionDuration] = useState("");
  const [noteType, setNoteType] = useState("SOAP");
  
  // SOAP Note fields
  const [subjective, setSubjective] = useState("");
  const [objective, setObjective] = useState("");
  const [assessment, setAssessment] = useState("");
  const [plan, setPlan] = useState("");
  const [interventions, setInterventions] = useState("");
  const [clientMood, setClientMood] = useState("");
  
  // Multimedia handling
  const [multimediaFiles, setMultimediaFiles] = useState<MultimediaFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // AI suggestions
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState<{[key: string]: string}>({});

  // Mock client data
  const clients = [
    { id: "1", name: "Sarah M." },
    { id: "2", name: "Michael R." },
    { id: "3", name: "Emma L." },
    { id: "4", name: "David K." }
  ];

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    setIsUploading(true);
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const fileType = getFileType(file.type);
      
      if (fileType) {
        const newFile: MultimediaFile = {
          id: Date.now().toString() + i,
          file,
          type: fileType,
          preview: fileType === 'image' ? URL.createObjectURL(file) : undefined
        };
        
        setMultimediaFiles(prev => [...prev, newFile]);
      }
    }
    
    setIsUploading(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const getFileType = (mimeType: string): 'image' | 'video' | 'audio' | null => {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('audio/')) return 'audio';
    return null;
  };

  const removeFile = (fileId: string) => {
    setMultimediaFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const generateAIDescription = async (fileId: string) => {
    const file = multimediaFiles.find(f => f.id === fileId);
    if (!file) return;

    setMultimediaFiles(prev => 
      prev.map(f => f.id === fileId ? { ...f, isGeneratingAI: true } : f)
    );

    // Simulate AI API call
    setTimeout(() => {
      const mockDescriptions = {
        image: "Client created a vibrant painting using warm colors (reds, oranges, yellows) with bold, expressive brushstrokes. The composition shows a central figure surrounded by swirling patterns, suggesting emotional expression and creative engagement.",
        video: "Client demonstrated fluid, rhythmic movements during the dance therapy session. Movements progressed from tentative, small gestures to more expansive, confident expressions, indicating increased comfort and emotional release.",
        audio: "Client's musical expression included steady drumming patterns with occasional dynamic changes. The rhythm became more structured throughout the session, suggesting improved focus and emotional regulation."
      };

      setMultimediaFiles(prev => 
        prev.map(f => f.id === fileId ? { 
          ...f, 
          isGeneratingAI: false,
          aiDescription: mockDescriptions[f.type] 
        } : f)
      );
    }, 2000);
  };

  const generateAISuggestion = async (field: string, context: string) => {
    console.log('Generating AI suggestion for field:', field, 'Context:', context);
    setIsGeneratingAI(true);

    try {
      // Call the real AI API
      const response = await fetch('/api/ai/suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: field.toUpperCase(),
          context: context || `Generate a ${field} section for a Creative Arts Therapy session note.`,
          sessionInfo: {
            clientAge: 'adult',
            sessionType: 'Creative Arts Therapy',
            duration: sessionDuration || 45,
            noteType: noteType
          }
        }),
      });

      if (response.ok) {
        const data = await response.json();
        console.log('AI API response for field:', field, 'Data:', data);
        setAiSuggestions(prev => ({
          ...prev,
          [field]: data.suggestion || "AI suggestion generated based on session context."
        }));
      } else {
        console.log('AI API failed with status:', response.status);
        // Fallback to mock suggestions if API fails
        const mockSuggestions: {[key: string]: string} = {
          objective: "Client engaged actively in art-making process, demonstrating sustained attention for 30 minutes. Chose vibrant colors and created abstract forms with confident brushstrokes. Maintained appropriate eye contact and responded verbally to therapeutic prompts.",
          assessment: "Client shows continued progress in emotional expression through creative media. Increased confidence in artistic choices and willingness to discuss emotional content of artwork. Mood appears stable with positive engagement in therapeutic process.",
          plan: "Continue weekly art therapy sessions focusing on emotional expression through color and form. Introduce new media (clay work) to explore tactile processing. Schedule follow-up with psychiatrist to discuss medication adjustment timeline."
        };

        setAiSuggestions(prev => ({
          ...prev,
          [field]: mockSuggestions[field] || "AI suggestion generated based on session context."
        }));
      }
    } catch (error) {
      console.error('Error generating AI suggestion:', error);

      // Fallback to mock suggestions on error
      const mockSuggestions: {[key: string]: string} = {
        objective: "Client engaged actively in art-making process, demonstrating sustained attention for 30 minutes. Chose vibrant colors and created abstract forms with confident brushstrokes. Maintained appropriate eye contact and responded verbally to therapeutic prompts.",
        assessment: "Client shows continued progress in emotional expression through creative media. Increased confidence in artistic choices and willingness to discuss emotional content of artwork. Mood appears stable with positive engagement in therapeutic process.",
        plan: "Continue weekly art therapy sessions focusing on emotional expression through color and form. Introduce new media (clay work) to explore tactile processing. Schedule follow-up with psychiatrist to discuss medication adjustment timeline."
      };

      setAiSuggestions(prev => ({
        ...prev,
        [field]: mockSuggestions[field] || "AI suggestion generated based on session context."
      }));
    } finally {
      setIsGeneratingAI(false);
    }
  };

  const insertAISuggestion = (field: string) => {
    const suggestion = aiSuggestions[field];
    console.log('Inserting AI suggestion for field:', field, 'Suggestion:', suggestion);

    if (!suggestion) {
      console.log('No suggestion found for field:', field);
      return;
    }

    switch (field) {
      case 'objective':
        console.log('Setting objective field');
        setObjective(prev => prev + (prev ? '\n\n' : '') + suggestion);
        break;
      case 'assessment':
        console.log('Setting assessment field');
        setAssessment(prev => prev + (prev ? '\n\n' : '') + suggestion);
        break;
      case 'plan':
        console.log('Setting plan field');
        setPlan(prev => prev + (prev ? '\n\n' : '') + suggestion);
        break;
      default:
        console.log('Unknown field:', field);
    }

    // Clear the suggestion after inserting
    setAiSuggestions(prev => ({ ...prev, [field]: '' }));
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image':
        return <ImageIcon className="h-5 w-5 text-green-600" />;
      case 'video':
        return <Video className="h-5 w-5 text-blue-600" />;
      case 'audio':
        return <Music className="h-5 w-5 text-purple-600" />;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center">
              <Link href="/dashboard" className="mr-4">
                <ArrowLeft className="h-6 w-6 text-gray-400 hover:text-gray-600" />
              </Link>
              <h1 className="text-2xl font-bold text-gray-900">New Session Note</h1>
            </div>
            <div className="flex space-x-3">
              <button
                type="button"
                className="btn-secondary"
              >
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </button>
              <button
                type="button"
                className="btn-secondary"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Draft
              </button>
              <button
                type="button"
                className="btn-primary"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Save & Finalize
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Session Information */}
            <div className="card">
              <div className="card-header">
                <h3 className="card-title">Session Information</h3>
                <p className="card-subtitle">Basic details about this therapy session</p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="form-label">
                    Client *
                  </label>
                  <select
                    value={selectedClient}
                    onChange={(e) => setSelectedClient(e.target.value)}
                    className="form-select"
                    required
                  >
                    <option value="">Choose a client...</option>
                    {clients.map(client => (
                      <option key={client.id} value={client.id}>
                        {client.name}
                      </option>
                    ))}
                  </select>
                  {!selectedClient && (
                    <p className="form-help">Select the client for this therapy session</p>
                  )}
                </div>
                <div>
                  <label className="form-label">
                    Session Date *
                  </label>
                  <input
                    type="date"
                    value={sessionDate}
                    onChange={(e) => setSessionDate(e.target.value)}
                    className="form-input"
                    required
                  />
                  <p className="form-help">Date when the therapy session occurred</p>
                </div>
                <div>
                  <label className="form-label">
                    Duration (minutes)
                  </label>
                  <input
                    type="number"
                    value={sessionDuration}
                    onChange={(e) => setSessionDuration(e.target.value)}
                    placeholder="Enter session length (e.g., 45)"
                    className="form-input"
                    min="1"
                    max="300"
                  />
                  <p className="form-help">Length of the therapy session in minutes</p>
                </div>
                <div>
                  <label className="form-label">
                    Note Type
                  </label>
                  <select
                    value={noteType}
                    onChange={(e) => setNoteType(e.target.value)}
                    className="form-select"
                  >
                    <option value="SOAP">SOAP Note</option>
                    <option value="DAP">DAP Note</option>
                    <option value="BIRP">BIRP Note</option>
                  </select>
                  <p className="form-help">Documentation format for this session</p>
                </div>
              </div>
            </div>

            {/* SOAP Note Fields */}
            <div className="card">
              <div className="card-header">
                <h3 className="card-title">SOAP Note Documentation</h3>
                <p className="card-subtitle">Structured clinical documentation for this therapy session</p>
              </div>

              {/* Subjective */}
              <div className="mb-6">
                <label className="form-label">
                  Subjective (Client's reported experience)
                </label>
                <textarea
                  value={subjective}
                  onChange={(e) => setSubjective(e.target.value)}
                  rows={3}
                  placeholder="Document what the client reports about their mood, feelings, concerns, and experiences..."
                  className="form-textarea"
                />
                <p className="form-help">Record the client's own words and self-reported experiences</p>
              </div>

              {/* Objective */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <label className="form-label">
                    Objective (Observable behaviors and activities)
                  </label>
                  <button
                    type="button"
                    onClick={() => generateAISuggestion('objective', objective)}
                    disabled={isGeneratingAI}
                    className="btn-secondary text-xs"
                  >
                    {isGeneratingAI ? (
                      <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                    ) : (
                      <Sparkles className="h-3 w-3 mr-1" />
                    )}
                    AI Suggest
                  </button>
                </div>
                <textarea
                  value={objective}
                  onChange={(e) => setObjective(e.target.value)}
                  rows={4}
                  placeholder="Describe observable behaviors, creative process, engagement level, materials used, and therapeutic activities..."
                  className="form-textarea"
                />
                <p className="form-help">Document what you observed during the session - behaviors, activities, and creative expressions</p>
                {aiSuggestions.objective && (
                  <div className="mt-3 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-blue-900 mb-1">AI Suggestion:</p>
                        <p className="text-sm text-blue-800 leading-relaxed">{aiSuggestions.objective}</p>
                      </div>
                      <div className="flex space-x-2 ml-4">
                        <button
                          onClick={() => insertAISuggestion('objective')}
                          className="btn-primary text-xs px-3 py-1"
                        >
                          Insert
                        </button>
                        <button
                          onClick={() => setAiSuggestions(prev => ({ ...prev, objective: '' }))}
                          className="btn-secondary text-xs px-3 py-1"
                        >
                          Dismiss
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Assessment */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <label className="form-label">
                    Assessment (Clinical interpretation)
                  </label>
                  <button
                    type="button"
                    onClick={() => generateAISuggestion('assessment', assessment)}
                    disabled={isGeneratingAI}
                    className="btn-secondary text-xs"
                  >
                    {isGeneratingAI ? (
                      <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                    ) : (
                      <Sparkles className="h-3 w-3 mr-1" />
                    )}
                    AI Suggest
                  </button>
                </div>
                <textarea
                  value={assessment}
                  onChange={(e) => setAssessment(e.target.value)}
                  rows={4}
                  placeholder="Provide clinical assessment, progress toward therapeutic goals, insights about client's emotional state and creative expression..."
                  className="form-textarea"
                />
                <p className="form-help">Your professional interpretation of the session and client's progress</p>
                {aiSuggestions.assessment && (
                  <div className="mt-3 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-blue-900 mb-1">AI Suggestion:</p>
                        <p className="text-sm text-blue-800 leading-relaxed">{aiSuggestions.assessment}</p>
                      </div>
                      <div className="flex space-x-2 ml-4">
                        <button
                          onClick={() => insertAISuggestion('assessment')}
                          className="btn-primary text-xs px-3 py-1"
                        >
                          Insert
                        </button>
                        <button
                          onClick={() => setAiSuggestions(prev => ({ ...prev, assessment: '' }))}
                          className="btn-secondary text-xs px-3 py-1"
                        >
                          Dismiss
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Plan */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <label className="form-label">
                    Plan (Treatment plan and next steps)
                  </label>
                  <button
                    type="button"
                    onClick={() => generateAISuggestion('plan', plan)}
                    disabled={isGeneratingAI}
                    className="btn-secondary text-xs"
                  >
                    {isGeneratingAI ? (
                      <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                    ) : (
                      <Sparkles className="h-3 w-3 mr-1" />
                    )}
                    AI Suggest
                  </button>
                </div>
                <textarea
                  value={plan}
                  onChange={(e) => setPlan(e.target.value)}
                  rows={4}
                  placeholder="Outline treatment plan, goals for next session, recommendations, and follow-up activities..."
                  className="form-textarea"
                />
                <p className="form-help">Document your treatment plan and next steps for continued therapy</p>
                {aiSuggestions.plan && (
                  <div className="mt-3 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-blue-900 mb-1">AI Suggestion:</p>
                        <p className="text-sm text-blue-800 leading-relaxed">{aiSuggestions.plan}</p>
                      </div>
                      <div className="flex space-x-2 ml-4">
                        <button
                          onClick={() => insertAISuggestion('plan')}
                          className="btn-primary text-xs px-3 py-1"
                        >
                          Insert
                        </button>
                        <button
                          onClick={() => setAiSuggestions(prev => ({ ...prev, plan: '' }))}
                          className="btn-secondary text-xs px-3 py-1"
                        >
                          Dismiss
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Additional Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="form-label">
                    Interventions Used
                  </label>
                  <input
                    type="text"
                    value={interventions}
                    onChange={(e) => setInterventions(e.target.value)}
                    placeholder="e.g., Art therapy, music therapy, movement therapy"
                    className="form-input"
                  />
                  <p className="form-help">List the therapeutic interventions used in this session</p>
                </div>
                <div>
                  <label className="form-label">
                    Client Mood/Presentation
                  </label>
                  <input
                    type="text"
                    value={clientMood}
                    onChange={(e) => setClientMood(e.target.value)}
                    placeholder="e.g., Calm, anxious, engaged, withdrawn"
                    className="form-input"
                  />
                  <p className="form-help">Describe the client's overall mood and presentation</p>
                </div>
              </div>
            </div>
          </div>

          {/* Multimedia Upload Sidebar */}
          <div className="space-y-6">
            {/* Upload Section */}
            <div className="card">
              <div className="card-header">
                <h3 className="card-title">Multimedia Files</h3>
                <p className="card-subtitle">Upload session artwork, recordings, and media</p>
              </div>

              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-indigo-400 transition-colors duration-200">
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept="image/*,video/*,audio/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />
                <Upload className="h-10 w-10 text-gray-400 mx-auto mb-3" />
                <p className="text-sm font-medium text-gray-700 mb-1">
                  Upload session media
                </p>
                <p className="text-xs text-gray-500 mb-4">
                  Photos of artwork, audio recordings, or video documentation
                </p>
                <button
                  type="button"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isUploading}
                  className="btn-primary"
                >
                  {isUploading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Upload className="h-4 w-4 mr-2" />
                  )}
                  Choose Files
                </button>
                <p className="text-xs text-gray-500 mt-3">
                  <strong>Supported:</strong> JPG, PNG, GIF, MP4, MOV, AVI, MP3, WAV, M4A<br />
                  <strong>Max size:</strong> 20MB per file
                </p>
              </div>

              {/* Uploaded Files */}
              {multimediaFiles.length > 0 && (
                <div className="mt-6 space-y-4">
                  <h4 className="text-sm font-medium text-gray-900">Uploaded Files</h4>
                  {multimediaFiles.map((file) => (
                    <div key={file.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          {getFileIcon(file.type)}
                          <span className="text-sm font-medium text-gray-900 truncate">
                            {file.file.name}
                          </span>
                        </div>
                        <button
                          onClick={() => removeFile(file.id)}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>

                      {file.type === 'image' && file.preview && (
                        <img
                          src={file.preview}
                          alt="Preview"
                          className="w-full h-32 object-cover rounded-md mb-2"
                        />
                      )}

                      <p className="text-xs text-gray-500 mb-2">
                        {(file.file.size / 1024 / 1024).toFixed(2)} MB
                      </p>

                      {/* AI Description */}
                      {file.aiDescription ? (
                        <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                          <p className="text-xs font-medium text-blue-800 mb-1">AI Description:</p>
                          <p className="text-xs text-blue-700">{file.aiDescription}</p>
                        </div>
                      ) : (
                        <button
                          onClick={() => generateAIDescription(file.id)}
                          disabled={file.isGeneratingAI}
                          className="w-full inline-flex items-center justify-center px-3 py-2 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200 disabled:opacity-50"
                        >
                          {file.isGeneratingAI ? (
                            <>
                              <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                              Generating...
                            </>
                          ) : (
                            <>
                              <Sparkles className="h-3 w-3 mr-1" />
                              Generate AI Description
                            </>
                          )}
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Tips */}
            <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-5">
              <h4 className="text-sm font-semibold text-indigo-900 mb-3 flex items-center">
                <span className="text-base mr-2">💡</span>
                Documentation Tips
              </h4>
              <ul className="text-sm text-indigo-800 space-y-2">
                <li className="flex items-start">
                  <span className="text-indigo-600 mr-2 mt-0.5">•</span>
                  Use AI suggestions to generate professional clinical language
                </li>
                <li className="flex items-start">
                  <span className="text-indigo-600 mr-2 mt-0.5">•</span>
                  Upload photos of client artwork to document creative expression
                </li>
                <li className="flex items-start">
                  <span className="text-indigo-600 mr-2 mt-0.5">•</span>
                  Record audio for music therapy sessions and verbal processing
                </li>
                <li className="flex items-start">
                  <span className="text-indigo-600 mr-2 mt-0.5">•</span>
                  All data is encrypted and HIPAA-compliant for client privacy
                </li>
              </ul>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
