"use client";

import { useState, useRef } from "react";
import Link from "next/link";
import { 
  ArrowLeft,
  Upload,
  Image as ImageIcon,
  Video,
  Music,
  Sparkles,
  Save,
  Eye,
  X,
  Loader2
} from "lucide-react";

interface MultimediaFile {
  id: string;
  file: File;
  type: 'image' | 'video' | 'audio';
  preview?: string;
  aiDescription?: string;
  isGeneratingAI?: boolean;
}

export default function NewSessionNote() {
  const [selectedClient, setSelectedClient] = useState("");
  const [sessionDate, setSessionDate] = useState(new Date().toISOString().split('T')[0]);
  const [sessionDuration, setSessionDuration] = useState("");
  const [noteType, setNoteType] = useState("SOAP");
  
  // SOAP Note fields
  const [subjective, setSubjective] = useState("");
  const [objective, setObjective] = useState("");
  const [assessment, setAssessment] = useState("");
  const [plan, setPlan] = useState("");
  const [interventions, setInterventions] = useState("");
  const [clientMood, setClientMood] = useState("");
  
  // Multimedia handling
  const [multimediaFiles, setMultimediaFiles] = useState<MultimediaFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // AI suggestions
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState<{[key: string]: string}>({});

  // Mock client data
  const clients = [
    { id: "1", name: "Sarah M." },
    { id: "2", name: "Michael R." },
    { id: "3", name: "Emma L." },
    { id: "4", name: "David K." }
  ];

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    setIsUploading(true);
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const fileType = getFileType(file.type);
      
      if (fileType) {
        const newFile: MultimediaFile = {
          id: Date.now().toString() + i,
          file,
          type: fileType,
          preview: fileType === 'image' ? URL.createObjectURL(file) : undefined
        };
        
        setMultimediaFiles(prev => [...prev, newFile]);
      }
    }
    
    setIsUploading(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const getFileType = (mimeType: string): 'image' | 'video' | 'audio' | null => {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('audio/')) return 'audio';
    return null;
  };

  const removeFile = (fileId: string) => {
    setMultimediaFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const generateAIDescription = async (fileId: string) => {
    const file = multimediaFiles.find(f => f.id === fileId);
    if (!file) return;

    setMultimediaFiles(prev => 
      prev.map(f => f.id === fileId ? { ...f, isGeneratingAI: true } : f)
    );

    // Simulate AI API call
    setTimeout(() => {
      const mockDescriptions = {
        image: "Client created a vibrant painting using warm colors (reds, oranges, yellows) with bold, expressive brushstrokes. The composition shows a central figure surrounded by swirling patterns, suggesting emotional expression and creative engagement.",
        video: "Client demonstrated fluid, rhythmic movements during the dance therapy session. Movements progressed from tentative, small gestures to more expansive, confident expressions, indicating increased comfort and emotional release.",
        audio: "Client's musical expression included steady drumming patterns with occasional dynamic changes. The rhythm became more structured throughout the session, suggesting improved focus and emotional regulation."
      };

      setMultimediaFiles(prev => 
        prev.map(f => f.id === fileId ? { 
          ...f, 
          isGeneratingAI: false,
          aiDescription: mockDescriptions[f.type] 
        } : f)
      );
    }, 2000);
  };

  const generateAISuggestion = async (field: string, context: string) => {
    setIsGeneratingAI(true);

    // Simulate AI API call
    setTimeout(() => {
      const mockSuggestions: {[key: string]: string} = {
        objective: "Client engaged actively in art-making process, demonstrating sustained attention for 30 minutes. Chose vibrant colors and created abstract forms with confident brushstrokes. Maintained appropriate eye contact and responded verbally to therapeutic prompts.",
        assessment: "Client shows continued progress in emotional expression through creative media. Increased confidence in artistic choices and willingness to discuss emotional content of artwork. Mood appears stable with positive engagement in therapeutic process.",
        plan: "Continue weekly art therapy sessions focusing on emotional expression through color and form. Introduce new media (clay work) to explore tactile processing. Schedule follow-up with psychiatrist to discuss medication adjustment timeline."
      };

      setAiSuggestions(prev => ({
        ...prev,
        [field]: mockSuggestions[field] || "AI suggestion generated based on session context."
      }));
      setIsGeneratingAI(false);
    }, 1500);
  };

  const insertAISuggestion = (field: string) => {
    const suggestion = aiSuggestions[field];
    if (!suggestion) return;

    switch (field) {
      case 'objective':
        setObjective(prev => prev + (prev ? '\n\n' : '') + suggestion);
        break;
      case 'assessment':
        setAssessment(prev => prev + (prev ? '\n\n' : '') + suggestion);
        break;
      case 'plan':
        setPlan(prev => prev + (prev ? '\n\n' : '') + suggestion);
        break;
    }
    
    // Clear the suggestion after inserting
    setAiSuggestions(prev => ({ ...prev, [field]: '' }));
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image':
        return <ImageIcon className="h-5 w-5 text-green-600" />;
      case 'video':
        return <Video className="h-5 w-5 text-blue-600" />;
      case 'audio':
        return <Music className="h-5 w-5 text-purple-600" />;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center">
              <Link href="/dashboard" className="mr-4">
                <ArrowLeft className="h-6 w-6 text-gray-400 hover:text-gray-600" />
              </Link>
              <h1 className="text-2xl font-bold text-gray-900">New Session Note</h1>
            </div>
            <div className="flex space-x-3">
              <button
                type="button"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </button>
              <button
                type="button"
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Note
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Session Information */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Session Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Client
                  </label>
                  <select
                    value={selectedClient}
                    onChange={(e) => setSelectedClient(e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  >
                    <option value="">Select a client</option>
                    {clients.map(client => (
                      <option key={client.id} value={client.id}>
                        {client.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Session Date
                  </label>
                  <input
                    type="date"
                    value={sessionDate}
                    onChange={(e) => setSessionDate(e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Duration (minutes)
                  </label>
                  <input
                    type="number"
                    value={sessionDuration}
                    onChange={(e) => setSessionDuration(e.target.value)}
                    placeholder="45"
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Note Type
                  </label>
                  <select
                    value={noteType}
                    onChange={(e) => setNoteType(e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  >
                    <option value="SOAP">SOAP Note</option>
                    <option value="DAP">DAP Note</option>
                    <option value="BIRP">BIRP Note</option>
                  </select>
                </div>
              </div>
            </div>

            {/* SOAP Note Fields */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">SOAP Note</h3>

              {/* Subjective */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Subjective (Client's reported experience)
                </label>
                <textarea
                  value={subjective}
                  onChange={(e) => setSubjective(e.target.value)}
                  rows={3}
                  placeholder="Client's self-reported mood, feelings, concerns..."
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>

              {/* Objective */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Objective (Observable behaviors and activities)
                  </label>
                  <button
                    type="button"
                    onClick={() => generateAISuggestion('objective', objective)}
                    disabled={isGeneratingAI}
                    className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200 disabled:opacity-50"
                  >
                    {isGeneratingAI ? (
                      <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                    ) : (
                      <Sparkles className="h-3 w-3 mr-1" />
                    )}
                    AI Suggest
                  </button>
                </div>
                <textarea
                  value={objective}
                  onChange={(e) => setObjective(e.target.value)}
                  rows={4}
                  placeholder="Observable behaviors, creative process, engagement level..."
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
                {aiSuggestions.objective && (
                  <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <p className="text-sm text-blue-800">{aiSuggestions.objective}</p>
                      </div>
                      <div className="flex space-x-2 ml-3">
                        <button
                          onClick={() => insertAISuggestion('objective')}
                          className="text-xs text-blue-600 hover:text-blue-800"
                        >
                          Insert
                        </button>
                        <button
                          onClick={() => setAiSuggestions(prev => ({ ...prev, objective: '' }))}
                          className="text-xs text-gray-500 hover:text-gray-700"
                        >
                          Dismiss
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Assessment */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Assessment (Clinical interpretation)
                  </label>
                  <button
                    type="button"
                    onClick={() => generateAISuggestion('assessment', assessment)}
                    disabled={isGeneratingAI}
                    className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200 disabled:opacity-50"
                  >
                    {isGeneratingAI ? (
                      <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                    ) : (
                      <Sparkles className="h-3 w-3 mr-1" />
                    )}
                    AI Suggest
                  </button>
                </div>
                <textarea
                  value={assessment}
                  onChange={(e) => setAssessment(e.target.value)}
                  rows={4}
                  placeholder="Clinical assessment, progress toward goals, therapeutic insights..."
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
                {aiSuggestions.assessment && (
                  <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <p className="text-sm text-blue-800">{aiSuggestions.assessment}</p>
                      </div>
                      <div className="flex space-x-2 ml-3">
                        <button
                          onClick={() => insertAISuggestion('assessment')}
                          className="text-xs text-blue-600 hover:text-blue-800"
                        >
                          Insert
                        </button>
                        <button
                          onClick={() => setAiSuggestions(prev => ({ ...prev, assessment: '' }))}
                          className="text-xs text-gray-500 hover:text-gray-700"
                        >
                          Dismiss
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Plan */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Plan (Treatment plan and next steps)
                  </label>
                  <button
                    type="button"
                    onClick={() => generateAISuggestion('plan', plan)}
                    disabled={isGeneratingAI}
                    className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200 disabled:opacity-50"
                  >
                    {isGeneratingAI ? (
                      <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                    ) : (
                      <Sparkles className="h-3 w-3 mr-1" />
                    )}
                    AI Suggest
                  </button>
                </div>
                <textarea
                  value={plan}
                  onChange={(e) => setPlan(e.target.value)}
                  rows={4}
                  placeholder="Treatment plan, goals for next session, recommendations..."
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
                {aiSuggestions.plan && (
                  <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <p className="text-sm text-blue-800">{aiSuggestions.plan}</p>
                      </div>
                      <div className="flex space-x-2 ml-3">
                        <button
                          onClick={() => insertAISuggestion('plan')}
                          className="text-xs text-blue-600 hover:text-blue-800"
                        >
                          Insert
                        </button>
                        <button
                          onClick={() => setAiSuggestions(prev => ({ ...prev, plan: '' }))}
                          className="text-xs text-gray-500 hover:text-gray-700"
                        >
                          Dismiss
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Additional Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Interventions Used
                  </label>
                  <input
                    type="text"
                    value={interventions}
                    onChange={(e) => setInterventions(e.target.value)}
                    placeholder="Art therapy, music therapy, movement..."
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Client Mood/Presentation
                  </label>
                  <input
                    type="text"
                    value={clientMood}
                    onChange={(e) => setClientMood(e.target.value)}
                    placeholder="Calm, anxious, engaged, withdrawn..."
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Multimedia Upload Sidebar */}
          <div className="space-y-6">
            {/* Upload Section */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Multimedia Files</h3>

              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept="image/*,video/*,audio/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />
                <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600 mb-2">
                  Upload photos, videos, or audio recordings
                </p>
                <button
                  type="button"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isUploading}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 disabled:opacity-50"
                >
                  {isUploading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Upload className="h-4 w-4 mr-2" />
                  )}
                  Choose Files
                </button>
                <p className="text-xs text-gray-500 mt-2">
                  Max 20MB per file. Supports JPG, PNG, MP4, MOV, MP3, WAV
                </p>
              </div>

              {/* Uploaded Files */}
              {multimediaFiles.length > 0 && (
                <div className="mt-6 space-y-4">
                  <h4 className="text-sm font-medium text-gray-900">Uploaded Files</h4>
                  {multimediaFiles.map((file) => (
                    <div key={file.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          {getFileIcon(file.type)}
                          <span className="text-sm font-medium text-gray-900 truncate">
                            {file.file.name}
                          </span>
                        </div>
                        <button
                          onClick={() => removeFile(file.id)}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>

                      {file.type === 'image' && file.preview && (
                        <img
                          src={file.preview}
                          alt="Preview"
                          className="w-full h-32 object-cover rounded-md mb-2"
                        />
                      )}

                      <p className="text-xs text-gray-500 mb-2">
                        {(file.file.size / 1024 / 1024).toFixed(2)} MB
                      </p>

                      {/* AI Description */}
                      {file.aiDescription ? (
                        <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                          <p className="text-xs font-medium text-blue-800 mb-1">AI Description:</p>
                          <p className="text-xs text-blue-700">{file.aiDescription}</p>
                        </div>
                      ) : (
                        <button
                          onClick={() => generateAIDescription(file.id)}
                          disabled={file.isGeneratingAI}
                          className="w-full inline-flex items-center justify-center px-3 py-2 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200 disabled:opacity-50"
                        >
                          {file.isGeneratingAI ? (
                            <>
                              <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                              Generating...
                            </>
                          ) : (
                            <>
                              <Sparkles className="h-3 w-3 mr-1" />
                              Generate AI Description
                            </>
                          )}
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Tips */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-800 mb-2">💡 Tips</h4>
              <ul className="text-xs text-blue-700 space-y-1">
                <li>• Use AI suggestions to quickly generate clinical descriptions</li>
                <li>• Upload artwork photos to document creative expression</li>
                <li>• Record audio for music therapy sessions</li>
                <li>• All files are encrypted and HIPAA-compliant</li>
              </ul>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
