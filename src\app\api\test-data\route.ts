import { NextResponse } from 'next/server';
import { db } from '@/lib/db';

export async function GET() {
  try {
    // Get sample data to verify database is working
    const users = await db.user.count();
    const clients = await db.client.count();
    const sessionNotes = await db.sessionNote.count();
    const customMetrics = await db.customMetric.count();
    
    // Get a sample session note with relations
    const sampleNote = await db.sessionNote.findFirst({
      include: {
        client: {
          select: {
            firstName: true,
            lastName: true
          }
        },
        multimedia: true,
        aiSuggestions: true,
        metricMeasurements: {
          include: {
            metric: true
          }
        }
      }
    });
    
    return NextResponse.json({
      status: 'success',
      counts: {
        users,
        clients,
        sessionNotes,
        customMetrics
      },
      sampleData: {
        note: sampleNote ? {
          id: sampleNote.id,
          clientName: `${sampleNote.client.firstName} ${sampleNote.client.lastName}`,
          sessionDate: sampleNote.sessionDate,
          noteType: sampleNote.noteType,
          isFinalized: sampleNote.isFinalized,
          hasMultimedia: sampleNote.multimedia.length > 0,
          hasAiSuggestions: sampleNote.aiSuggestions.length > 0,
          hasMetrics: sampleNote.metricMeasurements.length > 0
        } : null
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Database test error:', error);
    return NextResponse.json({
      status: 'error',
      error: error.message
    }, { status: 500 });
  }
}
