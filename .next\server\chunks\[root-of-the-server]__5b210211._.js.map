{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VistaNotes/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const db =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = db\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,KACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VistaNotes/src/app/api/test-data/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { db } from '@/lib/db';\n\nexport async function GET() {\n  try {\n    // Get sample data to verify database is working\n    const users = await db.user.count();\n    const clients = await db.client.count();\n    const sessionNotes = await db.sessionNote.count();\n    const customMetrics = await db.customMetric.count();\n    \n    // Get a sample session note with relations\n    const sampleNote = await db.sessionNote.findFirst({\n      include: {\n        client: {\n          select: {\n            firstName: true,\n            lastName: true\n          }\n        },\n        multimedia: true,\n        aiSuggestions: true,\n        metricMeasurements: {\n          include: {\n            metric: true\n          }\n        }\n      }\n    });\n    \n    return NextResponse.json({\n      status: 'success',\n      counts: {\n        users,\n        clients,\n        sessionNotes,\n        customMetrics\n      },\n      sampleData: {\n        note: sampleNote ? {\n          id: sampleNote.id,\n          clientName: `${sampleNote.client.firstName} ${sampleNote.client.lastName}`,\n          sessionDate: sampleNote.sessionDate,\n          noteType: sampleNote.noteType,\n          isFinalized: sampleNote.isFinalized,\n          hasMultimedia: sampleNote.multimedia.length > 0,\n          hasAiSuggestions: sampleNote.aiSuggestions.length > 0,\n          hasMetrics: sampleNote.metricMeasurements.length > 0\n        } : null\n      },\n      timestamp: new Date().toISOString()\n    });\n  } catch (error) {\n    console.error('Database test error:', error);\n    return NextResponse.json({\n      status: 'error',\n      error: error.message\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,gDAAgD;QAChD,MAAM,QAAQ,MAAM,kHAAA,CAAA,KAAE,CAAC,IAAI,CAAC,KAAK;QACjC,MAAM,UAAU,MAAM,kHAAA,CAAA,KAAE,CAAC,MAAM,CAAC,KAAK;QACrC,MAAM,eAAe,MAAM,kHAAA,CAAA,KAAE,CAAC,WAAW,CAAC,KAAK;QAC/C,MAAM,gBAAgB,MAAM,kHAAA,CAAA,KAAE,CAAC,YAAY,CAAC,KAAK;QAEjD,2CAA2C;QAC3C,MAAM,aAAa,MAAM,kHAAA,CAAA,KAAE,CAAC,WAAW,CAAC,SAAS,CAAC;YAChD,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,WAAW;wBACX,UAAU;oBACZ;gBACF;gBACA,YAAY;gBACZ,eAAe;gBACf,oBAAoB;oBAClB,SAAS;wBACP,QAAQ;oBACV;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR,QAAQ;gBACN;gBACA;gBACA;gBACA;YACF;YACA,YAAY;gBACV,MAAM,aAAa;oBACjB,IAAI,WAAW,EAAE;oBACjB,YAAY,GAAG,WAAW,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,MAAM,CAAC,QAAQ,EAAE;oBAC1E,aAAa,WAAW,WAAW;oBACnC,UAAU,WAAW,QAAQ;oBAC7B,aAAa,WAAW,WAAW;oBACnC,eAAe,WAAW,UAAU,CAAC,MAAM,GAAG;oBAC9C,kBAAkB,WAAW,aAAa,CAAC,MAAM,GAAG;oBACpD,YAAY,WAAW,kBAAkB,CAAC,MAAM,GAAG;gBACrD,IAAI;YACN;YACA,WAAW,IAAI,OAAO,WAAW;QACnC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR,OAAO,MAAM,OAAO;QACtB,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}