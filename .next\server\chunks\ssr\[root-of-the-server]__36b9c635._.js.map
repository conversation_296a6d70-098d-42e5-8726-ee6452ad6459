{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VistaNotes/src/app/clients/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { Plus, Search, User, Calendar, FileText } from \"lucide-react\";\n\nexport default function Clients() {\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  \n  // Mock client data\n  const [clients] = useState([\n    {\n      id: \"1\",\n      firstName: \"<PERSON>\",\n      lastName: \"M.\",\n      dateOfBirth: \"1995-03-15\",\n      lastSession: \"2024-01-15\",\n      totalSessions: 12,\n      status: \"Active\"\n    },\n    {\n      id: \"2\",\n      firstName: \"<PERSON>\",\n      lastName: \"R.\",\n      dateOfBirth: \"1988-07-22\",\n      lastSession: \"2024-01-14\",\n      totalSessions: 8,\n      status: \"Active\"\n    },\n    {\n      id: \"3\",\n      firstName: \"Emma\",\n      lastName: \"L.\",\n      dateOfBirth: \"2001-11-08\",\n      lastSession: \"2024-01-12\",\n      totalSessions: 15,\n      status: \"Active\"\n    },\n    {\n      id: \"4\",\n      firstName: \"<PERSON>\",\n      lastName: \"K.\",\n      dateOfBirth: \"1992-05-30\",\n      lastSession: \"2023-12-20\",\n      totalSessions: 6,\n      status: \"Inactive\"\n    }\n  ]);\n\n  const filteredClients = clients.filter(client =>\n    `${client.firstName} ${client.lastName}`.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  const calculateAge = (dateOfBirth: string) => {\n    const today = new Date();\n    const birthDate = new Date(dateOfBirth);\n    let age = today.getFullYear() - birthDate.getFullYear();\n    const monthDiff = today.getMonth() - birthDate.getMonth();\n    \n    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {\n      age--;\n    }\n    \n    return age;\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"flex-shrink-0 mr-8\">\n                <h1 className=\"text-2xl font-bold text-gray-900\">VistaNotes</h1>\n              </Link>\n              <nav className=\"hidden md:flex space-x-8\">\n                <Link href=\"/dashboard\" className=\"text-gray-500 hover:text-gray-900\">\n                  Dashboard\n                </Link>\n                <Link href=\"/clients\" className=\"text-indigo-600 font-medium\">\n                  Clients\n                </Link>\n                <Link href=\"/notes\" className=\"text-gray-500 hover:text-gray-900\">\n                  Session Notes\n                </Link>\n              </nav>\n            </div>\n            <Link\n              href=\"/clients/new\"\n              className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700\"\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Add Client\n            </Link>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Page Header */}\n        <div className=\"mb-8\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">Clients</h2>\n          <p className=\"text-gray-600\">Manage your client roster and session history</p>\n        </div>\n\n        {/* Search and Filters */}\n        <div className=\"card mb-8\">\n          <div className=\"flex flex-col sm:flex-row gap-6\">\n            <div className=\"flex-1\">\n              <label className=\"form-label\">Search Clients</label>\n              <div className=\"relative\">\n                <Search className=\"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search by name...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"form-input pl-10\"\n                />\n              </div>\n              <p className=\"form-help\">Find clients by first or last name</p>\n            </div>\n            <div className=\"sm:w-48\">\n              <label className=\"form-label\">Filter by Status</label>\n              <select\n                value={statusFilter}\n                onChange={(e) => setStatusFilter(e.target.value)}\n                className=\"form-select\"\n              >\n                <option value=\"all\">All Clients</option>\n                <option value=\"active\">Active Only</option>\n                <option value=\"inactive\">Inactive Only</option>\n              </select>\n              <p className=\"form-help\">Show active or inactive clients</p>\n            </div>\n          </div>\n        </div>\n\n        {/* Clients List */}\n        <div className=\"bg-white rounded-lg shadow overflow-hidden\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <h3 className=\"text-lg font-medium text-gray-900\">\n              Client List ({filteredClients.length})\n            </h3>\n          </div>\n          \n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Client\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Age\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Last Session\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Total Sessions\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Status\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Actions\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {filteredClients.map((client) => (\n                  <tr key={client.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center\">\n                        <div className=\"flex-shrink-0 h-10 w-10\">\n                          <div className=\"h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center\">\n                            <User className=\"h-5 w-5 text-indigo-600\" />\n                          </div>\n                        </div>\n                        <div className=\"ml-4\">\n                          <div className=\"text-sm font-medium text-gray-900\">\n                            {client.firstName} {client.lastName}\n                          </div>\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {calculateAge(client.dateOfBirth)}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {new Date(client.lastSession).toLocaleDateString()}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {client.totalSessions}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={client.status === 'Active' ? 'status-active' : 'status-inactive'}>\n                        {client.status}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-right\">\n                      <div className=\"flex justify-end space-x-2\">\n                        <Link\n                          href={`/clients/${client.id}`}\n                          className=\"btn-secondary text-xs px-3 py-1\"\n                        >\n                          View\n                        </Link>\n                        <Link\n                          href={`/notes/new?client=${client.id}`}\n                          className=\"btn-primary text-xs px-3 py-1\"\n                        >\n                          New Note\n                        </Link>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n\n          {filteredClients.length === 0 && (\n            <div className=\"text-center py-12\">\n              <User className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No clients found</h3>\n              <p className=\"text-gray-600 mb-4\">\n                {searchTerm ? 'Try adjusting your search terms.' : 'Get started by adding your first client.'}\n              </p>\n              <Link\n                href=\"/clients/new\"\n                className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700\"\n              >\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Add Client\n              </Link>\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,mBAAmB;IACnB,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzB;YACE,IAAI;YACJ,WAAW;YACX,UAAU;YACV,aAAa;YACb,aAAa;YACb,eAAe;YACf,QAAQ;QACV;QACA;YACE,IAAI;YACJ,WAAW;YACX,UAAU;YACV,aAAa;YACb,aAAa;YACb,eAAe;YACf,QAAQ;QACV;QACA;YACE,IAAI;YACJ,WAAW;YACX,UAAU;YACV,aAAa;YACb,aAAa;YACb,eAAe;YACf,QAAQ;QACV;QACA;YACE,IAAI;YACJ,WAAW;YACX,UAAU;YACV,aAAa;YACb,aAAa;YACb,eAAe;YAC<PERSON>,QAAQ;QACV;KACD;IAED,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA,SACrC,GAAG,OAAO,SAAS,CAAC,CAAC,EAAE,OAAO,QAAQ,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAGxF,MAAM,eAAe,CAAC;QACpB,MAAM,QAAQ,IAAI;QAClB,MAAM,YAAY,IAAI,KAAK;QAC3B,IAAI,MAAM,MAAM,WAAW,KAAK,UAAU,WAAW;QACrD,MAAM,YAAY,MAAM,QAAQ,KAAK,UAAU,QAAQ;QAEvD,IAAI,YAAY,KAAM,cAAc,KAAK,MAAM,OAAO,KAAK,UAAU,OAAO,IAAK;YAC/E;QACF;QAEA,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDACvB,cAAA,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;;;;;;kDAEnD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAoC;;;;;;0DAGtE,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8B;;;;;;0DAG9D,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAoC;;;;;;;;;;;;;;;;;;0CAKtE,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;0BAQzC,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAI/B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAa;;;;;;sDAC9B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,WAAU;;;;;;;;;;;;sDAGd,8OAAC;4CAAE,WAAU;sDAAY;;;;;;;;;;;;8CAE3B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAa;;;;;;sDAC9B,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAC/C,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAW;;;;;;;;;;;;sDAE3B,8OAAC;4CAAE,WAAU;sDAAY;;;;;;;;;;;;;;;;;;;;;;;kCAM/B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;;wCAAoC;wCAClC,gBAAgB,MAAM;wCAAC;;;;;;;;;;;;0CAIzC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;;;;;;;;;;;;sDAKnG,8OAAC;4CAAM,WAAU;sDACd,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC;oDAAmB,WAAU;;sEAC5B,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;;;;;;;;;;;kFAGpB,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;;gFACZ,OAAO,SAAS;gFAAC;gFAAE,OAAO,QAAQ;;;;;;;;;;;;;;;;;;;;;;;sEAK3C,8OAAC;4DAAG,WAAU;sEACX,aAAa,OAAO,WAAW;;;;;;sEAElC,8OAAC;4DAAG,WAAU;sEACX,IAAI,KAAK,OAAO,WAAW,EAAE,kBAAkB;;;;;;sEAElD,8OAAC;4DAAG,WAAU;sEACX,OAAO,aAAa;;;;;;sEAEvB,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAW,OAAO,MAAM,KAAK,WAAW,kBAAkB;0EAC7D,OAAO,MAAM;;;;;;;;;;;sEAGlB,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,4JAAA,CAAA,UAAI;wEACH,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,EAAE;wEAC7B,WAAU;kFACX;;;;;;kFAGD,8OAAC,4JAAA,CAAA,UAAI;wEACH,MAAM,CAAC,kBAAkB,EAAE,OAAO,EAAE,EAAE;wEACtC,WAAU;kFACX;;;;;;;;;;;;;;;;;;mDAxCE,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;4BAmDzB,gBAAgB,MAAM,KAAK,mBAC1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAE,WAAU;kDACV,aAAa,qCAAqC;;;;;;kDAErD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjD", "debugId": null}}]}