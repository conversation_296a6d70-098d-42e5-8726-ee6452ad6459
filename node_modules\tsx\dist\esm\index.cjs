"use strict";var q=Object.defineProperty;var d=(e,t)=>q(e,"name",{value:t,configurable:!0});var b=require("node:worker_threads"),u=require("../node-features-C11kbLv2.cjs"),T=require("../register-D9k8ayxd.cjs");require("../get-pipe-path-BoR10qr8.cjs"),require("node:module");var p=require("node:path"),R=require("node:url");require("get-tsconfig");var o=require("../register-BXA4IaYH.cjs"),E=require("node:fs");require("esbuild"),require("node:crypto");var l=require("../index-gckBtVBf.cjs"),F=require("../client-D6NvIMSC.cjs");require("../require-CwOSHjvM.cjs");var g=require("node:fs/promises");require("module"),require("../temporary-directory-B83uKxJF.cjs"),require("node:os"),require("node:util"),require("../index-BWFBUo6r.cjs"),require("node:net");const f={active:!0},_=d(async e=>{if(!e)throw new Error(`tsx must be loaded with --import instead of --loader
The --loader flag was deprecated in Node v20.6.0 and v18.19.0`);f.namespace=e.namespace,e.tsconfig!==!1&&o.loadTsconfig(e.tsconfig??process.env.TSX_TSCONFIG_PATH),e.port&&(f.port=e.port,e.port.on("message",t=>{t==="deactivate"&&(f.active=!1,e.port.postMessage({type:"deactivated"}))}))},"initialize"),O=d(()=>(o.loadTsconfig(process.env.TSX_TSCONFIG_PATH),"process.setSourceMapsEnabled(true);"),"globalPreload"),y=new Map,J=d(async e=>{if(y.has(e))return y.get(e);if(!await E.promises.access(e).then(()=>!0,()=>!1)){y.set(e,void 0);return}const n=await E.promises.readFile(e,"utf8");try{const s=JSON.parse(n);return y.set(e,s),s}catch{throw new Error(`Error parsing: ${e}`)}},"readPackageJson"),N=d(async e=>{let t=new URL("package.json",e);for(;!t.pathname.endsWith("/node_modules/package.json");){const n=R.fileURLToPath(t),s=await J(n);if(s)return s;const r=t;if(t=new URL("../package.json",t),t.pathname===r.pathname)break}},"findPackageJson"),C=d(async e=>(await N(e))?.type??"commonjs","getPackageType"),W=d(e=>{[e]=e.split("?");const t=p.extname(e);if(t===".mts")return"module";if(t===".cts")return"commonjs"},"getFormatFromExtension"),A=d(e=>{const t=W(e);if(t)return t;if(o.tsExtensionsPattern.test(e))return C(e)},"getFormatFromFileUrl"),w="tsx-namespace=",U=d(e=>{const t=e.indexOf(w);if(t===-1)return;const n=e[t-1];if(n!=="?"&&n!=="&")return;const s=t+w.length,r=e.indexOf("&",s);return r===-1?e.slice(s):e.slice(s,r)},"getNamespace"),S=d(e=>{if(!e.includes("%3F"))return e;const[t,n]=e.split("%3F",2),s=decodeURIComponent(n);return`${t}?${s}`},"decodeCjsQuery"),j=u.isFeatureSupported(u.importAttributes)?"importAttributes":"importAssertions";if(exports.load=async(e,t,n)=>{if(!f.active)return n(e,t);e=S(e);const s=U(e);if(f.namespace&&f.namespace!==s)return n(e,t);if(f.port){const a=new URL(e);a.searchParams.delete("tsx-namespace"),f.port.postMessage({type:"load",url:a.toString()})}F.parent.send&&F.parent.send({type:"dependency",path:e});const r=e.startsWith(o.fileUrlPrefix)?R.fileURLToPath(e):e;if(t.format==="module-json"){const a=await g.readFile(new URL(e),"utf8"),m=await l.transform(a,r,{tsconfigRaw:p.isAbsolute(r)?o.fileMatcher?.(r):void 0});return{shortCircuit:!0,format:"module",source:o.inlineSourceMap(m)}}if(t.format==="commonjs-json"){const a=await g.readFile(new URL(e),"utf8"),m=l.transformSync(a,r,{tsconfigRaw:p.isAbsolute(r)?o.fileMatcher?.(r):void 0});return m.code+=`
0 && (module.exports = ${JSON.stringify(Object.fromEntries(Object.keys(JSON.parse(a)).map(h=>[h,0])))});`,{shortCircuit:!0,format:"commonjs",source:o.inlineSourceMap(m)}}if((t.format===void 0||t.format==="commonjs"||t.format==="commonjs-typescript")&&u.isFeatureSupported(u.esmLoadReadFile)&&e.startsWith("file:")&&r.endsWith(".js")){const a=await g.readFile(new URL(e),"utf8");if(l.isESM(a)){const m=l.transformSync(a,e,{tsconfigRaw:o.fileMatcher?.(r)});if(u.isFeatureSupported(u.loadReadFromSource))return{shortCircuit:!0,format:"commonjs",source:o.inlineSourceMap(m)};const h=s?`${r}?namespace=${encodeURIComponent(s)}`:r;return{shortCircuit:!0,format:"commonjs",responseURL:`data:text/javascript,${encodeURIComponent(m.code)}?filePath=${encodeURIComponent(h)}`}}}if(o.isJsonPattern.test(e)){let a=t[j];a||(a={},t[j]=a),a.type||(a.type="json")}const c=await n(e,t);if(o.logEsm("loaded by next loader",{url:e,loaded:c}),u.isFeatureSupported(u.loadReadFromSource)&&c.format==="commonjs"&&r.endsWith(".cjs")){let a=await g.readFile(new URL(e),"utf8");const m=l.transformDynamicImport(r,a);return m&&(a=o.inlineSourceMap(m)),c.source=a,c.shortCircuit=!0,c}if(c.format==="commonjs"&&u.isFeatureSupported(u.esmLoadReadFile)&&c.responseURL?.startsWith("file:")&&!r.endsWith(".cjs")){const a=await g.readFile(new URL(e),"utf8");if(!r.endsWith(".js")||l.isESM(a)){const m=l.transformSync(a,e,{tsconfigRaw:o.fileMatcher?.(r)});if(u.isFeatureSupported(u.loadReadFromSource))c.source=o.inlineSourceMap(m);else{const h=s?`${r}?namespace=${encodeURIComponent(s)}`:r;c.responseURL=`data:text/javascript,${encodeURIComponent(m.code)}?filePath=${encodeURIComponent(h)}`}return o.logEsm("returning CJS export annotation",c),c}}if(!c.source)return c;const i=c.source.toString();if(u.isFeatureSupported(u.requireEsm)&&c.format==="json"){const a=l.transformSync(i,r,{tsconfigRaw:p.isAbsolute(r)?o.fileMatcher?.(r):void 0});return a.code+=`
0 && (module.exports = ${JSON.stringify(Object.fromEntries(Object.keys(JSON.parse(i)).map(m=>[m,0])))});`,{format:"commonjs",source:o.inlineSourceMap(a)}}if(c.format==="json"||o.tsExtensionsPattern.test(e)){const a=await l.transform(i,r,{tsconfigRaw:p.isAbsolute(r)?o.fileMatcher?.(r):void 0});return{format:"module",source:o.inlineSourceMap(a)}}if(c.format==="module"){const a=l.transformDynamicImport(r,i);a&&(c.source=o.inlineSourceMap(a))}return c},o.debugEnabled){const e=exports.load;exports.load=async(t,n,s)=>{o.logEsm("load",{url:t,context:n});const r=await e(t,n,s);return o.logEsm("loaded",{url:t,result:r}),r}}const L=d(e=>{if(e.url)return e.url;const t=e.message.match(/^Cannot find module '([^']+)'/);if(t){const[,s]=t;return s}const n=e.message.match(/^Cannot find package '([^']+)'/);if(n){const[,s]=n;if(!p.isAbsolute(s))return;const r=R.pathToFileURL(s);if(r.pathname.endsWith("/")&&(r.pathname+="package.json"),r.pathname.endsWith("/package.json")){const c=l.readJsonFile(r);if(c?.main)return new URL(c.main,r).toString()}else return r.toString()}},"getMissingPathFromNotFound"),P=d(async(e,t,n,s)=>{const r=o.mapTsExtensions(e);if(o.logEsm("resolveExtensions",{url:e,context:t,throwError:s,tryPaths:r}),!r)return;let c;for(const i of r)try{return await n(i,t)}catch(a){const{code:m}=a;if(m!=="ERR_MODULE_NOT_FOUND"&&m!=="ERR_PACKAGE_PATH_NOT_EXPORTED")throw a;c=a}if(s)throw c},"resolveExtensions"),I=d(async(e,t,n)=>{if(o.logEsm("resolveBase",{specifier:e,context:t,specifierStartsWithFileUrl:e.startsWith(o.fileUrlPrefix),isRelativePath:o.isRelativePath(e),specifierNodeModules:e.includes(o.nodeModulesPath),tsExtensionsPattern:o.tsExtensionsPattern.test(t.parentURL),allowJs:o.allowJs}),(e.startsWith(o.fileUrlPrefix)||o.isRelativePath(e))&&!e.includes("/node_modules/")&&(o.tsExtensionsPattern.test(t.parentURL)||o.allowJs)){const s=await P(e,t,n);if(o.logEsm("resolveBase resolved",{specifier:e,context:t,resolved:s}),s)return s}try{return await n(e,t)}catch(s){if(o.logEsm("resolveBase error",{specifier:e,context:t,error:s}),s instanceof Error){const r=s;if(r.code==="ERR_MODULE_NOT_FOUND"){const c=L(r);if(c){const i=await P(c,t,n);if(i)return i}}}throw s}},"resolveBase"),M=d(async(e,t,n)=>{if(o.logEsm("resolveDirectory",{specifier:e,context:t,isDirectory:o.isDirectoryPattern.test(e)}),(e==="."||e===".."||e.endsWith("/.."))&&(e+="/"),o.isDirectoryPattern.test(e)){const s=new URL(e,t.parentURL);return s.pathname=p.join(s.pathname,"index"),await P(s.toString(),t,n,!0)}try{return await I(e,t,n)}catch(s){if(s instanceof Error){o.logEsm("resolveDirectory error",{specifier:e,context:t,error:s});const r=s;if(r.code==="ERR_UNSUPPORTED_DIR_IMPORT"){const c=L(r);if(c)try{return await P(`${c}/index`,t,n,!0)}catch(i){const a=i,{message:m}=a;throw a.message=a.message.replace(`${"/index".replace("/",p.sep)}'`,"'"),a.stack=a.stack.replace(m,a.message),a}}}throw s}},"resolveDirectory"),$=d(async(e,t,n)=>{if(o.logEsm("resolveTsPaths",{specifier:e,context:t,requestAcceptsQuery:o.requestAcceptsQuery(e),tsconfigPathsMatcher:o.tsconfigPathsMatcher,fromNodeModules:t.parentURL?.includes("/node_modules/")}),!o.requestAcceptsQuery(e)&&o.tsconfigPathsMatcher&&!t.parentURL?.includes("/node_modules/")){const s=o.tsconfigPathsMatcher(e);o.logEsm("resolveTsPaths",{possiblePaths:s});for(const r of s)try{return await M(R.pathToFileURL(r).toString(),t,n)}catch{}}return M(e,t,n)},"resolveTsPaths"),k="tsx://",v=new Map;if(exports.resolve=async(e,t,n)=>{if(!f.active||e.startsWith("node:"))return n(e,t);u.isFeatureSupported(u.loadReadFromSource)&&(e.includes("%3F")&&(e=S(e)),t.parentURL?.includes("%3F")&&(t.parentURL=S(t.parentURL)));let s=U(e)??(t.parentURL&&U(t.parentURL));if(f.namespace){let a;if(e.startsWith(k)){try{a=JSON.parse(e.slice(k.length))}catch{}a?.namespace&&(s=a.namespace)}if(f.namespace!==s)return n(e,t);a&&(e=a.specifier,t.parentURL=a.parentURL)}const[r,c]=e.split("?"),i=await $(r,t,n);if(i.format==="builtin")return i;if(!i.format&&i.url.startsWith(o.fileUrlPrefix)&&(i.format=await A(i.url)),c&&(i.url+=`?${c}`),s&&!i.url.includes(w)&&(i.url+=(i.url.includes("?")?"&":"?")+w+s),i.format==="commonjs-typescript"&&(i.format="commonjs"),i.format==="module-typescript"&&(i.format="module"),i.format==="module"&&t.parentURL&&v.get(t.parentURL)==="commonjs"&&(i.format="commonjs"),i.format==="json"&&t.parentURL){const a=v.get(t.parentURL);a==="commonjs"&&(i.format="commonjs-json"),a==="module"&&(i.format="module-json")}if(v.set(i.url,i.format),u.isFeatureSupported([[20,11,0],[22,10,0]])&&i.format==="commonjs"&&i.url.includes("?")){const[a,m]=i.url.split("?");i.url=a+encodeURIComponent(`?${m}`)}return i},o.debugEnabled){const e=exports.resolve;exports.resolve=async(t,n,s)=>{o.logEsm("resolve",{specifier:t,context:n});const r=await e(t,n,s);return o.logEsm("resolved",{specifier:t,context:n,result:r}),r}}u.isFeatureSupported(u.moduleRegister)&&b.isMainThread&&T.register(),exports.globalPreload=O,exports.initialize=_;
