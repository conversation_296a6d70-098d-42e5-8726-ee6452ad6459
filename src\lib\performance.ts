// Production performance monitoring and optimization

export interface PerformanceConfig {
  enableCaching: boolean;
  cacheTimeout: number;
  enableCompression: boolean;
  enableMetrics: boolean;
  maxRequestSize: number;
  rateLimitWindow: number;
  rateLimitRequests: number;
}

export const performanceConfig: PerformanceConfig = {
  enableCaching: process.env.NODE_ENV === 'production',
  cacheTimeout: 300, // 5 minutes
  enableCompression: true,
  enableMetrics: true,
  maxRequestSize: 20 * 1024 * 1024, // 20MB
  rateLimitWindow: 15 * 60 * 1000, // 15 minutes
  rateLimitRequests: 100,
};

// Simple in-memory cache for development, Redis for production
class CacheService {
  private cache = new Map<string, { data: any; expires: number }>();

  async get(key: string): Promise<any> {
    if (process.env.NODE_ENV === 'production' && process.env.REDIS_URL) {
      // In production, use Redis
      return this.getFromRedis(key);
    }
    
    // Development: use in-memory cache
    const item = this.cache.get(key);
    if (item && item.expires > Date.now()) {
      return item.data;
    }
    return null;
  }

  async set(key: string, data: any, ttl: number = performanceConfig.cacheTimeout): Promise<void> {
    if (process.env.NODE_ENV === 'production' && process.env.REDIS_URL) {
      // In production, use Redis
      return this.setInRedis(key, data, ttl);
    }
    
    // Development: use in-memory cache
    this.cache.set(key, {
      data,
      expires: Date.now() + (ttl * 1000)
    });
  }

  async delete(key: string): Promise<void> {
    if (process.env.NODE_ENV === 'production' && process.env.REDIS_URL) {
      return this.deleteFromRedis(key);
    }
    
    this.cache.delete(key);
  }

  private async getFromRedis(key: string): Promise<any> {
    try {
      // Redis implementation would go here
      // const redis = new Redis(process.env.REDIS_URL);
      // const data = await redis.get(key);
      // return data ? JSON.parse(data) : null;
      return null;
    } catch (error) {
      console.error('Redis get error:', error);
      return null;
    }
  }

  private async setInRedis(key: string, data: any, ttl: number): Promise<void> {
    try {
      // Redis implementation would go here
      // const redis = new Redis(process.env.REDIS_URL);
      // await redis.setex(key, ttl, JSON.stringify(data));
    } catch (error) {
      console.error('Redis set error:', error);
    }
  }

  private async deleteFromRedis(key: string): Promise<void> {
    try {
      // Redis implementation would go here
      // const redis = new Redis(process.env.REDIS_URL);
      // await redis.del(key);
    } catch (error) {
      console.error('Redis delete error:', error);
    }
  }
}

export const cache = new CacheService();

// Performance metrics collection
export class PerformanceMetrics {
  private metrics: Map<string, number[]> = new Map();

  recordMetric(name: string, value: number): void {
    if (!performanceConfig.enableMetrics) return;

    const values = this.metrics.get(name) || [];
    values.push(value);
    
    // Keep only last 100 values
    if (values.length > 100) {
      values.shift();
    }
    
    this.metrics.set(name, values);
  }

  getMetricSummary(name: string): { avg: number; min: number; max: number; count: number } | null {
    const values = this.metrics.get(name);
    if (!values || values.length === 0) return null;

    const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);

    return { avg, min, max, count: values.length };
  }

  getAllMetrics(): Record<string, any> {
    const summary: Record<string, any> = {};
    
    for (const [name, values] of this.metrics.entries()) {
      summary[name] = this.getMetricSummary(name);
    }
    
    return summary;
  }
}

export const performanceMetrics = new PerformanceMetrics();

// Database query optimization
export async function withQueryOptimization<T>(
  queryName: string,
  queryFn: () => Promise<T>
): Promise<T> {
  const startTime = performance.now();
  
  try {
    const result = await queryFn();
    const duration = performance.now() - startTime;
    
    performanceMetrics.recordMetric(`db_query_${queryName}`, duration);
    
    if (duration > 1000) {
      console.warn(`Slow query detected: ${queryName} took ${duration.toFixed(2)}ms`);
    }
    
    return result;
  } catch (error) {
    const duration = performance.now() - startTime;
    performanceMetrics.recordMetric(`db_query_${queryName}_error`, duration);
    throw error;
  }
}

// API response caching
export function withCaching(cacheKey: string, ttl?: number) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      if (!performanceConfig.enableCaching) {
        return method.apply(this, args);
      }

      const key = `${cacheKey}_${JSON.stringify(args)}`;
      const cached = await cache.get(key);
      
      if (cached) {
        performanceMetrics.recordMetric('cache_hit', 1);
        return cached;
      }

      const result = await method.apply(this, args);
      await cache.set(key, result, ttl);
      performanceMetrics.recordMetric('cache_miss', 1);
      
      return result;
    };
  };
}

// Request size validation
export function validateRequestSize(req: Request): boolean {
  const contentLength = req.headers.get('content-length');
  if (contentLength && parseInt(contentLength) > performanceConfig.maxRequestSize) {
    return false;
  }
  return true;
}

// Rate limiting (simple implementation)
class RateLimiter {
  private requests = new Map<string, number[]>();

  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const windowStart = now - performanceConfig.rateLimitWindow;
    
    const userRequests = this.requests.get(identifier) || [];
    const recentRequests = userRequests.filter(time => time > windowStart);
    
    if (recentRequests.length >= performanceConfig.rateLimitRequests) {
      return false;
    }
    
    recentRequests.push(now);
    this.requests.set(identifier, recentRequests);
    
    return true;
  }

  getRemainingRequests(identifier: string): number {
    const now = Date.now();
    const windowStart = now - performanceConfig.rateLimitWindow;
    
    const userRequests = this.requests.get(identifier) || [];
    const recentRequests = userRequests.filter(time => time > windowStart);
    
    return Math.max(0, performanceConfig.rateLimitRequests - recentRequests.length);
  }
}

export const rateLimiter = new RateLimiter();

// Health check utilities
export async function getSystemHealth(): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy';
  checks: Record<string, boolean>;
  metrics: Record<string, any>;
}> {
  const checks: Record<string, boolean> = {};
  
  // Database health
  try {
    // This would be replaced with actual database ping
    checks.database = true;
  } catch {
    checks.database = false;
  }
  
  // Cache health
  try {
    await cache.set('health_check', 'ok', 10);
    const result = await cache.get('health_check');
    checks.cache = result === 'ok';
  } catch {
    checks.cache = false;
  }
  
  // AI service health
  checks.ai_service = !!process.env.GROQ_API_KEY;
  
  const allHealthy = Object.values(checks).every(Boolean);
  const status = allHealthy ? 'healthy' : 'degraded';
  
  return {
    status,
    checks,
    metrics: performanceMetrics.getAllMetrics()
  };
}
