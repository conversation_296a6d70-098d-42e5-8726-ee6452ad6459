# Database
DATABASE_URL="postgresql://username:password@localhost:5432/vistanotes?schema=public"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="vistanotes-secret-key-development-only"

# AWS Configuration (for production)
AWS_REGION="us-east-1"
AWS_ACCESS_KEY_ID="your-access-key"
AWS_SECRET_ACCESS_KEY="your-secret-key"
AWS_S3_BUCKET="vistanotes-media"

# OpenAI API (for AI suggestions)
OPENAI_API_KEY="your-openai-api-key"

# Application Settings
NODE_ENV="development"

# For development - using SQLite instead of PostgreSQL
DATABASE_URL="file:./dev.db"
