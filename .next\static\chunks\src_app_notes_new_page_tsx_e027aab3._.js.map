{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VistaNotes/src/app/notes/new/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useRef } from \"react\";\nimport Link from \"next/link\";\nimport {\n  ArrowLeft,\n  Upload,\n  Image as ImageIcon,\n  Video,\n  Music,\n  Sparkles,\n  Save,\n  Eye,\n  X,\n  Loader2,\n  CheckCircle\n} from \"lucide-react\";\n\ninterface MultimediaFile {\n  id: string;\n  file: File;\n  type: 'image' | 'video' | 'audio';\n  preview?: string;\n  aiDescription?: string;\n  isGeneratingAI?: boolean;\n}\n\nexport default function NewSessionNote() {\n  const [selectedClient, setSelectedClient] = useState(\"\");\n  const [sessionDate, setSessionDate] = useState(new Date().toISOString().split('T')[0]);\n  const [sessionDuration, setSessionDuration] = useState(\"\");\n  const [noteType, setNoteType] = useState(\"SOAP\");\n  \n  // SOAP Note fields\n  const [subjective, setSubjective] = useState(\"\");\n  const [objective, setObjective] = useState(\"\");\n  const [assessment, setAssessment] = useState(\"\");\n  const [plan, setPlan] = useState(\"\");\n  const [interventions, setInterventions] = useState(\"\");\n  const [clientMood, setClientMood] = useState(\"\");\n  \n  // Multimedia handling\n  const [multimediaFiles, setMultimediaFiles] = useState<MultimediaFile[]>([]);\n  const [isUploading, setIsUploading] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  \n  // AI suggestions\n  const [isGeneratingAI, setIsGeneratingAI] = useState(false);\n  const [aiSuggestions, setAiSuggestions] = useState<{[key: string]: string}>({});\n\n  // Mock client data\n  const clients = [\n    { id: \"1\", name: \"Sarah M.\" },\n    { id: \"2\", name: \"Michael R.\" },\n    { id: \"3\", name: \"Emma L.\" },\n    { id: \"4\", name: \"David K.\" }\n  ];\n\n  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const files = event.target.files;\n    if (!files) return;\n\n    setIsUploading(true);\n    \n    for (let i = 0; i < files.length; i++) {\n      const file = files[i];\n      const fileType = getFileType(file.type);\n      \n      if (fileType) {\n        const newFile: MultimediaFile = {\n          id: Date.now().toString() + i,\n          file,\n          type: fileType,\n          preview: fileType === 'image' ? URL.createObjectURL(file) : undefined\n        };\n        \n        setMultimediaFiles(prev => [...prev, newFile]);\n      }\n    }\n    \n    setIsUploading(false);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  const getFileType = (mimeType: string): 'image' | 'video' | 'audio' | null => {\n    if (mimeType.startsWith('image/')) return 'image';\n    if (mimeType.startsWith('video/')) return 'video';\n    if (mimeType.startsWith('audio/')) return 'audio';\n    return null;\n  };\n\n  const removeFile = (fileId: string) => {\n    setMultimediaFiles(prev => prev.filter(f => f.id !== fileId));\n  };\n\n  const generateAIDescription = async (fileId: string) => {\n    const file = multimediaFiles.find(f => f.id === fileId);\n    if (!file) return;\n\n    setMultimediaFiles(prev => \n      prev.map(f => f.id === fileId ? { ...f, isGeneratingAI: true } : f)\n    );\n\n    // Simulate AI API call\n    setTimeout(() => {\n      const mockDescriptions = {\n        image: \"Client created a vibrant painting using warm colors (reds, oranges, yellows) with bold, expressive brushstrokes. The composition shows a central figure surrounded by swirling patterns, suggesting emotional expression and creative engagement.\",\n        video: \"Client demonstrated fluid, rhythmic movements during the dance therapy session. Movements progressed from tentative, small gestures to more expansive, confident expressions, indicating increased comfort and emotional release.\",\n        audio: \"Client's musical expression included steady drumming patterns with occasional dynamic changes. The rhythm became more structured throughout the session, suggesting improved focus and emotional regulation.\"\n      };\n\n      setMultimediaFiles(prev => \n        prev.map(f => f.id === fileId ? { \n          ...f, \n          isGeneratingAI: false,\n          aiDescription: mockDescriptions[f.type] \n        } : f)\n      );\n    }, 2000);\n  };\n\n  const generateAISuggestion = async (field: string, context: string) => {\n    console.log('Generating AI suggestion for field:', field, 'Context:', context);\n    setIsGeneratingAI(true);\n\n    try {\n      // Call the real AI API\n      const response = await fetch('/api/ai/suggestions', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          type: field.toUpperCase(),\n          context: context || `Generate a ${field} section for a Creative Arts Therapy session note.`,\n          sessionInfo: {\n            clientAge: 'adult',\n            sessionType: 'Creative Arts Therapy',\n            duration: sessionDuration || 45,\n            noteType: noteType\n          }\n        }),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        console.log('AI API response for field:', field, 'Data:', data);\n        setAiSuggestions(prev => ({\n          ...prev,\n          [field]: data.suggestion || \"AI suggestion generated based on session context.\"\n        }));\n      } else {\n        console.log('AI API failed with status:', response.status);\n        // Fallback to mock suggestions if API fails\n        const mockSuggestions: {[key: string]: string} = {\n          objective: \"Client engaged actively in art-making process, demonstrating sustained attention for 30 minutes. Chose vibrant colors and created abstract forms with confident brushstrokes. Maintained appropriate eye contact and responded verbally to therapeutic prompts.\",\n          assessment: \"Client shows continued progress in emotional expression through creative media. Increased confidence in artistic choices and willingness to discuss emotional content of artwork. Mood appears stable with positive engagement in therapeutic process.\",\n          plan: \"Continue weekly art therapy sessions focusing on emotional expression through color and form. Introduce new media (clay work) to explore tactile processing. Schedule follow-up with psychiatrist to discuss medication adjustment timeline.\"\n        };\n\n        setAiSuggestions(prev => ({\n          ...prev,\n          [field]: mockSuggestions[field] || \"AI suggestion generated based on session context.\"\n        }));\n      }\n    } catch (error) {\n      console.error('Error generating AI suggestion:', error);\n\n      // Fallback to mock suggestions on error\n      const mockSuggestions: {[key: string]: string} = {\n        objective: \"Client engaged actively in art-making process, demonstrating sustained attention for 30 minutes. Chose vibrant colors and created abstract forms with confident brushstrokes. Maintained appropriate eye contact and responded verbally to therapeutic prompts.\",\n        assessment: \"Client shows continued progress in emotional expression through creative media. Increased confidence in artistic choices and willingness to discuss emotional content of artwork. Mood appears stable with positive engagement in therapeutic process.\",\n        plan: \"Continue weekly art therapy sessions focusing on emotional expression through color and form. Introduce new media (clay work) to explore tactile processing. Schedule follow-up with psychiatrist to discuss medication adjustment timeline.\"\n      };\n\n      setAiSuggestions(prev => ({\n        ...prev,\n        [field]: mockSuggestions[field] || \"AI suggestion generated based on session context.\"\n      }));\n    } finally {\n      setIsGeneratingAI(false);\n    }\n  };\n\n  const insertAISuggestion = (field: string) => {\n    const suggestion = aiSuggestions[field];\n    console.log('Inserting AI suggestion for field:', field, 'Suggestion:', suggestion);\n\n    if (!suggestion) {\n      console.log('No suggestion found for field:', field);\n      return;\n    }\n\n    switch (field) {\n      case 'objective':\n        console.log('Setting objective field');\n        setObjective(prev => prev + (prev ? '\\n\\n' : '') + suggestion);\n        break;\n      case 'assessment':\n        console.log('Setting assessment field');\n        setAssessment(prev => prev + (prev ? '\\n\\n' : '') + suggestion);\n        break;\n      case 'plan':\n        console.log('Setting plan field');\n        setPlan(prev => prev + (prev ? '\\n\\n' : '') + suggestion);\n        break;\n      default:\n        console.log('Unknown field:', field);\n    }\n\n    // Clear the suggestion after inserting\n    setAiSuggestions(prev => ({ ...prev, [field]: '' }));\n  };\n\n  const getFileIcon = (type: string) => {\n    switch (type) {\n      case 'image':\n        return <ImageIcon className=\"h-5 w-5 text-green-600\" />;\n      case 'video':\n        return <Video className=\"h-5 w-5 text-blue-600\" />;\n      case 'audio':\n        return <Music className=\"h-5 w-5 text-purple-600\" />;\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between py-6\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard\" className=\"mr-4\">\n                <ArrowLeft className=\"h-6 w-6 text-gray-400 hover:text-gray-600\" />\n              </Link>\n              <h1 className=\"text-2xl font-bold text-gray-900\">New Session Note</h1>\n            </div>\n            <div className=\"flex space-x-3\">\n              <button\n                type=\"button\"\n                className=\"btn-secondary\"\n              >\n                <Eye className=\"h-4 w-4 mr-2\" />\n                Preview\n              </button>\n              <button\n                type=\"button\"\n                className=\"btn-secondary\"\n              >\n                <Save className=\"h-4 w-4 mr-2\" />\n                Save Draft\n              </button>\n              <button\n                type=\"button\"\n                className=\"btn-primary\"\n              >\n                <CheckCircle className=\"h-4 w-4 mr-2\" />\n                Save & Finalize\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Main Form */}\n          <div className=\"lg:col-span-2 space-y-6\">\n            {/* Session Information */}\n            <div className=\"card\">\n              <div className=\"card-header\">\n                <h3 className=\"card-title\">Session Information</h3>\n                <p className=\"card-subtitle\">Basic details about this therapy session</p>\n              </div>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"form-label\">\n                    Client *\n                  </label>\n                  <select\n                    value={selectedClient}\n                    onChange={(e) => setSelectedClient(e.target.value)}\n                    className=\"form-select\"\n                    required\n                  >\n                    <option value=\"\">Choose a client...</option>\n                    {clients.map(client => (\n                      <option key={client.id} value={client.id}>\n                        {client.name}\n                      </option>\n                    ))}\n                  </select>\n                  {!selectedClient && (\n                    <p className=\"form-help\">Select the client for this therapy session</p>\n                  )}\n                </div>\n                <div>\n                  <label className=\"form-label\">\n                    Session Date *\n                  </label>\n                  <input\n                    type=\"date\"\n                    value={sessionDate}\n                    onChange={(e) => setSessionDate(e.target.value)}\n                    className=\"form-input\"\n                    required\n                  />\n                  <p className=\"form-help\">Date when the therapy session occurred</p>\n                </div>\n                <div>\n                  <label className=\"form-label\">\n                    Duration (minutes)\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={sessionDuration}\n                    onChange={(e) => setSessionDuration(e.target.value)}\n                    placeholder=\"Enter session length (e.g., 45)\"\n                    className=\"form-input\"\n                    min=\"1\"\n                    max=\"300\"\n                  />\n                  <p className=\"form-help\">Length of the therapy session in minutes</p>\n                </div>\n                <div>\n                  <label className=\"form-label\">\n                    Note Type\n                  </label>\n                  <select\n                    value={noteType}\n                    onChange={(e) => setNoteType(e.target.value)}\n                    className=\"form-select\"\n                  >\n                    <option value=\"SOAP\">SOAP Note</option>\n                    <option value=\"DAP\">DAP Note</option>\n                    <option value=\"BIRP\">BIRP Note</option>\n                  </select>\n                  <p className=\"form-help\">Documentation format for this session</p>\n                </div>\n              </div>\n            </div>\n\n            {/* SOAP Note Fields */}\n            <div className=\"card\">\n              <div className=\"card-header\">\n                <h3 className=\"card-title\">SOAP Note Documentation</h3>\n                <p className=\"card-subtitle\">Structured clinical documentation for this therapy session</p>\n              </div>\n\n              {/* Subjective */}\n              <div className=\"mb-6\">\n                <label className=\"form-label\">\n                  Subjective (Client's reported experience)\n                </label>\n                <textarea\n                  value={subjective}\n                  onChange={(e) => setSubjective(e.target.value)}\n                  rows={3}\n                  placeholder=\"Document what the client reports about their mood, feelings, concerns, and experiences...\"\n                  className=\"form-textarea\"\n                />\n                <p className=\"form-help\">Record the client's own words and self-reported experiences</p>\n              </div>\n\n              {/* Objective */}\n              <div className=\"mb-6\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <label className=\"form-label\">\n                    Objective (Observable behaviors and activities)\n                  </label>\n                  <button\n                    type=\"button\"\n                    onClick={() => generateAISuggestion('objective', objective)}\n                    disabled={isGeneratingAI}\n                    className=\"btn-secondary text-xs\"\n                  >\n                    {isGeneratingAI ? (\n                      <Loader2 className=\"h-3 w-3 mr-1 animate-spin\" />\n                    ) : (\n                      <Sparkles className=\"h-3 w-3 mr-1\" />\n                    )}\n                    AI Suggest\n                  </button>\n                </div>\n                <textarea\n                  value={objective}\n                  onChange={(e) => setObjective(e.target.value)}\n                  rows={4}\n                  placeholder=\"Describe observable behaviors, creative process, engagement level, materials used, and therapeutic activities...\"\n                  className=\"form-textarea\"\n                />\n                <p className=\"form-help\">Document what you observed during the session - behaviors, activities, and creative expressions</p>\n                {aiSuggestions.objective && (\n                  <div className=\"mt-3 p-4 bg-blue-50 border border-blue-200 rounded-lg\">\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm font-medium text-blue-900 mb-1\">AI Suggestion:</p>\n                        <p className=\"text-sm text-blue-800 leading-relaxed\">{aiSuggestions.objective}</p>\n                      </div>\n                      <div className=\"flex space-x-2 ml-4\">\n                        <button\n                          onClick={() => insertAISuggestion('objective')}\n                          className=\"btn-primary text-xs px-3 py-1\"\n                        >\n                          Insert\n                        </button>\n                        <button\n                          onClick={() => setAiSuggestions(prev => ({ ...prev, objective: '' }))}\n                          className=\"btn-secondary text-xs px-3 py-1\"\n                        >\n                          Dismiss\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              {/* Assessment */}\n              <div className=\"mb-6\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <label className=\"form-label\">\n                    Assessment (Clinical interpretation)\n                  </label>\n                  <button\n                    type=\"button\"\n                    onClick={() => generateAISuggestion('assessment', assessment)}\n                    disabled={isGeneratingAI}\n                    className=\"btn-secondary text-xs\"\n                  >\n                    {isGeneratingAI ? (\n                      <Loader2 className=\"h-3 w-3 mr-1 animate-spin\" />\n                    ) : (\n                      <Sparkles className=\"h-3 w-3 mr-1\" />\n                    )}\n                    AI Suggest\n                  </button>\n                </div>\n                <textarea\n                  value={assessment}\n                  onChange={(e) => setAssessment(e.target.value)}\n                  rows={4}\n                  placeholder=\"Provide clinical assessment, progress toward therapeutic goals, insights about client's emotional state and creative expression...\"\n                  className=\"form-textarea\"\n                />\n                <p className=\"form-help\">Your professional interpretation of the session and client's progress</p>\n                {aiSuggestions.assessment && (\n                  <div className=\"mt-3 p-4 bg-blue-50 border border-blue-200 rounded-lg\">\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm font-medium text-blue-900 mb-1\">AI Suggestion:</p>\n                        <p className=\"text-sm text-blue-800 leading-relaxed\">{aiSuggestions.assessment}</p>\n                      </div>\n                      <div className=\"flex space-x-2 ml-4\">\n                        <button\n                          onClick={() => insertAISuggestion('assessment')}\n                          className=\"btn-primary text-xs px-3 py-1\"\n                        >\n                          Insert\n                        </button>\n                        <button\n                          onClick={() => setAiSuggestions(prev => ({ ...prev, assessment: '' }))}\n                          className=\"btn-secondary text-xs px-3 py-1\"\n                        >\n                          Dismiss\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              {/* Plan */}\n              <div className=\"mb-6\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <label className=\"form-label\">\n                    Plan (Treatment plan and next steps)\n                  </label>\n                  <button\n                    type=\"button\"\n                    onClick={() => generateAISuggestion('plan', plan)}\n                    disabled={isGeneratingAI}\n                    className=\"btn-secondary text-xs\"\n                  >\n                    {isGeneratingAI ? (\n                      <Loader2 className=\"h-3 w-3 mr-1 animate-spin\" />\n                    ) : (\n                      <Sparkles className=\"h-3 w-3 mr-1\" />\n                    )}\n                    AI Suggest\n                  </button>\n                </div>\n                <textarea\n                  value={plan}\n                  onChange={(e) => setPlan(e.target.value)}\n                  rows={4}\n                  placeholder=\"Outline treatment plan, goals for next session, recommendations, and follow-up activities...\"\n                  className=\"form-textarea\"\n                />\n                <p className=\"form-help\">Document your treatment plan and next steps for continued therapy</p>\n                {aiSuggestions.plan && (\n                  <div className=\"mt-3 p-4 bg-blue-50 border border-blue-200 rounded-lg\">\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm font-medium text-blue-900 mb-1\">AI Suggestion:</p>\n                        <p className=\"text-sm text-blue-800 leading-relaxed\">{aiSuggestions.plan}</p>\n                      </div>\n                      <div className=\"flex space-x-2 ml-4\">\n                        <button\n                          onClick={() => insertAISuggestion('plan')}\n                          className=\"btn-primary text-xs px-3 py-1\"\n                        >\n                          Insert\n                        </button>\n                        <button\n                          onClick={() => setAiSuggestions(prev => ({ ...prev, plan: '' }))}\n                          className=\"btn-secondary text-xs px-3 py-1\"\n                        >\n                          Dismiss\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              {/* Additional Fields */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"form-label\">\n                    Interventions Used\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={interventions}\n                    onChange={(e) => setInterventions(e.target.value)}\n                    placeholder=\"e.g., Art therapy, music therapy, movement therapy\"\n                    className=\"form-input\"\n                  />\n                  <p className=\"form-help\">List the therapeutic interventions used in this session</p>\n                </div>\n                <div>\n                  <label className=\"form-label\">\n                    Client Mood/Presentation\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={clientMood}\n                    onChange={(e) => setClientMood(e.target.value)}\n                    placeholder=\"e.g., Calm, anxious, engaged, withdrawn\"\n                    className=\"form-input\"\n                  />\n                  <p className=\"form-help\">Describe the client's overall mood and presentation</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Multimedia Upload Sidebar */}\n          <div className=\"space-y-6\">\n            {/* Upload Section */}\n            <div className=\"card\">\n              <div className=\"card-header\">\n                <h3 className=\"card-title\">Multimedia Files</h3>\n                <p className=\"card-subtitle\">Upload session artwork, recordings, and media</p>\n              </div>\n\n              <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-indigo-400 transition-colors duration-200\">\n                <input\n                  ref={fileInputRef}\n                  type=\"file\"\n                  multiple\n                  accept=\"image/*,video/*,audio/*\"\n                  onChange={handleFileUpload}\n                  className=\"hidden\"\n                />\n                <Upload className=\"h-10 w-10 text-gray-400 mx-auto mb-3\" />\n                <p className=\"text-sm font-medium text-gray-700 mb-1\">\n                  Upload session media\n                </p>\n                <p className=\"text-xs text-gray-500 mb-4\">\n                  Photos of artwork, audio recordings, or video documentation\n                </p>\n                <button\n                  type=\"button\"\n                  onClick={() => fileInputRef.current?.click()}\n                  disabled={isUploading}\n                  className=\"btn-primary\"\n                >\n                  {isUploading ? (\n                    <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                  ) : (\n                    <Upload className=\"h-4 w-4 mr-2\" />\n                  )}\n                  Choose Files\n                </button>\n                <p className=\"text-xs text-gray-500 mt-3\">\n                  <strong>Supported:</strong> JPG, PNG, GIF, MP4, MOV, AVI, MP3, WAV, M4A<br />\n                  <strong>Max size:</strong> 20MB per file\n                </p>\n              </div>\n\n              {/* Uploaded Files */}\n              {multimediaFiles.length > 0 && (\n                <div className=\"mt-6 space-y-4\">\n                  <h4 className=\"text-sm font-medium text-gray-900\">Uploaded Files</h4>\n                  {multimediaFiles.map((file) => (\n                    <div key={file.id} className=\"border border-gray-200 rounded-lg p-4\">\n                      <div className=\"flex items-start justify-between mb-2\">\n                        <div className=\"flex items-center space-x-2\">\n                          {getFileIcon(file.type)}\n                          <span className=\"text-sm font-medium text-gray-900 truncate\">\n                            {file.file.name}\n                          </span>\n                        </div>\n                        <button\n                          onClick={() => removeFile(file.id)}\n                          className=\"text-gray-400 hover:text-gray-600\"\n                        >\n                          <X className=\"h-4 w-4\" />\n                        </button>\n                      </div>\n\n                      {file.type === 'image' && file.preview && (\n                        <img\n                          src={file.preview}\n                          alt=\"Preview\"\n                          className=\"w-full h-32 object-cover rounded-md mb-2\"\n                        />\n                      )}\n\n                      <p className=\"text-xs text-gray-500 mb-2\">\n                        {(file.file.size / 1024 / 1024).toFixed(2)} MB\n                      </p>\n\n                      {/* AI Description */}\n                      {file.aiDescription ? (\n                        <div className=\"bg-blue-50 border border-blue-200 rounded-md p-3\">\n                          <p className=\"text-xs font-medium text-blue-800 mb-1\">AI Description:</p>\n                          <p className=\"text-xs text-blue-700\">{file.aiDescription}</p>\n                        </div>\n                      ) : (\n                        <button\n                          onClick={() => generateAIDescription(file.id)}\n                          disabled={file.isGeneratingAI}\n                          className=\"w-full inline-flex items-center justify-center px-3 py-2 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200 disabled:opacity-50\"\n                        >\n                          {file.isGeneratingAI ? (\n                            <>\n                              <Loader2 className=\"h-3 w-3 mr-1 animate-spin\" />\n                              Generating...\n                            </>\n                          ) : (\n                            <>\n                              <Sparkles className=\"h-3 w-3 mr-1\" />\n                              Generate AI Description\n                            </>\n                          )}\n                        </button>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n\n            {/* Tips */}\n            <div className=\"bg-indigo-50 border border-indigo-200 rounded-lg p-5\">\n              <h4 className=\"text-sm font-semibold text-indigo-900 mb-3 flex items-center\">\n                <span className=\"text-base mr-2\">💡</span>\n                Documentation Tips\n              </h4>\n              <ul className=\"text-sm text-indigo-800 space-y-2\">\n                <li className=\"flex items-start\">\n                  <span className=\"text-indigo-600 mr-2 mt-0.5\">•</span>\n                  Use AI suggestions to generate professional clinical language\n                </li>\n                <li className=\"flex items-start\">\n                  <span className=\"text-indigo-600 mr-2 mt-0.5\">•</span>\n                  Upload photos of client artwork to document creative expression\n                </li>\n                <li className=\"flex items-start\">\n                  <span className=\"text-indigo-600 mr-2 mt-0.5\">•</span>\n                  Record audio for music therapy sessions and verbal processing\n                </li>\n                <li className=\"flex items-start\">\n                  <span className=\"text-indigo-600 mr-2 mt-0.5\">•</span>\n                  All data is encrypted and HIPAA-compliant for client privacy\n                </li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AA2Be,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACrF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,mBAAmB;IACnB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,sBAAsB;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC3E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,iBAAiB;IACjB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAE7E,mBAAmB;IACnB,MAAM,UAAU;QACd;YAAE,IAAI;YAAK,MAAM;QAAW;QAC5B;YAAE,IAAI;YAAK,MAAM;QAAa;QAC9B;YAAE,IAAI;YAAK,MAAM;QAAU;QAC3B;YAAE,IAAI;YAAK,MAAM;QAAW;KAC7B;IAED,MAAM,mBAAmB,OAAO;QAC9B,MAAM,QAAQ,MAAM,MAAM,CAAC,KAAK;QAChC,IAAI,CAAC,OAAO;QAEZ,eAAe;QAEf,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,MAAM,WAAW,YAAY,KAAK,IAAI;YAEtC,IAAI,UAAU;gBACZ,MAAM,UAA0B;oBAC9B,IAAI,KAAK,GAAG,GAAG,QAAQ,KAAK;oBAC5B;oBACA,MAAM;oBACN,SAAS,aAAa,UAAU,IAAI,eAAe,CAAC,QAAQ;gBAC9D;gBAEA,mBAAmB,CAAA,OAAQ;2BAAI;wBAAM;qBAAQ;YAC/C;QACF;QAEA,eAAe;QACf,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,SAAS,UAAU,CAAC,WAAW,OAAO;QAC1C,IAAI,SAAS,UAAU,CAAC,WAAW,OAAO;QAC1C,IAAI,SAAS,UAAU,CAAC,WAAW,OAAO;QAC1C,OAAO;IACT;IAEA,MAAM,aAAa,CAAC;QAClB,mBAAmB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACvD;IAEA,MAAM,wBAAwB,OAAO;QACnC,MAAM,OAAO,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAChD,IAAI,CAAC,MAAM;QAEX,mBAAmB,CAAA,OACjB,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS;oBAAE,GAAG,CAAC;oBAAE,gBAAgB;gBAAK,IAAI;QAGnE,uBAAuB;QACvB,WAAW;YACT,MAAM,mBAAmB;gBACvB,OAAO;gBACP,OAAO;gBACP,OAAO;YACT;YAEA,mBAAmB,CAAA,OACjB,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS;wBAC9B,GAAG,CAAC;wBACJ,gBAAgB;wBAChB,eAAe,gBAAgB,CAAC,EAAE,IAAI,CAAC;oBACzC,IAAI;QAER,GAAG;IACL;IAEA,MAAM,uBAAuB,OAAO,OAAe;QACjD,QAAQ,GAAG,CAAC,uCAAuC,OAAO,YAAY;QACtE,kBAAkB;QAElB,IAAI;YACF,uBAAuB;YACvB,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM,MAAM,WAAW;oBACvB,SAAS,WAAW,CAAC,WAAW,EAAE,MAAM,kDAAkD,CAAC;oBAC3F,aAAa;wBACX,WAAW;wBACX,aAAa;wBACb,UAAU,mBAAmB;wBAC7B,UAAU;oBACZ;gBACF;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,QAAQ,GAAG,CAAC,8BAA8B,OAAO,SAAS;gBAC1D,iBAAiB,CAAA,OAAQ,CAAC;wBACxB,GAAG,IAAI;wBACP,CAAC,MAAM,EAAE,KAAK,UAAU,IAAI;oBAC9B,CAAC;YACH,OAAO;gBACL,QAAQ,GAAG,CAAC,8BAA8B,SAAS,MAAM;gBACzD,4CAA4C;gBAC5C,MAAM,kBAA2C;oBAC/C,WAAW;oBACX,YAAY;oBACZ,MAAM;gBACR;gBAEA,iBAAiB,CAAA,OAAQ,CAAC;wBACxB,GAAG,IAAI;wBACP,CAAC,MAAM,EAAE,eAAe,CAAC,MAAM,IAAI;oBACrC,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YAEjD,wCAAwC;YACxC,MAAM,kBAA2C;gBAC/C,WAAW;gBACX,YAAY;gBACZ,MAAM;YACR;YAEA,iBAAiB,CAAA,OAAQ,CAAC;oBACxB,GAAG,IAAI;oBACP,CAAC,MAAM,EAAE,eAAe,CAAC,MAAM,IAAI;gBACrC,CAAC;QACH,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,aAAa,aAAa,CAAC,MAAM;QACvC,QAAQ,GAAG,CAAC,sCAAsC,OAAO,eAAe;QAExE,IAAI,CAAC,YAAY;YACf,QAAQ,GAAG,CAAC,kCAAkC;YAC9C;QACF;QAEA,OAAQ;YACN,KAAK;gBACH,QAAQ,GAAG,CAAC;gBACZ,aAAa,CAAA,OAAQ,OAAO,CAAC,OAAO,SAAS,EAAE,IAAI;gBACnD;YACF,KAAK;gBACH,QAAQ,GAAG,CAAC;gBACZ,cAAc,CAAA,OAAQ,OAAO,CAAC,OAAO,SAAS,EAAE,IAAI;gBACpD;YACF,KAAK;gBACH,QAAQ,GAAG,CAAC;gBACZ,QAAQ,CAAA,OAAQ,OAAO,CAAC,OAAO,SAAS,EAAE,IAAI;gBAC9C;YACF;gBACE,QAAQ,GAAG,CAAC,kBAAkB;QAClC;QAEA,uCAAuC;QACvC,iBAAiB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAG,CAAC;IACpD;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAS;oBAAC,WAAU;;;;;;YAC9B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAChC,cAAA,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;;;;;;;0CAEnD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,WAAU;;0DAEV,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGlC,6LAAC;wCACC,MAAK;wCACL,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGnC,6LAAC;wCACC,MAAK;wCACL,WAAU;;0DAEV,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlD,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAa;;;;;;8DAC3B,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAE/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAAa;;;;;;sEAG9B,6LAAC;4DACC,OAAO;4DACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4DACjD,WAAU;4DACV,QAAQ;;8EAER,6LAAC;oEAAO,OAAM;8EAAG;;;;;;gEAChB,QAAQ,GAAG,CAAC,CAAA,uBACX,6LAAC;wEAAuB,OAAO,OAAO,EAAE;kFACrC,OAAO,IAAI;uEADD,OAAO,EAAE;;;;;;;;;;;wDAKzB,CAAC,gCACA,6LAAC;4DAAE,WAAU;sEAAY;;;;;;;;;;;;8DAG7B,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAAa;;;;;;sEAG9B,6LAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4DAC9C,WAAU;4DACV,QAAQ;;;;;;sEAEV,6LAAC;4DAAE,WAAU;sEAAY;;;;;;;;;;;;8DAE3B,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAAa;;;;;;sEAG9B,6LAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;4DAClD,aAAY;4DACZ,WAAU;4DACV,KAAI;4DACJ,KAAI;;;;;;sEAEN,6LAAC;4DAAE,WAAU;sEAAY;;;;;;;;;;;;8DAE3B,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAAa;;;;;;sEAG9B,6LAAC;4DACC,OAAO;4DACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4DAC3C,WAAU;;8EAEV,6LAAC;oEAAO,OAAM;8EAAO;;;;;;8EACrB,6LAAC;oEAAO,OAAM;8EAAM;;;;;;8EACpB,6LAAC;oEAAO,OAAM;8EAAO;;;;;;;;;;;;sEAEvB,6LAAC;4DAAE,WAAU;sEAAY;;;;;;;;;;;;;;;;;;;;;;;;8CAM/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAa;;;;;;8DAC3B,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAI/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;8DAAa;;;;;;8DAG9B,6LAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,MAAM;oDACN,aAAY;oDACZ,WAAU;;;;;;8DAEZ,6LAAC;oDAAE,WAAU;8DAAY;;;;;;;;;;;;sDAI3B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;sEAAa;;;;;;sEAG9B,6LAAC;4DACC,MAAK;4DACL,SAAS,IAAM,qBAAqB,aAAa;4DACjD,UAAU;4DACV,WAAU;;gEAET,+BACC,6LAAC,oNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;yFAEnB,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACpB;;;;;;;;;;;;;8DAIN,6LAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oDAC5C,MAAM;oDACN,aAAY;oDACZ,WAAU;;;;;;8DAEZ,6LAAC;oDAAE,WAAU;8DAAY;;;;;;gDACxB,cAAc,SAAS,kBACtB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;kFAAyC;;;;;;kFACtD,6LAAC;wEAAE,WAAU;kFAAyC,cAAc,SAAS;;;;;;;;;;;;0EAE/E,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,SAAS,IAAM,mBAAmB;wEAClC,WAAU;kFACX;;;;;;kFAGD,6LAAC;wEACC,SAAS,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oFAAE,GAAG,IAAI;oFAAE,WAAW;gFAAG,CAAC;wEACnE,WAAU;kFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAUX,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;sEAAa;;;;;;sEAG9B,6LAAC;4DACC,MAAK;4DACL,SAAS,IAAM,qBAAqB,cAAc;4DAClD,UAAU;4DACV,WAAU;;gEAET,+BACC,6LAAC,oNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;yFAEnB,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACpB;;;;;;;;;;;;;8DAIN,6LAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,MAAM;oDACN,aAAY;oDACZ,WAAU;;;;;;8DAEZ,6LAAC;oDAAE,WAAU;8DAAY;;;;;;gDACxB,cAAc,UAAU,kBACvB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;kFAAyC;;;;;;kFACtD,6LAAC;wEAAE,WAAU;kFAAyC,cAAc,UAAU;;;;;;;;;;;;0EAEhF,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,SAAS,IAAM,mBAAmB;wEAClC,WAAU;kFACX;;;;;;kFAGD,6LAAC;wEACC,SAAS,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oFAAE,GAAG,IAAI;oFAAE,YAAY;gFAAG,CAAC;wEACpE,WAAU;kFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAUX,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;sEAAa;;;;;;sEAG9B,6LAAC;4DACC,MAAK;4DACL,SAAS,IAAM,qBAAqB,QAAQ;4DAC5C,UAAU;4DACV,WAAU;;gEAET,+BACC,6LAAC,oNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;yFAEnB,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACpB;;;;;;;;;;;;;8DAIN,6LAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACvC,MAAM;oDACN,aAAY;oDACZ,WAAU;;;;;;8DAEZ,6LAAC;oDAAE,WAAU;8DAAY;;;;;;gDACxB,cAAc,IAAI,kBACjB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;kFAAyC;;;;;;kFACtD,6LAAC;wEAAE,WAAU;kFAAyC,cAAc,IAAI;;;;;;;;;;;;0EAE1E,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,SAAS,IAAM,mBAAmB;wEAClC,WAAU;kFACX;;;;;;kFAGD,6LAAC;wEACC,SAAS,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oFAAE,GAAG,IAAI;oFAAE,MAAM;gFAAG,CAAC;wEAC9D,WAAU;kFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAUX,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAAa;;;;;;sEAG9B,6LAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;4DAChD,aAAY;4DACZ,WAAU;;;;;;sEAEZ,6LAAC;4DAAE,WAAU;sEAAY;;;;;;;;;;;;8DAE3B,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAAa;;;;;;sEAG9B,6LAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,aAAY;4DACZ,WAAU;;;;;;sEAEZ,6LAAC;4DAAE,WAAU;sEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOjC,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAa;;;;;;8DAC3B,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAG/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,KAAK;oDACL,MAAK;oDACL,QAAQ;oDACR,QAAO;oDACP,UAAU;oDACV,WAAU;;;;;;8DAEZ,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAE,WAAU;8DAAyC;;;;;;8DAGtD,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAG1C,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,aAAa,OAAO,EAAE;oDACrC,UAAU;oDACV,WAAU;;wDAET,4BACC,6LAAC,oNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;iFAEnB,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAClB;;;;;;;8DAGJ,6LAAC;oDAAE,WAAU;;sEACX,6LAAC;sEAAO;;;;;;wDAAmB;sEAA4C,6LAAC;;;;;sEACxE,6LAAC;sEAAO;;;;;;wDAAkB;;;;;;;;;;;;;wCAK7B,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;gDACjD,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC;wDAAkB,WAAU;;0EAC3B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;4EACZ,YAAY,KAAK,IAAI;0FACtB,6LAAC;gFAAK,WAAU;0FACb,KAAK,IAAI,CAAC,IAAI;;;;;;;;;;;;kFAGnB,6LAAC;wEACC,SAAS,IAAM,WAAW,KAAK,EAAE;wEACjC,WAAU;kFAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;;;;;;;;;;;;4DAIhB,KAAK,IAAI,KAAK,WAAW,KAAK,OAAO,kBACpC,6LAAC;gEACC,KAAK,KAAK,OAAO;gEACjB,KAAI;gEACJ,WAAU;;;;;;0EAId,6LAAC;gEAAE,WAAU;;oEACV,CAAC,KAAK,IAAI,CAAC,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;oEAAG;;;;;;;4DAI5C,KAAK,aAAa,iBACjB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;kFAAyC;;;;;;kFACtD,6LAAC;wEAAE,WAAU;kFAAyB,KAAK,aAAa;;;;;;;;;;;qFAG1D,6LAAC;gEACC,SAAS,IAAM,sBAAsB,KAAK,EAAE;gEAC5C,UAAU,KAAK,cAAc;gEAC7B,WAAU;0EAET,KAAK,cAAc,iBAClB;;sFACE,6LAAC,oNAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;wEAA8B;;iGAInD;;sFACE,6LAAC,6MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;uDA/CrC,KAAK,EAAE;;;;;;;;;;;;;;;;;8CA4DzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAiB;;;;;;gDAAS;;;;;;;sDAG5C,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAK,WAAU;sEAA8B;;;;;;wDAAQ;;;;;;;8DAGxD,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAK,WAAU;sEAA8B;;;;;;wDAAQ;;;;;;;8DAGxD,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAK,WAAU;sEAA8B;;;;;;wDAAQ;;;;;;;8DAGxD,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAK,WAAU;sEAA8B;;;;;;wDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxE;GAlqBwB;KAAA", "debugId": null}}]}