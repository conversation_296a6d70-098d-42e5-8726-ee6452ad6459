// Performance monitoring and error tracking for VistaNotes

export interface PerformanceMetric {
  name: string;
  value: number;
  unit: 'ms' | 'bytes' | 'count';
  timestamp: Date;
  userId?: string;
  sessionId?: string;
  metadata?: Record<string, any>;
}

export interface ErrorEvent {
  message: string;
  stack?: string;
  userId?: string;
  sessionId?: string;
  url?: string;
  userAgent?: string;
  timestamp: Date;
  severity: 'low' | 'medium' | 'high' | 'critical';
  metadata?: Record<string, any>;
}

class MonitoringService {
  private metrics: PerformanceMetric[] = [];
  private errors: ErrorEvent[] = [];
  private maxStoredItems = 1000;

  // Performance tracking
  trackMetric(metric: Omit<PerformanceMetric, 'timestamp'>) {
    const fullMetric: PerformanceMetric = {
      ...metric,
      timestamp: new Date(),
    };

    this.metrics.push(fullMetric);
    
    // Keep only recent metrics
    if (this.metrics.length > this.maxStoredItems) {
      this.metrics = this.metrics.slice(-this.maxStoredItems);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`[METRIC] ${metric.name}: ${metric.value}${metric.unit}`);
    }

    // In production, send to monitoring service
    this.sendToMonitoringService(fullMetric);
  }

  // Error tracking
  trackError(error: Omit<ErrorEvent, 'timestamp'>) {
    const fullError: ErrorEvent = {
      ...error,
      timestamp: new Date(),
    };

    this.errors.push(fullError);
    
    // Keep only recent errors
    if (this.errors.length > this.maxStoredItems) {
      this.errors = this.errors.slice(-this.maxStoredItems);
    }

    // Log to console
    console.error(`[ERROR] ${error.severity.toUpperCase()}: ${error.message}`, error);

    // In production, send to error tracking service
    this.sendToErrorService(fullError);
  }

  // API response time tracking
  async trackApiCall<T>(
    apiName: string,
    apiCall: () => Promise<T>,
    userId?: string
  ): Promise<T> {
    const startTime = performance.now();
    
    try {
      const result = await apiCall();
      const duration = performance.now() - startTime;
      
      this.trackMetric({
        name: `api_response_time_${apiName}`,
        value: duration,
        unit: 'ms',
        userId,
        metadata: { success: true }
      });

      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      this.trackMetric({
        name: `api_response_time_${apiName}`,
        value: duration,
        unit: 'ms',
        userId,
        metadata: { success: false }
      });

      this.trackError({
        message: `API call failed: ${apiName}`,
        stack: error instanceof Error ? error.stack : undefined,
        userId,
        severity: 'medium',
        metadata: { apiName, duration }
      });

      throw error;
    }
  }

  // File upload tracking
  trackFileUpload(
    fileName: string,
    fileSize: number,
    uploadTime: number,
    success: boolean,
    userId?: string
  ) {
    this.trackMetric({
      name: 'file_upload_time',
      value: uploadTime,
      unit: 'ms',
      userId,
      metadata: { fileName, fileSize, success }
    });

    this.trackMetric({
      name: 'file_upload_size',
      value: fileSize,
      unit: 'bytes',
      userId,
      metadata: { fileName, success }
    });

    if (!success) {
      this.trackError({
        message: `File upload failed: ${fileName}`,
        userId,
        severity: 'medium',
        metadata: { fileName, fileSize, uploadTime }
      });
    }
  }

  // AI suggestion tracking
  trackAiSuggestion(
    suggestionType: string,
    responseTime: number,
    success: boolean,
    userId?: string,
    confidence?: number
  ) {
    this.trackMetric({
      name: 'ai_suggestion_time',
      value: responseTime,
      unit: 'ms',
      userId,
      metadata: { suggestionType, success, confidence }
    });

    if (!success) {
      this.trackError({
        message: `AI suggestion failed: ${suggestionType}`,
        userId,
        severity: 'medium',
        metadata: { suggestionType, responseTime }
      });
    }
  }

  // Database query tracking
  trackDatabaseQuery(
    queryType: string,
    duration: number,
    success: boolean,
    userId?: string
  ) {
    this.trackMetric({
      name: `db_query_time_${queryType}`,
      value: duration,
      unit: 'ms',
      userId,
      metadata: { success }
    });

    if (!success) {
      this.trackError({
        message: `Database query failed: ${queryType}`,
        userId,
        severity: 'high',
        metadata: { queryType, duration }
      });
    }
  }

  // Get performance summary
  getPerformanceSummary(timeRange: 'hour' | 'day' | 'week' = 'hour') {
    const now = new Date();
    const cutoff = new Date();
    
    switch (timeRange) {
      case 'hour':
        cutoff.setHours(now.getHours() - 1);
        break;
      case 'day':
        cutoff.setDate(now.getDate() - 1);
        break;
      case 'week':
        cutoff.setDate(now.getDate() - 7);
        break;
    }

    const recentMetrics = this.metrics.filter(m => m.timestamp >= cutoff);
    const recentErrors = this.errors.filter(e => e.timestamp >= cutoff);

    // Calculate averages by metric name
    const metricSummary: Record<string, { avg: number; count: number; unit: string }> = {};
    
    recentMetrics.forEach(metric => {
      if (!metricSummary[metric.name]) {
        metricSummary[metric.name] = { avg: 0, count: 0, unit: metric.unit };
      }
      metricSummary[metric.name].avg += metric.value;
      metricSummary[metric.name].count += 1;
    });

    // Calculate final averages
    Object.keys(metricSummary).forEach(key => {
      metricSummary[key].avg = metricSummary[key].avg / metricSummary[key].count;
    });

    // Error summary by severity
    const errorSummary = {
      low: recentErrors.filter(e => e.severity === 'low').length,
      medium: recentErrors.filter(e => e.severity === 'medium').length,
      high: recentErrors.filter(e => e.severity === 'high').length,
      critical: recentErrors.filter(e => e.severity === 'critical').length,
    };

    return {
      timeRange,
      metrics: metricSummary,
      errors: errorSummary,
      totalMetrics: recentMetrics.length,
      totalErrors: recentErrors.length,
    };
  }

  private async sendToMonitoringService(metric: PerformanceMetric) {
    // In production, send to monitoring service like DataDog, New Relic, etc.
    if (process.env.NODE_ENV === 'production') {
      try {
        // Example: await fetch('/api/monitoring/metrics', { method: 'POST', body: JSON.stringify(metric) });
      } catch (error) {
        console.error('Failed to send metric to monitoring service:', error);
      }
    }
  }

  private async sendToErrorService(error: ErrorEvent) {
    // In production, send to error tracking service like Sentry, Bugsnag, etc.
    if (process.env.NODE_ENV === 'production') {
      try {
        // Example: await fetch('/api/monitoring/errors', { method: 'POST', body: JSON.stringify(error) });
      } catch (err) {
        console.error('Failed to send error to tracking service:', err);
      }
    }
  }
}

// Singleton instance
export const monitoring = new MonitoringService();

// Utility functions for common tracking scenarios
export const trackApiCall = monitoring.trackApiCall.bind(monitoring);
export const trackError = monitoring.trackError.bind(monitoring);
export const trackMetric = monitoring.trackMetric.bind(monitoring);
export const trackFileUpload = monitoring.trackFileUpload.bind(monitoring);
export const trackAiSuggestion = monitoring.trackAiSuggestion.bind(monitoring);
export const trackDatabaseQuery = monitoring.trackDatabaseQuery.bind(monitoring);
