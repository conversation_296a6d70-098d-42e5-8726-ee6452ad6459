import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const clientId = searchParams.get('clientId');
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');

    const where: any = {
      therapistId: session.user.id,
    };

    if (clientId) {
      where.clientId = clientId;
    }

    if (status) {
      where.isFinalized = status === 'completed';
    }

    const notes = await db.sessionNote.findMany({
      where,
      include: {
        client: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          }
        },
        multimedia: {
          select: {
            id: true,
            fileType: true,
            fileName: true,
          }
        },
        _count: {
          select: {
            multimedia: true,
            aiSuggestions: true,
          }
        }
      },
      orderBy: {
        sessionDate: 'desc'
      },
      take: limit,
    });

    return NextResponse.json(notes);
  } catch (error) {
    console.error('Error fetching session notes:', error);
    return NextResponse.json(
      { error: 'Failed to fetch session notes' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      clientId,
      sessionDate,
      sessionDuration,
      noteType,
      subjective,
      objective,
      assessment,
      plan,
      interventions,
      clientMood,
      isFinalized = false
    } = body;

    // Validate required fields
    if (!clientId || !sessionDate) {
      return NextResponse.json(
        { error: 'Missing required fields: clientId and sessionDate' },
        { status: 400 }
      );
    }

    // Create the session note
    const note = await db.sessionNote.create({
      data: {
        clientId,
        therapistId: session.user.id,
        sessionDate: new Date(sessionDate),
        sessionDuration: sessionDuration ? parseInt(sessionDuration) : null,
        noteType: noteType || 'SOAP',
        subjective,
        objective,
        assessment,
        plan,
        interventions,
        clientMood,
        isFinalized,
      },
      include: {
        client: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          }
        },
        multimedia: true,
      }
    });

    // Log the action for audit trail
    await db.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'CREATE',
        resourceType: 'SessionNote',
        resourceId: note.id,
        details: {
          clientId,
          noteType: note.noteType,
          isFinalized,
        },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      }
    });

    return NextResponse.json(note, { status: 201 });
  } catch (error) {
    console.error('Error creating session note:', error);
    return NextResponse.json(
      { error: 'Failed to create session note' },
      { status: 500 }
    );
  }
}
