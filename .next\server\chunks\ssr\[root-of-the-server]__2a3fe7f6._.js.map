{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VistaNotes/src/app/analytics/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { \n  <PERSON><PERSON><PERSON>, \n  <PERSON>, \n  LineChart, \n  Line, \n  XAxis, \n  YAxis, \n  CartesianGrid, \n  <PERSON>lt<PERSON>, \n  Legend, \n  ResponsiveContainer,\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  Cell\n} from \"recharts\";\nimport { \n  TrendingUp, \n  Users, \n  FileText, \n  Clock, \n  Activity,\n  AlertTriangle,\n  CheckCircle,\n  BarChart3\n} from \"lucide-react\";\n\nexport default function Analytics() {\n  const [timeRange, setTimeRange] = useState('month');\n  const [isLoading, setIsLoading] = useState(true);\n  \n  // Mock analytics data - in production, this would come from APIs\n  const [analyticsData] = useState({\n    overview: {\n      totalClients: 24,\n      activeClients: 18,\n      totalSessions: 156,\n      avgSessionDuration: 47,\n      completionRate: 94,\n      aiUsageRate: 78\n    },\n    sessionTrends: [\n      { month: 'Jan', sessions: 45, duration: 46 },\n      { month: 'Feb', sessions: 52, duration: 48 },\n      { month: 'Mar', sessions: 48, duration: 45 },\n      { month: 'Apr', sessions: 59, duration: 49 },\n      { month: 'May', sessions: 61, duration: 47 },\n      { month: 'Jun', sessions: 58, duration: 46 }\n    ],\n    aiUsage: [\n      { feature: 'Objective Notes', usage: 85, timeSaved: 12 },\n      { feature: 'Assessment', usage: 72, timeSaved: 8 },\n      { feature: 'Treatment Plans', usage: 68, timeSaved: 15 },\n      { feature: 'Media Analysis', usage: 45, timeSaved: 20 }\n    ],\n    clientProgress: [\n      { metric: 'Emotional Expression', improvement: 23 },\n      { metric: 'Creative Engagement', improvement: 18 },\n      { metric: 'Self-Awareness', improvement: 31 },\n      { metric: 'Coping Skills', improvement: 15 }\n    ],\n    noteTypes: [\n      { name: 'SOAP', value: 78, color: '#4f46e5' },\n      { name: 'DAP', value: 15, color: '#06b6d4' },\n      { name: 'BIRP', value: 7, color: '#10b981' }\n    ],\n    performance: {\n      avgNoteTime: 8.5, // minutes\n      aiAcceptanceRate: 82,\n      errorRate: 0.3,\n      systemUptime: 99.8\n    }\n  });\n\n  useEffect(() => {\n    // Simulate loading\n    setTimeout(() => setIsLoading(false), 1000);\n  }, []);\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-8 bg-gray-200 rounded w-1/4 mb-8\"></div>\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n              {[...Array(4)].map((_, i) => (\n                <div key={i} className=\"bg-white rounded-lg shadow p-6\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                  <div className=\"h-8 bg-gray-200 rounded w-1/2\"></div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"flex-shrink-0 mr-8\">\n                <h1 className=\"text-2xl font-bold text-gray-900\">VistaNotes</h1>\n              </Link>\n              <nav className=\"hidden md:flex space-x-8\">\n                <Link href=\"/dashboard\" className=\"text-gray-500 hover:text-gray-900\">\n                  Dashboard\n                </Link>\n                <Link href=\"/clients\" className=\"text-gray-500 hover:text-gray-900\">\n                  Clients\n                </Link>\n                <Link href=\"/notes\" className=\"text-gray-500 hover:text-gray-900\">\n                  Session Notes\n                </Link>\n                <Link href=\"/analytics\" className=\"text-indigo-600 font-medium\">\n                  Analytics\n                </Link>\n              </nav>\n            </div>\n            <select\n              value={timeRange}\n              onChange={(e) => setTimeRange(e.target.value)}\n              className=\"border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n            >\n              <option value=\"week\">Last Week</option>\n              <option value=\"month\">Last Month</option>\n              <option value=\"quarter\">Last Quarter</option>\n              <option value=\"year\">Last Year</option>\n            </select>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Page Header */}\n        <div className=\"mb-8\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">Practice Analytics</h2>\n          <p className=\"text-gray-600\">Insights into your Creative Arts Therapy practice</p>\n        </div>\n\n        {/* Overview Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <Users className=\"h-8 w-8 text-indigo-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Active Clients</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {analyticsData.overview.activeClients}\n                </p>\n                <p className=\"text-xs text-gray-500\">\n                  of {analyticsData.overview.totalClients} total\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <FileText className=\"h-8 w-8 text-green-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Sessions This Month</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {analyticsData.overview.totalSessions}\n                </p>\n                <p className=\"text-xs text-green-600 flex items-center\">\n                  <TrendingUp className=\"h-3 w-3 mr-1\" />\n                  +12% from last month\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <Clock className=\"h-8 w-8 text-blue-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Avg Note Time</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {analyticsData.performance.avgNoteTime}m\n                </p>\n                <p className=\"text-xs text-green-600\">\n                  -3.2m with AI assistance\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <Activity className=\"h-8 w-8 text-purple-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">AI Usage Rate</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {analyticsData.overview.aiUsageRate}%\n                </p>\n                <p className=\"text-xs text-purple-600\">\n                  Across all features\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Charts Row */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\">\n          {/* Session Trends */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Session Trends</h3>\n            <div className=\"h-64\">\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\n                <LineChart data={analyticsData.sessionTrends}>\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"month\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Legend />\n                  <Line \n                    type=\"monotone\" \n                    dataKey=\"sessions\" \n                    stroke=\"#4f46e5\" \n                    strokeWidth={2}\n                    name=\"Sessions\"\n                  />\n                  <Line \n                    type=\"monotone\" \n                    dataKey=\"duration\" \n                    stroke=\"#06b6d4\" \n                    strokeWidth={2}\n                    name=\"Avg Duration (min)\"\n                  />\n                </LineChart>\n              </ResponsiveContainer>\n            </div>\n          </div>\n\n          {/* Note Types Distribution */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Note Types Distribution</h3>\n            <div className=\"h-64\">\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\n                <PieChart>\n                  <Pie\n                    data={analyticsData.noteTypes}\n                    cx=\"50%\"\n                    cy=\"50%\"\n                    labelLine={false}\n                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\n                    outerRadius={80}\n                    fill=\"#8884d8\"\n                    dataKey=\"value\"\n                  >\n                    {analyticsData.noteTypes.map((entry, index) => (\n                      <Cell key={`cell-${index}`} fill={entry.color} />\n                    ))}\n                  </Pie>\n                  <Tooltip />\n                </PieChart>\n              </ResponsiveContainer>\n            </div>\n          </div>\n        </div>\n\n        {/* AI Usage and Client Progress */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\">\n          {/* AI Feature Usage */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">AI Feature Usage</h3>\n            <div className=\"space-y-4\">\n              {analyticsData.aiUsage.map((feature, index) => (\n                <div key={index} className=\"flex items-center justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center justify-between mb-1\">\n                      <span className=\"text-sm font-medium text-gray-900\">\n                        {feature.feature}\n                      </span>\n                      <span className=\"text-sm text-gray-500\">\n                        {feature.usage}% usage\n                      </span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div \n                        className=\"bg-indigo-600 h-2 rounded-full\" \n                        style={{ width: `${feature.usage}%` }}\n                      ></div>\n                    </div>\n                    <p className=\"text-xs text-gray-500 mt-1\">\n                      Saves avg {feature.timeSaved} min per note\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Client Progress Metrics */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Client Progress Overview</h3>\n            <div className=\"h-64\">\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\n                <BarChart data={analyticsData.clientProgress}>\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"metric\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Bar dataKey=\"improvement\" fill=\"#10b981\" />\n                </BarChart>\n              </ResponsiveContainer>\n            </div>\n          </div>\n        </div>\n\n        {/* System Performance */}\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">System Performance</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"flex items-center justify-center mb-2\">\n                <CheckCircle className=\"h-8 w-8 text-green-600\" />\n              </div>\n              <p className=\"text-2xl font-bold text-gray-900\">\n                {analyticsData.performance.systemUptime}%\n              </p>\n              <p className=\"text-sm text-gray-500\">System Uptime</p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"flex items-center justify-center mb-2\">\n                <BarChart3 className=\"h-8 w-8 text-blue-600\" />\n              </div>\n              <p className=\"text-2xl font-bold text-gray-900\">\n                {analyticsData.performance.aiAcceptanceRate}%\n              </p>\n              <p className=\"text-sm text-gray-500\">AI Acceptance Rate</p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"flex items-center justify-center mb-2\">\n                <AlertTriangle className=\"h-8 w-8 text-yellow-600\" />\n              </div>\n              <p className=\"text-2xl font-bold text-gray-900\">\n                {analyticsData.performance.errorRate}%\n              </p>\n              <p className=\"text-sm text-gray-500\">Error Rate</p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"flex items-center justify-center mb-2\">\n                <Clock className=\"h-8 w-8 text-purple-600\" />\n              </div>\n              <p className=\"text-2xl font-bold text-gray-900\">\n                {analyticsData.performance.avgNoteTime}m\n              </p>\n              <p className=\"text-sm text-gray-500\">Avg Note Time</p>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAnBA;;;;;;AA8Be,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,iEAAiE;IACjE,MAAM,CAAC,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/B,UAAU;YACR,cAAc;YACd,eAAe;YACf,eAAe;YACf,oBAAoB;YACpB,gBAAgB;YAChB,aAAa;QACf;QACA,eAAe;YACb;gBAAE,OAAO;gBAAO,UAAU;gBAAI,UAAU;YAAG;YAC3C;gBAAE,OAAO;gBAAO,UAAU;gBAAI,UAAU;YAAG;YAC3C;gBAAE,OAAO;gBAAO,UAAU;gBAAI,UAAU;YAAG;YAC3C;gBAAE,OAAO;gBAAO,UAAU;gBAAI,UAAU;YAAG;YAC3C;gBAAE,OAAO;gBAAO,UAAU;gBAAI,UAAU;YAAG;YAC3C;gBAAE,OAAO;gBAAO,UAAU;gBAAI,UAAU;YAAG;SAC5C;QACD,SAAS;YACP;gBAAE,SAAS;gBAAmB,OAAO;gBAAI,WAAW;YAAG;YACvD;gBAAE,SAAS;gBAAc,OAAO;gBAAI,WAAW;YAAE;YACjD;gBAAE,SAAS;gBAAmB,OAAO;gBAAI,WAAW;YAAG;YACvD;gBAAE,SAAS;gBAAkB,OAAO;gBAAI,WAAW;YAAG;SACvD;QACD,gBAAgB;YACd;gBAAE,QAAQ;gBAAwB,aAAa;YAAG;YAClD;gBAAE,QAAQ;gBAAuB,aAAa;YAAG;YACjD;gBAAE,QAAQ;gBAAkB,aAAa;YAAG;YAC5C;gBAAE,QAAQ;gBAAiB,aAAa;YAAG;SAC5C;QACD,WAAW;YACT;gBAAE,MAAM;gBAAQ,OAAO;gBAAI,OAAO;YAAU;YAC5C;gBAAE,MAAM;gBAAO,OAAO;gBAAI,OAAO;YAAU;YAC3C;gBAAE,MAAM;gBAAQ,OAAO;gBAAG,OAAO;YAAU;SAC5C;QACD,aAAa;YACX,aAAa;YACb,kBAAkB;YAClB,WAAW;YACX,cAAc;QAChB;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mBAAmB;QACnB,WAAW,IAAM,aAAa,QAAQ;IACxC,GAAG,EAAE;IAEL,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;oCAAY,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;mCAFP;;;;;;;;;;;;;;;;;;;;;;;;;;IAUxB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDACvB,cAAA,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;;;;;;kDAEnD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAoC;;;;;;0DAGtE,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAoC;;;;;;0DAGpE,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAoC;;;;;;0DAGlE,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;0CAKpE,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC5C,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,8OAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,8OAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,8OAAC;wCAAO,OAAM;kDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7B,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAI/B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DACV,cAAc,QAAQ,CAAC,aAAa;;;;;;8DAEvC,8OAAC;oDAAE,WAAU;;wDAAwB;wDAC/B,cAAc,QAAQ,CAAC,YAAY;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAMhD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DACV,cAAc,QAAQ,CAAC,aAAa;;;;;;8DAEvC,8OAAC;oDAAE,WAAU;;sEACX,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;0CAO/C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;;wDACV,cAAc,WAAW,CAAC,WAAW;wDAAC;;;;;;;8DAEzC,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;0CAO5C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;;wDACV,cAAc,QAAQ,CAAC,WAAW;wDAAC;;;;;;;8DAEtC,8OAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS/C,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;4CAAC,OAAM;4CAAO,QAAO;sDACvC,cAAA,8OAAC,qJAAA,CAAA,YAAS;gDAAC,MAAM,cAAc,aAAa;;kEAC1C,8OAAC,6JAAA,CAAA,gBAAa;wDAAC,iBAAgB;;;;;;kEAC/B,8OAAC,qJAAA,CAAA,QAAK;wDAAC,SAAQ;;;;;;kEACf,8OAAC,qJAAA,CAAA,QAAK;;;;;kEACN,8OAAC,uJAAA,CAAA,UAAO;;;;;kEACR,8OAAC,sJAAA,CAAA,SAAM;;;;;kEACP,8OAAC,oJAAA,CAAA,OAAI;wDACH,MAAK;wDACL,SAAQ;wDACR,QAAO;wDACP,aAAa;wDACb,MAAK;;;;;;kEAEP,8OAAC,oJAAA,CAAA,OAAI;wDACH,MAAK;wDACL,SAAQ;wDACR,QAAO;wDACP,aAAa;wDACb,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;4CAAC,OAAM;4CAAO,QAAO;sDACvC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;;kEACP,8OAAC,+IAAA,CAAA,MAAG;wDACF,MAAM,cAAc,SAAS;wDAC7B,IAAG;wDACH,IAAG;wDACH,WAAW;wDACX,OAAO,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAK,GAAG,KAAK,CAAC,EAAE,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;wDACtE,aAAa;wDACb,MAAK;wDACL,SAAQ;kEAEP,cAAc,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,sBACnC,8OAAC,oJAAA,CAAA,OAAI;gEAAuB,MAAM,MAAM,KAAK;+DAAlC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;kEAG9B,8OAAC,uJAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQlB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDACZ,cAAc,OAAO,CAAC,GAAG,CAAC,CAAC,SAAS,sBACnC,8OAAC;gDAAgB,WAAU;0DACzB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EACb,QAAQ,OAAO;;;;;;8EAElB,8OAAC;oEAAK,WAAU;;wEACb,QAAQ,KAAK;wEAAC;;;;;;;;;;;;;sEAGnB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO,GAAG,QAAQ,KAAK,CAAC,CAAC,CAAC;gEAAC;;;;;;;;;;;sEAGxC,8OAAC;4DAAE,WAAU;;gEAA6B;gEAC7B,QAAQ,SAAS;gEAAC;;;;;;;;;;;;;+CAjBzB;;;;;;;;;;;;;;;;0CA0BhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;4CAAC,OAAM;4CAAO,QAAO;sDACvC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;gDAAC,MAAM,cAAc,cAAc;;kEAC1C,8OAAC,6JAAA,CAAA,gBAAa;wDAAC,iBAAgB;;;;;;kEAC/B,8OAAC,qJAAA,CAAA,QAAK;wDAAC,SAAQ;;;;;;kEACf,8OAAC,qJAAA,CAAA,QAAK;;;;;kEACN,8OAAC,uJAAA,CAAA,UAAO;;;;;kEACR,8OAAC,mJAAA,CAAA,MAAG;wDAAC,SAAQ;wDAAc,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ1C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;0DAEzB,8OAAC;gDAAE,WAAU;;oDACV,cAAc,WAAW,CAAC,YAAY;oDAAC;;;;;;;0DAE1C,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAGvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;gDAAE,WAAU;;oDACV,cAAc,WAAW,CAAC,gBAAgB;oDAAC;;;;;;;0DAE9C,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAGvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;0DAE3B,8OAAC;gDAAE,WAAU;;oDACV,cAAc,WAAW,CAAC,SAAS;oDAAC;;;;;;;0DAEvC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAGvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDAAE,WAAU;;oDACV,cAAc,WAAW,CAAC,WAAW;oDAAC;;;;;;;0DAEzC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD", "debugId": null}}]}