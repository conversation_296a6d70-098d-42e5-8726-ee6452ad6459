import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create a test therapist user
  const hashedPassword = await bcrypt.hash('password123', 12)
  
  const therapist = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Dr. <PERSON>',
      role: 'CAT',
      passwordHash: hashedPassword,
      isActive: true,
    },
  })

  console.log('👩‍⚕️ Created therapist:', therapist.name)

  // Create test clients
  const clients = await Promise.all([
    prisma.client.upsert({
      where: { id: 'client-1' },
      update: {},
      create: {
        id: 'client-1',
        firstName: 'Sarah',
        lastName: 'M.',
        dateOfBirth: new Date('1995-03-15'),
        ehrId: 'EHR-001',
        isActive: true,
      },
    }),
    prisma.client.upsert({
      where: { id: 'client-2' },
      update: {},
      create: {
        id: 'client-2',
        firstName: '<PERSON>',
        lastName: 'R.',
        dateOfBirth: new Date('1988-07-22'),
        ehrId: 'EHR-002',
        isActive: true,
      },
    }),
    prisma.client.upsert({
      where: { id: 'client-3' },
      update: {},
      create: {
        id: 'client-3',
        firstName: 'Emma',
        lastName: 'L.',
        dateOfBirth: new Date('2001-11-08'),
        ehrId: 'EHR-003',
        isActive: true,
      },
    }),
    prisma.client.upsert({
      where: { id: 'client-4' },
      update: {},
      create: {
        id: 'client-4',
        firstName: 'David',
        lastName: 'K.',
        dateOfBirth: new Date('1992-05-30'),
        ehrId: 'EHR-004',
        isActive: false,
      },
    }),
  ])

  console.log('👥 Created clients:', clients.map(c => `${c.firstName} ${c.lastName}`).join(', '))

  // Create test session notes
  const sessionNotes = await Promise.all([
    prisma.sessionNote.create({
      data: {
        clientId: 'client-1',
        therapistId: therapist.id,
        sessionDate: new Date('2024-01-15'),
        sessionDuration: 45,
        noteType: 'SOAP',
        subjective: 'Client reported feeling "more creative" this week and expressed interest in trying new art materials.',
        objective: 'Client engaged actively in art-making process, demonstrating sustained attention for 30 minutes. Selected vibrant colors and created abstract forms with confident brushstrokes.',
        assessment: 'Client shows continued progress in emotional expression through creative media. Increased confidence in artistic choices and willingness to discuss emotional content.',
        plan: 'Continue weekly art therapy sessions focusing on emotional expression through color and form. Introduce new media (clay work) next session.',
        interventions: 'Art therapy, color exploration',
        clientMood: 'Engaged, optimistic',
        isFinalized: true,
      },
    }),
    prisma.sessionNote.create({
      data: {
        clientId: 'client-2',
        therapistId: therapist.id,
        sessionDate: new Date('2024-01-14'),
        sessionDuration: 50,
        noteType: 'SOAP',
        subjective: 'Client mentioned feeling anxious about upcoming work presentation.',
        objective: 'Client participated in movement therapy session. Initial movements were tentative, progressing to more fluid and expressive gestures.',
        assessment: 'Client demonstrates improved body awareness and emotional regulation through movement. Anxiety levels appear to decrease during creative expression.',
        plan: 'Continue movement therapy with focus on grounding techniques. Consider adding music therapy component.',
        interventions: 'Movement therapy, breathing exercises',
        clientMood: 'Initially anxious, calmer by session end',
        isFinalized: false,
      },
    }),
    prisma.sessionNote.create({
      data: {
        clientId: 'client-3',
        therapistId: therapist.id,
        sessionDate: new Date('2024-01-12'),
        sessionDuration: 40,
        noteType: 'SOAP',
        subjective: 'Client expressed excitement about recent progress and asked to share artwork with family.',
        objective: 'Client completed detailed drawing with fine motor control. Demonstrated patience and persistence throughout the creative process.',
        assessment: 'Significant improvement in focus and task completion. Client shows increased self-confidence and pride in creative work.',
        plan: 'Support client in preparing artwork for family sharing. Continue building on current momentum with similar structured activities.',
        interventions: 'Drawing therapy, positive reinforcement',
        clientMood: 'Excited, proud',
        isFinalized: true,
      },
    }),
  ])

  console.log('📝 Created session notes:', sessionNotes.length)

  // Create some AI suggestions
  await Promise.all([
    prisma.aiSuggestion.create({
      data: {
        sessionNoteId: sessionNotes[0].id,
        suggestionType: 'OBJECTIVE',
        originalText: 'Client made art',
        suggestedText: 'Client engaged actively in art-making process, demonstrating sustained attention for 30 minutes. Selected vibrant colors and created abstract forms with confident brushstrokes.',
        isAccepted: true,
      },
    }),
    prisma.aiSuggestion.create({
      data: {
        sessionNoteId: sessionNotes[1].id,
        suggestionType: 'ASSESSMENT',
        originalText: 'Client seemed better',
        suggestedText: 'Client demonstrates improved body awareness and emotional regulation through movement. Anxiety levels appear to decrease during creative expression.',
        isAccepted: true,
      },
    }),
  ])

  console.log('🤖 Created AI suggestions')

  console.log('✅ Database seeded successfully!')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })
