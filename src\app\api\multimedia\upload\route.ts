import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { uploadToS3 } from '@/lib/aws';
import { validateFileType, validateFileSize, getMediaType, MAX_FILE_SIZE_MB } from '@/lib/utils';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const sessionNoteId = formData.get('sessionNoteId') as string;
    const description = formData.get('description') as string;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    if (!sessionNoteId) {
      return NextResponse.json({ error: 'Session note ID required' }, { status: 400 });
    }

    // Validate file type and size
    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/gif',
      'video/mp4', 'video/mov', 'video/avi',
      'audio/mp3', 'audio/wav', 'audio/m4a'
    ];

    if (!validateFileType(file, allowedTypes)) {
      return NextResponse.json({ error: 'Invalid file type' }, { status: 400 });
    }

    if (!validateFileSize(file, MAX_FILE_SIZE_MB)) {
      return NextResponse.json({ 
        error: `File size exceeds ${MAX_FILE_SIZE_MB}MB limit` 
      }, { status: 400 });
    }

    // Verify session note exists and belongs to user
    const sessionNote = await db.sessionNote.findFirst({
      where: {
        id: sessionNoteId,
        therapistId: session.user.id,
      },
      include: {
        client: {
          select: { id: true }
        }
      }
    });

    if (!sessionNote) {
      return NextResponse.json({ error: 'Session note not found' }, { status: 404 });
    }

    // Convert file to buffer for S3 upload
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Upload to S3
    const uploadResult = await uploadToS3(
      buffer,
      file.name,
      file.type,
      sessionNote.client.id,
      sessionNoteId
    );

    // Get media type
    const mediaType = getMediaType(file.type);
    if (!mediaType) {
      return NextResponse.json({ error: 'Unsupported media type' }, { status: 400 });
    }

    // Save multimedia record to database
    const multimediaFile = await db.multimediaFile.create({
      data: {
        sessionNoteId,
        fileName: uploadResult.key.split('/').pop() || file.name,
        originalName: file.name,
        fileType: mediaType,
        fileSize: file.size,
        s3Key: uploadResult.key,
        s3Bucket: uploadResult.bucket,
        description,
      }
    });

    // Log the action for audit trail
    await db.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'CREATE',
        resourceType: 'MultimediaFile',
        resourceId: multimediaFile.id,
        details: {
          sessionNoteId,
          fileName: file.name,
          fileType: mediaType,
          fileSize: file.size,
        },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      }
    });

    return NextResponse.json({
      id: multimediaFile.id,
      fileName: multimediaFile.fileName,
      originalName: multimediaFile.originalName,
      fileType: multimediaFile.fileType,
      fileSize: multimediaFile.fileSize,
      uploadedAt: multimediaFile.uploadedAt,
      url: uploadResult.url, // Temporary URL for immediate access
    }, { status: 201 });

  } catch (error) {
    console.error('Error uploading multimedia file:', error);
    return NextResponse.json(
      { error: 'Failed to upload file' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const sessionNoteId = searchParams.get('sessionNoteId');

    if (!sessionNoteId) {
      return NextResponse.json({ error: 'Session note ID required' }, { status: 400 });
    }

    // Verify session note belongs to user
    const sessionNote = await db.sessionNote.findFirst({
      where: {
        id: sessionNoteId,
        therapistId: session.user.id,
      }
    });

    if (!sessionNote) {
      return NextResponse.json({ error: 'Session note not found' }, { status: 404 });
    }

    // Get multimedia files for the session note
    const multimediaFiles = await db.multimediaFile.findMany({
      where: {
        sessionNoteId,
      },
      orderBy: {
        uploadedAt: 'asc'
      }
    });

    return NextResponse.json(multimediaFiles);

  } catch (error) {
    console.error('Error fetching multimedia files:', error);
    return NextResponse.json(
      { error: 'Failed to fetch multimedia files' },
      { status: 500 }
    );
  }
}
