
/**
 * Client
**/

import * as runtime from '@prisma/client/runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model User
 * 
 */
export type User = $Result.DefaultSelection<Prisma.$UserPayload>
/**
 * Model Client
 * 
 */
export type Client = $Result.DefaultSelection<Prisma.$ClientPayload>
/**
 * Model SessionNote
 * 
 */
export type SessionNote = $Result.DefaultSelection<Prisma.$SessionNotePayload>
/**
 * Model MultimediaFile
 * 
 */
export type MultimediaFile = $Result.DefaultSelection<Prisma.$MultimediaFilePayload>
/**
 * Model AiSuggestion
 * 
 */
export type AiSuggestion = $Result.DefaultSelection<Prisma.$AiSuggestionPayload>
/**
 * Model AuditLog
 * 
 */
export type AuditLog = $Result.DefaultSelection<Prisma.$AuditLogPayload>

/**
 * Enums
 */
export namespace $Enums {
  export const UserRole: {
  CAT: 'CAT',
  PSYCHIATRIST: 'PSYCHIATRIST',
  CASE_MANAGER: 'CASE_MANAGER',
  ADMIN: 'ADMIN'
};

export type UserRole = (typeof UserRole)[keyof typeof UserRole]


export const NoteType: {
  SOAP: 'SOAP',
  DAP: 'DAP',
  BIRP: 'BIRP',
  GIRP: 'GIRP'
};

export type NoteType = (typeof NoteType)[keyof typeof NoteType]


export const MediaType: {
  IMAGE: 'IMAGE',
  VIDEO: 'VIDEO',
  AUDIO: 'AUDIO'
};

export type MediaType = (typeof MediaType)[keyof typeof MediaType]


export const SuggestionType: {
  DESCRIPTION: 'DESCRIPTION',
  INTERPRETATION: 'INTERPRETATION',
  OBJECTIVE: 'OBJECTIVE',
  ASSESSMENT: 'ASSESSMENT',
  PLAN: 'PLAN'
};

export type SuggestionType = (typeof SuggestionType)[keyof typeof SuggestionType]

}

export type UserRole = $Enums.UserRole

export const UserRole: typeof $Enums.UserRole

export type NoteType = $Enums.NoteType

export const NoteType: typeof $Enums.NoteType

export type MediaType = $Enums.MediaType

export const MediaType: typeof $Enums.MediaType

export type SuggestionType = $Enums.SuggestionType

export const SuggestionType: typeof $Enums.SuggestionType

/**
 * ##  Prisma Client ʲˢ
 *
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Users
 * const users = await prisma.user.findMany()
 * ```
 *
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   *
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more Users
   * const users = await prisma.user.findMany()
   * ```
   *
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<ClientOptions>, ExtArgs, $Utils.Call<Prisma.TypeMapCb<ClientOptions>, {
    extArgs: ExtArgs
  }>>

      /**
   * `prisma.user`: Exposes CRUD operations for the **User** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Users
    * const users = await prisma.user.findMany()
    * ```
    */
  get user(): Prisma.UserDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.client`: Exposes CRUD operations for the **Client** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Clients
    * const clients = await prisma.client.findMany()
    * ```
    */
  get client(): Prisma.ClientDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.sessionNote`: Exposes CRUD operations for the **SessionNote** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more SessionNotes
    * const sessionNotes = await prisma.sessionNote.findMany()
    * ```
    */
  get sessionNote(): Prisma.SessionNoteDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.multimediaFile`: Exposes CRUD operations for the **MultimediaFile** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more MultimediaFiles
    * const multimediaFiles = await prisma.multimediaFile.findMany()
    * ```
    */
  get multimediaFile(): Prisma.MultimediaFileDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.aiSuggestion`: Exposes CRUD operations for the **AiSuggestion** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more AiSuggestions
    * const aiSuggestions = await prisma.aiSuggestion.findMany()
    * ```
    */
  get aiSuggestion(): Prisma.AiSuggestionDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.auditLog`: Exposes CRUD operations for the **AuditLog** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more AuditLogs
    * const auditLogs = await prisma.auditLog.findMany()
    * ```
    */
  get auditLog(): Prisma.AuditLogDelegate<ExtArgs, ClientOptions>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 6.9.0
   * Query Engine version: 81e4af48011447c3cc503a190e86995b66d2a28e
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    User: 'User',
    Client: 'Client',
    SessionNote: 'SessionNote',
    MultimediaFile: 'MultimediaFile',
    AiSuggestion: 'AiSuggestion',
    AuditLog: 'AuditLog'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb<ClientOptions = {}> extends $Utils.Fn<{extArgs: $Extensions.InternalArgs }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
    globalOmitOptions: {
      omit: GlobalOmitOptions
    }
    meta: {
      modelProps: "user" | "client" | "sessionNote" | "multimediaFile" | "aiSuggestion" | "auditLog"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      User: {
        payload: Prisma.$UserPayload<ExtArgs>
        fields: Prisma.UserFieldRefs
        operations: {
          findUnique: {
            args: Prisma.UserFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.UserFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findFirst: {
            args: Prisma.UserFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.UserFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findMany: {
            args: Prisma.UserFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          create: {
            args: Prisma.UserCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          createMany: {
            args: Prisma.UserCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.UserCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          delete: {
            args: Prisma.UserDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          update: {
            args: Prisma.UserUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          deleteMany: {
            args: Prisma.UserDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.UserUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.UserUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          upsert: {
            args: Prisma.UserUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          aggregate: {
            args: Prisma.UserAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateUser>
          }
          groupBy: {
            args: Prisma.UserGroupByArgs<ExtArgs>
            result: $Utils.Optional<UserGroupByOutputType>[]
          }
          count: {
            args: Prisma.UserCountArgs<ExtArgs>
            result: $Utils.Optional<UserCountAggregateOutputType> | number
          }
        }
      }
      Client: {
        payload: Prisma.$ClientPayload<ExtArgs>
        fields: Prisma.ClientFieldRefs
        operations: {
          findUnique: {
            args: Prisma.ClientFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClientPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.ClientFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClientPayload>
          }
          findFirst: {
            args: Prisma.ClientFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClientPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.ClientFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClientPayload>
          }
          findMany: {
            args: Prisma.ClientFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClientPayload>[]
          }
          create: {
            args: Prisma.ClientCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClientPayload>
          }
          createMany: {
            args: Prisma.ClientCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.ClientCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClientPayload>[]
          }
          delete: {
            args: Prisma.ClientDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClientPayload>
          }
          update: {
            args: Prisma.ClientUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClientPayload>
          }
          deleteMany: {
            args: Prisma.ClientDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.ClientUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.ClientUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClientPayload>[]
          }
          upsert: {
            args: Prisma.ClientUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ClientPayload>
          }
          aggregate: {
            args: Prisma.ClientAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateClient>
          }
          groupBy: {
            args: Prisma.ClientGroupByArgs<ExtArgs>
            result: $Utils.Optional<ClientGroupByOutputType>[]
          }
          count: {
            args: Prisma.ClientCountArgs<ExtArgs>
            result: $Utils.Optional<ClientCountAggregateOutputType> | number
          }
        }
      }
      SessionNote: {
        payload: Prisma.$SessionNotePayload<ExtArgs>
        fields: Prisma.SessionNoteFieldRefs
        operations: {
          findUnique: {
            args: Prisma.SessionNoteFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SessionNotePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.SessionNoteFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SessionNotePayload>
          }
          findFirst: {
            args: Prisma.SessionNoteFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SessionNotePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.SessionNoteFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SessionNotePayload>
          }
          findMany: {
            args: Prisma.SessionNoteFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SessionNotePayload>[]
          }
          create: {
            args: Prisma.SessionNoteCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SessionNotePayload>
          }
          createMany: {
            args: Prisma.SessionNoteCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.SessionNoteCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SessionNotePayload>[]
          }
          delete: {
            args: Prisma.SessionNoteDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SessionNotePayload>
          }
          update: {
            args: Prisma.SessionNoteUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SessionNotePayload>
          }
          deleteMany: {
            args: Prisma.SessionNoteDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.SessionNoteUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.SessionNoteUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SessionNotePayload>[]
          }
          upsert: {
            args: Prisma.SessionNoteUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SessionNotePayload>
          }
          aggregate: {
            args: Prisma.SessionNoteAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateSessionNote>
          }
          groupBy: {
            args: Prisma.SessionNoteGroupByArgs<ExtArgs>
            result: $Utils.Optional<SessionNoteGroupByOutputType>[]
          }
          count: {
            args: Prisma.SessionNoteCountArgs<ExtArgs>
            result: $Utils.Optional<SessionNoteCountAggregateOutputType> | number
          }
        }
      }
      MultimediaFile: {
        payload: Prisma.$MultimediaFilePayload<ExtArgs>
        fields: Prisma.MultimediaFileFieldRefs
        operations: {
          findUnique: {
            args: Prisma.MultimediaFileFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MultimediaFilePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.MultimediaFileFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MultimediaFilePayload>
          }
          findFirst: {
            args: Prisma.MultimediaFileFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MultimediaFilePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.MultimediaFileFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MultimediaFilePayload>
          }
          findMany: {
            args: Prisma.MultimediaFileFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MultimediaFilePayload>[]
          }
          create: {
            args: Prisma.MultimediaFileCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MultimediaFilePayload>
          }
          createMany: {
            args: Prisma.MultimediaFileCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.MultimediaFileCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MultimediaFilePayload>[]
          }
          delete: {
            args: Prisma.MultimediaFileDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MultimediaFilePayload>
          }
          update: {
            args: Prisma.MultimediaFileUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MultimediaFilePayload>
          }
          deleteMany: {
            args: Prisma.MultimediaFileDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.MultimediaFileUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.MultimediaFileUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MultimediaFilePayload>[]
          }
          upsert: {
            args: Prisma.MultimediaFileUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MultimediaFilePayload>
          }
          aggregate: {
            args: Prisma.MultimediaFileAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateMultimediaFile>
          }
          groupBy: {
            args: Prisma.MultimediaFileGroupByArgs<ExtArgs>
            result: $Utils.Optional<MultimediaFileGroupByOutputType>[]
          }
          count: {
            args: Prisma.MultimediaFileCountArgs<ExtArgs>
            result: $Utils.Optional<MultimediaFileCountAggregateOutputType> | number
          }
        }
      }
      AiSuggestion: {
        payload: Prisma.$AiSuggestionPayload<ExtArgs>
        fields: Prisma.AiSuggestionFieldRefs
        operations: {
          findUnique: {
            args: Prisma.AiSuggestionFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AiSuggestionPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.AiSuggestionFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AiSuggestionPayload>
          }
          findFirst: {
            args: Prisma.AiSuggestionFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AiSuggestionPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.AiSuggestionFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AiSuggestionPayload>
          }
          findMany: {
            args: Prisma.AiSuggestionFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AiSuggestionPayload>[]
          }
          create: {
            args: Prisma.AiSuggestionCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AiSuggestionPayload>
          }
          createMany: {
            args: Prisma.AiSuggestionCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.AiSuggestionCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AiSuggestionPayload>[]
          }
          delete: {
            args: Prisma.AiSuggestionDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AiSuggestionPayload>
          }
          update: {
            args: Prisma.AiSuggestionUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AiSuggestionPayload>
          }
          deleteMany: {
            args: Prisma.AiSuggestionDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.AiSuggestionUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.AiSuggestionUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AiSuggestionPayload>[]
          }
          upsert: {
            args: Prisma.AiSuggestionUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AiSuggestionPayload>
          }
          aggregate: {
            args: Prisma.AiSuggestionAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateAiSuggestion>
          }
          groupBy: {
            args: Prisma.AiSuggestionGroupByArgs<ExtArgs>
            result: $Utils.Optional<AiSuggestionGroupByOutputType>[]
          }
          count: {
            args: Prisma.AiSuggestionCountArgs<ExtArgs>
            result: $Utils.Optional<AiSuggestionCountAggregateOutputType> | number
          }
        }
      }
      AuditLog: {
        payload: Prisma.$AuditLogPayload<ExtArgs>
        fields: Prisma.AuditLogFieldRefs
        operations: {
          findUnique: {
            args: Prisma.AuditLogFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AuditLogPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.AuditLogFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AuditLogPayload>
          }
          findFirst: {
            args: Prisma.AuditLogFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AuditLogPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.AuditLogFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AuditLogPayload>
          }
          findMany: {
            args: Prisma.AuditLogFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AuditLogPayload>[]
          }
          create: {
            args: Prisma.AuditLogCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AuditLogPayload>
          }
          createMany: {
            args: Prisma.AuditLogCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.AuditLogCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AuditLogPayload>[]
          }
          delete: {
            args: Prisma.AuditLogDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AuditLogPayload>
          }
          update: {
            args: Prisma.AuditLogUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AuditLogPayload>
          }
          deleteMany: {
            args: Prisma.AuditLogDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.AuditLogUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.AuditLogUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AuditLogPayload>[]
          }
          upsert: {
            args: Prisma.AuditLogUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AuditLogPayload>
          }
          aggregate: {
            args: Prisma.AuditLogAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateAuditLog>
          }
          groupBy: {
            args: Prisma.AuditLogGroupByArgs<ExtArgs>
            result: $Utils.Optional<AuditLogGroupByOutputType>[]
          }
          count: {
            args: Prisma.AuditLogCountArgs<ExtArgs>
            result: $Utils.Optional<AuditLogCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Defaults to stdout
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events
     * log: [
     *   { emit: 'stdout', level: 'query' },
     *   { emit: 'stdout', level: 'info' },
     *   { emit: 'stdout', level: 'warn' }
     *   { emit: 'stdout', level: 'error' }
     * ]
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
    /**
     * Global configuration for omitting model fields by default.
     * 
     * @example
     * ```
     * const prisma = new PrismaClient({
     *   omit: {
     *     user: {
     *       password: true
     *     }
     *   }
     * })
     * ```
     */
    omit?: Prisma.GlobalOmitConfig
  }
  export type GlobalOmitConfig = {
    user?: UserOmit
    client?: ClientOmit
    sessionNote?: SessionNoteOmit
    multimediaFile?: MultimediaFileOmit
    aiSuggestion?: AiSuggestionOmit
    auditLog?: AuditLogOmit
  }

  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type GetLogType<T extends LogLevel | LogDefinition> = T extends LogDefinition ? T['emit'] extends 'event' ? T['level'] : never : never
  export type GetEvents<T extends any> = T extends Array<LogLevel | LogDefinition> ?
    GetLogType<T[0]> | GetLogType<T[1]> | GetLogType<T[2]> | GetLogType<T[3]>
    : never

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'updateManyAndReturn'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */


  /**
   * Count Type UserCountOutputType
   */

  export type UserCountOutputType = {
    sessionNotes: number
    auditLogs: number
  }

  export type UserCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    sessionNotes?: boolean | UserCountOutputTypeCountSessionNotesArgs
    auditLogs?: boolean | UserCountOutputTypeCountAuditLogsArgs
  }

  // Custom InputTypes
  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserCountOutputType
     */
    select?: UserCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountSessionNotesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: SessionNoteWhereInput
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountAuditLogsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: AuditLogWhereInput
  }


  /**
   * Count Type ClientCountOutputType
   */

  export type ClientCountOutputType = {
    sessionNotes: number
  }

  export type ClientCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    sessionNotes?: boolean | ClientCountOutputTypeCountSessionNotesArgs
  }

  // Custom InputTypes
  /**
   * ClientCountOutputType without action
   */
  export type ClientCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ClientCountOutputType
     */
    select?: ClientCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * ClientCountOutputType without action
   */
  export type ClientCountOutputTypeCountSessionNotesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: SessionNoteWhereInput
  }


  /**
   * Count Type SessionNoteCountOutputType
   */

  export type SessionNoteCountOutputType = {
    multimedia: number
    aiSuggestions: number
  }

  export type SessionNoteCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    multimedia?: boolean | SessionNoteCountOutputTypeCountMultimediaArgs
    aiSuggestions?: boolean | SessionNoteCountOutputTypeCountAiSuggestionsArgs
  }

  // Custom InputTypes
  /**
   * SessionNoteCountOutputType without action
   */
  export type SessionNoteCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SessionNoteCountOutputType
     */
    select?: SessionNoteCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * SessionNoteCountOutputType without action
   */
  export type SessionNoteCountOutputTypeCountMultimediaArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: MultimediaFileWhereInput
  }

  /**
   * SessionNoteCountOutputType without action
   */
  export type SessionNoteCountOutputTypeCountAiSuggestionsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: AiSuggestionWhereInput
  }


  /**
   * Models
   */

  /**
   * Model User
   */

  export type AggregateUser = {
    _count: UserCountAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  export type UserMinAggregateOutputType = {
    id: string | null
    email: string | null
    name: string | null
    role: $Enums.UserRole | null
    passwordHash: string | null
    isActive: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserMaxAggregateOutputType = {
    id: string | null
    email: string | null
    name: string | null
    role: $Enums.UserRole | null
    passwordHash: string | null
    isActive: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserCountAggregateOutputType = {
    id: number
    email: number
    name: number
    role: number
    passwordHash: number
    isActive: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type UserMinAggregateInputType = {
    id?: true
    email?: true
    name?: true
    role?: true
    passwordHash?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserMaxAggregateInputType = {
    id?: true
    email?: true
    name?: true
    role?: true
    passwordHash?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserCountAggregateInputType = {
    id?: true
    email?: true
    name?: true
    role?: true
    passwordHash?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type UserAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which User to aggregate.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Users
    **/
    _count?: true | UserCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: UserMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: UserMaxAggregateInputType
  }

  export type GetUserAggregateType<T extends UserAggregateArgs> = {
        [P in keyof T & keyof AggregateUser]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateUser[P]>
      : GetScalarType<T[P], AggregateUser[P]>
  }




  export type UserGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserWhereInput
    orderBy?: UserOrderByWithAggregationInput | UserOrderByWithAggregationInput[]
    by: UserScalarFieldEnum[] | UserScalarFieldEnum
    having?: UserScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: UserCountAggregateInputType | true
    _min?: UserMinAggregateInputType
    _max?: UserMaxAggregateInputType
  }

  export type UserGroupByOutputType = {
    id: string
    email: string
    name: string | null
    role: $Enums.UserRole
    passwordHash: string
    isActive: boolean
    createdAt: Date
    updatedAt: Date
    _count: UserCountAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  type GetUserGroupByPayload<T extends UserGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<UserGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof UserGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], UserGroupByOutputType[P]>
            : GetScalarType<T[P], UserGroupByOutputType[P]>
        }
      >
    >


  export type UserSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    name?: boolean
    role?: boolean
    passwordHash?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    sessionNotes?: boolean | User$sessionNotesArgs<ExtArgs>
    auditLogs?: boolean | User$auditLogsArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["user"]>

  export type UserSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    name?: boolean
    role?: boolean
    passwordHash?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["user"]>

  export type UserSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    name?: boolean
    role?: boolean
    passwordHash?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["user"]>

  export type UserSelectScalar = {
    id?: boolean
    email?: boolean
    name?: boolean
    role?: boolean
    passwordHash?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type UserOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "email" | "name" | "role" | "passwordHash" | "isActive" | "createdAt" | "updatedAt", ExtArgs["result"]["user"]>
  export type UserInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    sessionNotes?: boolean | User$sessionNotesArgs<ExtArgs>
    auditLogs?: boolean | User$auditLogsArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type UserIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}
  export type UserIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $UserPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "User"
    objects: {
      sessionNotes: Prisma.$SessionNotePayload<ExtArgs>[]
      auditLogs: Prisma.$AuditLogPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      email: string
      name: string | null
      role: $Enums.UserRole
      passwordHash: string
      isActive: boolean
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["user"]>
    composites: {}
  }

  type UserGetPayload<S extends boolean | null | undefined | UserDefaultArgs> = $Result.GetResult<Prisma.$UserPayload, S>

  type UserCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<UserFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: UserCountAggregateInputType | true
    }

  export interface UserDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['User'], meta: { name: 'User' } }
    /**
     * Find zero or one User that matches the filter.
     * @param {UserFindUniqueArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends UserFindUniqueArgs>(args: SelectSubset<T, UserFindUniqueArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one User that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {UserFindUniqueOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends UserFindUniqueOrThrowArgs>(args: SelectSubset<T, UserFindUniqueOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first User that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends UserFindFirstArgs>(args?: SelectSubset<T, UserFindFirstArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first User that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends UserFindFirstOrThrowArgs>(args?: SelectSubset<T, UserFindFirstOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Users that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Users
     * const users = await prisma.user.findMany()
     * 
     * // Get first 10 Users
     * const users = await prisma.user.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const userWithIdOnly = await prisma.user.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends UserFindManyArgs>(args?: SelectSubset<T, UserFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a User.
     * @param {UserCreateArgs} args - Arguments to create a User.
     * @example
     * // Create one User
     * const User = await prisma.user.create({
     *   data: {
     *     // ... data to create a User
     *   }
     * })
     * 
     */
    create<T extends UserCreateArgs>(args: SelectSubset<T, UserCreateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Users.
     * @param {UserCreateManyArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends UserCreateManyArgs>(args?: SelectSubset<T, UserCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Users and returns the data saved in the database.
     * @param {UserCreateManyAndReturnArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Users and only return the `id`
     * const userWithIdOnly = await prisma.user.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends UserCreateManyAndReturnArgs>(args?: SelectSubset<T, UserCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a User.
     * @param {UserDeleteArgs} args - Arguments to delete one User.
     * @example
     * // Delete one User
     * const User = await prisma.user.delete({
     *   where: {
     *     // ... filter to delete one User
     *   }
     * })
     * 
     */
    delete<T extends UserDeleteArgs>(args: SelectSubset<T, UserDeleteArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one User.
     * @param {UserUpdateArgs} args - Arguments to update one User.
     * @example
     * // Update one User
     * const user = await prisma.user.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends UserUpdateArgs>(args: SelectSubset<T, UserUpdateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Users.
     * @param {UserDeleteManyArgs} args - Arguments to filter Users to delete.
     * @example
     * // Delete a few Users
     * const { count } = await prisma.user.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends UserDeleteManyArgs>(args?: SelectSubset<T, UserDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Users
     * const user = await prisma.user.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends UserUpdateManyArgs>(args: SelectSubset<T, UserUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users and returns the data updated in the database.
     * @param {UserUpdateManyAndReturnArgs} args - Arguments to update many Users.
     * @example
     * // Update many Users
     * const user = await prisma.user.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Users and only return the `id`
     * const userWithIdOnly = await prisma.user.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends UserUpdateManyAndReturnArgs>(args: SelectSubset<T, UserUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one User.
     * @param {UserUpsertArgs} args - Arguments to update or create a User.
     * @example
     * // Update or create a User
     * const user = await prisma.user.upsert({
     *   create: {
     *     // ... data to create a User
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the User we want to update
     *   }
     * })
     */
    upsert<T extends UserUpsertArgs>(args: SelectSubset<T, UserUpsertArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserCountArgs} args - Arguments to filter Users to count.
     * @example
     * // Count the number of Users
     * const count = await prisma.user.count({
     *   where: {
     *     // ... the filter for the Users we want to count
     *   }
     * })
    **/
    count<T extends UserCountArgs>(
      args?: Subset<T, UserCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], UserCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends UserAggregateArgs>(args: Subset<T, UserAggregateArgs>): Prisma.PrismaPromise<GetUserAggregateType<T>>

    /**
     * Group by User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends UserGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: UserGroupByArgs['orderBy'] }
        : { orderBy?: UserGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, UserGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the User model
   */
  readonly fields: UserFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for User.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__UserClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    sessionNotes<T extends User$sessionNotesArgs<ExtArgs> = {}>(args?: Subset<T, User$sessionNotesArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SessionNotePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    auditLogs<T extends User$auditLogsArgs<ExtArgs> = {}>(args?: Subset<T, User$auditLogsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the User model
   */
  interface UserFieldRefs {
    readonly id: FieldRef<"User", 'String'>
    readonly email: FieldRef<"User", 'String'>
    readonly name: FieldRef<"User", 'String'>
    readonly role: FieldRef<"User", 'UserRole'>
    readonly passwordHash: FieldRef<"User", 'String'>
    readonly isActive: FieldRef<"User", 'Boolean'>
    readonly createdAt: FieldRef<"User", 'DateTime'>
    readonly updatedAt: FieldRef<"User", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * User findUnique
   */
  export type UserFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findUniqueOrThrow
   */
  export type UserFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findFirst
   */
  export type UserFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findFirstOrThrow
   */
  export type UserFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findMany
   */
  export type UserFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which Users to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User create
   */
  export type UserCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to create a User.
     */
    data: XOR<UserCreateInput, UserUncheckedCreateInput>
  }

  /**
   * User createMany
   */
  export type UserCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
  }

  /**
   * User createManyAndReturn
   */
  export type UserCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
  }

  /**
   * User update
   */
  export type UserUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to update a User.
     */
    data: XOR<UserUpdateInput, UserUncheckedUpdateInput>
    /**
     * Choose, which User to update.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User updateMany
   */
  export type UserUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Users.
     */
    data: XOR<UserUpdateManyMutationInput, UserUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to update.
     */
    limit?: number
  }

  /**
   * User updateManyAndReturn
   */
  export type UserUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * The data used to update Users.
     */
    data: XOR<UserUpdateManyMutationInput, UserUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to update.
     */
    limit?: number
  }

  /**
   * User upsert
   */
  export type UserUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The filter to search for the User to update in case it exists.
     */
    where: UserWhereUniqueInput
    /**
     * In case the User found by the `where` argument doesn't exist, create a new User with this data.
     */
    create: XOR<UserCreateInput, UserUncheckedCreateInput>
    /**
     * In case the User was found with the provided `where` argument, update it with this data.
     */
    update: XOR<UserUpdateInput, UserUncheckedUpdateInput>
  }

  /**
   * User delete
   */
  export type UserDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter which User to delete.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User deleteMany
   */
  export type UserDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Users to delete
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to delete.
     */
    limit?: number
  }

  /**
   * User.sessionNotes
   */
  export type User$sessionNotesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SessionNote
     */
    select?: SessionNoteSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SessionNote
     */
    omit?: SessionNoteOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionNoteInclude<ExtArgs> | null
    where?: SessionNoteWhereInput
    orderBy?: SessionNoteOrderByWithRelationInput | SessionNoteOrderByWithRelationInput[]
    cursor?: SessionNoteWhereUniqueInput
    take?: number
    skip?: number
    distinct?: SessionNoteScalarFieldEnum | SessionNoteScalarFieldEnum[]
  }

  /**
   * User.auditLogs
   */
  export type User$auditLogsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AuditLog
     */
    select?: AuditLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AuditLog
     */
    omit?: AuditLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuditLogInclude<ExtArgs> | null
    where?: AuditLogWhereInput
    orderBy?: AuditLogOrderByWithRelationInput | AuditLogOrderByWithRelationInput[]
    cursor?: AuditLogWhereUniqueInput
    take?: number
    skip?: number
    distinct?: AuditLogScalarFieldEnum | AuditLogScalarFieldEnum[]
  }

  /**
   * User without action
   */
  export type UserDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
  }


  /**
   * Model Client
   */

  export type AggregateClient = {
    _count: ClientCountAggregateOutputType | null
    _min: ClientMinAggregateOutputType | null
    _max: ClientMaxAggregateOutputType | null
  }

  export type ClientMinAggregateOutputType = {
    id: string | null
    firstName: string | null
    lastName: string | null
    dateOfBirth: Date | null
    ehrId: string | null
    isActive: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type ClientMaxAggregateOutputType = {
    id: string | null
    firstName: string | null
    lastName: string | null
    dateOfBirth: Date | null
    ehrId: string | null
    isActive: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type ClientCountAggregateOutputType = {
    id: number
    firstName: number
    lastName: number
    dateOfBirth: number
    ehrId: number
    isActive: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type ClientMinAggregateInputType = {
    id?: true
    firstName?: true
    lastName?: true
    dateOfBirth?: true
    ehrId?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
  }

  export type ClientMaxAggregateInputType = {
    id?: true
    firstName?: true
    lastName?: true
    dateOfBirth?: true
    ehrId?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
  }

  export type ClientCountAggregateInputType = {
    id?: true
    firstName?: true
    lastName?: true
    dateOfBirth?: true
    ehrId?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type ClientAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Client to aggregate.
     */
    where?: ClientWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Clients to fetch.
     */
    orderBy?: ClientOrderByWithRelationInput | ClientOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: ClientWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Clients from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Clients.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Clients
    **/
    _count?: true | ClientCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: ClientMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: ClientMaxAggregateInputType
  }

  export type GetClientAggregateType<T extends ClientAggregateArgs> = {
        [P in keyof T & keyof AggregateClient]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateClient[P]>
      : GetScalarType<T[P], AggregateClient[P]>
  }




  export type ClientGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ClientWhereInput
    orderBy?: ClientOrderByWithAggregationInput | ClientOrderByWithAggregationInput[]
    by: ClientScalarFieldEnum[] | ClientScalarFieldEnum
    having?: ClientScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: ClientCountAggregateInputType | true
    _min?: ClientMinAggregateInputType
    _max?: ClientMaxAggregateInputType
  }

  export type ClientGroupByOutputType = {
    id: string
    firstName: string
    lastName: string
    dateOfBirth: Date | null
    ehrId: string | null
    isActive: boolean
    createdAt: Date
    updatedAt: Date
    _count: ClientCountAggregateOutputType | null
    _min: ClientMinAggregateOutputType | null
    _max: ClientMaxAggregateOutputType | null
  }

  type GetClientGroupByPayload<T extends ClientGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<ClientGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof ClientGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], ClientGroupByOutputType[P]>
            : GetScalarType<T[P], ClientGroupByOutputType[P]>
        }
      >
    >


  export type ClientSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    firstName?: boolean
    lastName?: boolean
    dateOfBirth?: boolean
    ehrId?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    sessionNotes?: boolean | Client$sessionNotesArgs<ExtArgs>
    _count?: boolean | ClientCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["client"]>

  export type ClientSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    firstName?: boolean
    lastName?: boolean
    dateOfBirth?: boolean
    ehrId?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["client"]>

  export type ClientSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    firstName?: boolean
    lastName?: boolean
    dateOfBirth?: boolean
    ehrId?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["client"]>

  export type ClientSelectScalar = {
    id?: boolean
    firstName?: boolean
    lastName?: boolean
    dateOfBirth?: boolean
    ehrId?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type ClientOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "firstName" | "lastName" | "dateOfBirth" | "ehrId" | "isActive" | "createdAt" | "updatedAt", ExtArgs["result"]["client"]>
  export type ClientInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    sessionNotes?: boolean | Client$sessionNotesArgs<ExtArgs>
    _count?: boolean | ClientCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type ClientIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}
  export type ClientIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $ClientPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Client"
    objects: {
      sessionNotes: Prisma.$SessionNotePayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      firstName: string
      lastName: string
      dateOfBirth: Date | null
      ehrId: string | null
      isActive: boolean
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["client"]>
    composites: {}
  }

  type ClientGetPayload<S extends boolean | null | undefined | ClientDefaultArgs> = $Result.GetResult<Prisma.$ClientPayload, S>

  type ClientCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<ClientFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: ClientCountAggregateInputType | true
    }

  export interface ClientDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Client'], meta: { name: 'Client' } }
    /**
     * Find zero or one Client that matches the filter.
     * @param {ClientFindUniqueArgs} args - Arguments to find a Client
     * @example
     * // Get one Client
     * const client = await prisma.client.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends ClientFindUniqueArgs>(args: SelectSubset<T, ClientFindUniqueArgs<ExtArgs>>): Prisma__ClientClient<$Result.GetResult<Prisma.$ClientPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Client that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {ClientFindUniqueOrThrowArgs} args - Arguments to find a Client
     * @example
     * // Get one Client
     * const client = await prisma.client.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends ClientFindUniqueOrThrowArgs>(args: SelectSubset<T, ClientFindUniqueOrThrowArgs<ExtArgs>>): Prisma__ClientClient<$Result.GetResult<Prisma.$ClientPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Client that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClientFindFirstArgs} args - Arguments to find a Client
     * @example
     * // Get one Client
     * const client = await prisma.client.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends ClientFindFirstArgs>(args?: SelectSubset<T, ClientFindFirstArgs<ExtArgs>>): Prisma__ClientClient<$Result.GetResult<Prisma.$ClientPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Client that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClientFindFirstOrThrowArgs} args - Arguments to find a Client
     * @example
     * // Get one Client
     * const client = await prisma.client.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends ClientFindFirstOrThrowArgs>(args?: SelectSubset<T, ClientFindFirstOrThrowArgs<ExtArgs>>): Prisma__ClientClient<$Result.GetResult<Prisma.$ClientPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Clients that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClientFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Clients
     * const clients = await prisma.client.findMany()
     * 
     * // Get first 10 Clients
     * const clients = await prisma.client.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const clientWithIdOnly = await prisma.client.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends ClientFindManyArgs>(args?: SelectSubset<T, ClientFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ClientPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Client.
     * @param {ClientCreateArgs} args - Arguments to create a Client.
     * @example
     * // Create one Client
     * const Client = await prisma.client.create({
     *   data: {
     *     // ... data to create a Client
     *   }
     * })
     * 
     */
    create<T extends ClientCreateArgs>(args: SelectSubset<T, ClientCreateArgs<ExtArgs>>): Prisma__ClientClient<$Result.GetResult<Prisma.$ClientPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Clients.
     * @param {ClientCreateManyArgs} args - Arguments to create many Clients.
     * @example
     * // Create many Clients
     * const client = await prisma.client.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends ClientCreateManyArgs>(args?: SelectSubset<T, ClientCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Clients and returns the data saved in the database.
     * @param {ClientCreateManyAndReturnArgs} args - Arguments to create many Clients.
     * @example
     * // Create many Clients
     * const client = await prisma.client.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Clients and only return the `id`
     * const clientWithIdOnly = await prisma.client.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends ClientCreateManyAndReturnArgs>(args?: SelectSubset<T, ClientCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ClientPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Client.
     * @param {ClientDeleteArgs} args - Arguments to delete one Client.
     * @example
     * // Delete one Client
     * const Client = await prisma.client.delete({
     *   where: {
     *     // ... filter to delete one Client
     *   }
     * })
     * 
     */
    delete<T extends ClientDeleteArgs>(args: SelectSubset<T, ClientDeleteArgs<ExtArgs>>): Prisma__ClientClient<$Result.GetResult<Prisma.$ClientPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Client.
     * @param {ClientUpdateArgs} args - Arguments to update one Client.
     * @example
     * // Update one Client
     * const client = await prisma.client.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends ClientUpdateArgs>(args: SelectSubset<T, ClientUpdateArgs<ExtArgs>>): Prisma__ClientClient<$Result.GetResult<Prisma.$ClientPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Clients.
     * @param {ClientDeleteManyArgs} args - Arguments to filter Clients to delete.
     * @example
     * // Delete a few Clients
     * const { count } = await prisma.client.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends ClientDeleteManyArgs>(args?: SelectSubset<T, ClientDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Clients.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClientUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Clients
     * const client = await prisma.client.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends ClientUpdateManyArgs>(args: SelectSubset<T, ClientUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Clients and returns the data updated in the database.
     * @param {ClientUpdateManyAndReturnArgs} args - Arguments to update many Clients.
     * @example
     * // Update many Clients
     * const client = await prisma.client.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Clients and only return the `id`
     * const clientWithIdOnly = await prisma.client.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends ClientUpdateManyAndReturnArgs>(args: SelectSubset<T, ClientUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ClientPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Client.
     * @param {ClientUpsertArgs} args - Arguments to update or create a Client.
     * @example
     * // Update or create a Client
     * const client = await prisma.client.upsert({
     *   create: {
     *     // ... data to create a Client
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Client we want to update
     *   }
     * })
     */
    upsert<T extends ClientUpsertArgs>(args: SelectSubset<T, ClientUpsertArgs<ExtArgs>>): Prisma__ClientClient<$Result.GetResult<Prisma.$ClientPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Clients.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClientCountArgs} args - Arguments to filter Clients to count.
     * @example
     * // Count the number of Clients
     * const count = await prisma.client.count({
     *   where: {
     *     // ... the filter for the Clients we want to count
     *   }
     * })
    **/
    count<T extends ClientCountArgs>(
      args?: Subset<T, ClientCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], ClientCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Client.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClientAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends ClientAggregateArgs>(args: Subset<T, ClientAggregateArgs>): Prisma.PrismaPromise<GetClientAggregateType<T>>

    /**
     * Group by Client.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ClientGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends ClientGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: ClientGroupByArgs['orderBy'] }
        : { orderBy?: ClientGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, ClientGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetClientGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Client model
   */
  readonly fields: ClientFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Client.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__ClientClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    sessionNotes<T extends Client$sessionNotesArgs<ExtArgs> = {}>(args?: Subset<T, Client$sessionNotesArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SessionNotePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Client model
   */
  interface ClientFieldRefs {
    readonly id: FieldRef<"Client", 'String'>
    readonly firstName: FieldRef<"Client", 'String'>
    readonly lastName: FieldRef<"Client", 'String'>
    readonly dateOfBirth: FieldRef<"Client", 'DateTime'>
    readonly ehrId: FieldRef<"Client", 'String'>
    readonly isActive: FieldRef<"Client", 'Boolean'>
    readonly createdAt: FieldRef<"Client", 'DateTime'>
    readonly updatedAt: FieldRef<"Client", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * Client findUnique
   */
  export type ClientFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Client
     */
    select?: ClientSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Client
     */
    omit?: ClientOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClientInclude<ExtArgs> | null
    /**
     * Filter, which Client to fetch.
     */
    where: ClientWhereUniqueInput
  }

  /**
   * Client findUniqueOrThrow
   */
  export type ClientFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Client
     */
    select?: ClientSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Client
     */
    omit?: ClientOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClientInclude<ExtArgs> | null
    /**
     * Filter, which Client to fetch.
     */
    where: ClientWhereUniqueInput
  }

  /**
   * Client findFirst
   */
  export type ClientFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Client
     */
    select?: ClientSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Client
     */
    omit?: ClientOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClientInclude<ExtArgs> | null
    /**
     * Filter, which Client to fetch.
     */
    where?: ClientWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Clients to fetch.
     */
    orderBy?: ClientOrderByWithRelationInput | ClientOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Clients.
     */
    cursor?: ClientWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Clients from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Clients.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Clients.
     */
    distinct?: ClientScalarFieldEnum | ClientScalarFieldEnum[]
  }

  /**
   * Client findFirstOrThrow
   */
  export type ClientFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Client
     */
    select?: ClientSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Client
     */
    omit?: ClientOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClientInclude<ExtArgs> | null
    /**
     * Filter, which Client to fetch.
     */
    where?: ClientWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Clients to fetch.
     */
    orderBy?: ClientOrderByWithRelationInput | ClientOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Clients.
     */
    cursor?: ClientWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Clients from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Clients.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Clients.
     */
    distinct?: ClientScalarFieldEnum | ClientScalarFieldEnum[]
  }

  /**
   * Client findMany
   */
  export type ClientFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Client
     */
    select?: ClientSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Client
     */
    omit?: ClientOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClientInclude<ExtArgs> | null
    /**
     * Filter, which Clients to fetch.
     */
    where?: ClientWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Clients to fetch.
     */
    orderBy?: ClientOrderByWithRelationInput | ClientOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Clients.
     */
    cursor?: ClientWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Clients from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Clients.
     */
    skip?: number
    distinct?: ClientScalarFieldEnum | ClientScalarFieldEnum[]
  }

  /**
   * Client create
   */
  export type ClientCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Client
     */
    select?: ClientSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Client
     */
    omit?: ClientOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClientInclude<ExtArgs> | null
    /**
     * The data needed to create a Client.
     */
    data: XOR<ClientCreateInput, ClientUncheckedCreateInput>
  }

  /**
   * Client createMany
   */
  export type ClientCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Clients.
     */
    data: ClientCreateManyInput | ClientCreateManyInput[]
  }

  /**
   * Client createManyAndReturn
   */
  export type ClientCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Client
     */
    select?: ClientSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Client
     */
    omit?: ClientOmit<ExtArgs> | null
    /**
     * The data used to create many Clients.
     */
    data: ClientCreateManyInput | ClientCreateManyInput[]
  }

  /**
   * Client update
   */
  export type ClientUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Client
     */
    select?: ClientSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Client
     */
    omit?: ClientOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClientInclude<ExtArgs> | null
    /**
     * The data needed to update a Client.
     */
    data: XOR<ClientUpdateInput, ClientUncheckedUpdateInput>
    /**
     * Choose, which Client to update.
     */
    where: ClientWhereUniqueInput
  }

  /**
   * Client updateMany
   */
  export type ClientUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Clients.
     */
    data: XOR<ClientUpdateManyMutationInput, ClientUncheckedUpdateManyInput>
    /**
     * Filter which Clients to update
     */
    where?: ClientWhereInput
    /**
     * Limit how many Clients to update.
     */
    limit?: number
  }

  /**
   * Client updateManyAndReturn
   */
  export type ClientUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Client
     */
    select?: ClientSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Client
     */
    omit?: ClientOmit<ExtArgs> | null
    /**
     * The data used to update Clients.
     */
    data: XOR<ClientUpdateManyMutationInput, ClientUncheckedUpdateManyInput>
    /**
     * Filter which Clients to update
     */
    where?: ClientWhereInput
    /**
     * Limit how many Clients to update.
     */
    limit?: number
  }

  /**
   * Client upsert
   */
  export type ClientUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Client
     */
    select?: ClientSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Client
     */
    omit?: ClientOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClientInclude<ExtArgs> | null
    /**
     * The filter to search for the Client to update in case it exists.
     */
    where: ClientWhereUniqueInput
    /**
     * In case the Client found by the `where` argument doesn't exist, create a new Client with this data.
     */
    create: XOR<ClientCreateInput, ClientUncheckedCreateInput>
    /**
     * In case the Client was found with the provided `where` argument, update it with this data.
     */
    update: XOR<ClientUpdateInput, ClientUncheckedUpdateInput>
  }

  /**
   * Client delete
   */
  export type ClientDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Client
     */
    select?: ClientSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Client
     */
    omit?: ClientOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClientInclude<ExtArgs> | null
    /**
     * Filter which Client to delete.
     */
    where: ClientWhereUniqueInput
  }

  /**
   * Client deleteMany
   */
  export type ClientDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Clients to delete
     */
    where?: ClientWhereInput
    /**
     * Limit how many Clients to delete.
     */
    limit?: number
  }

  /**
   * Client.sessionNotes
   */
  export type Client$sessionNotesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SessionNote
     */
    select?: SessionNoteSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SessionNote
     */
    omit?: SessionNoteOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionNoteInclude<ExtArgs> | null
    where?: SessionNoteWhereInput
    orderBy?: SessionNoteOrderByWithRelationInput | SessionNoteOrderByWithRelationInput[]
    cursor?: SessionNoteWhereUniqueInput
    take?: number
    skip?: number
    distinct?: SessionNoteScalarFieldEnum | SessionNoteScalarFieldEnum[]
  }

  /**
   * Client without action
   */
  export type ClientDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Client
     */
    select?: ClientSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Client
     */
    omit?: ClientOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ClientInclude<ExtArgs> | null
  }


  /**
   * Model SessionNote
   */

  export type AggregateSessionNote = {
    _count: SessionNoteCountAggregateOutputType | null
    _avg: SessionNoteAvgAggregateOutputType | null
    _sum: SessionNoteSumAggregateOutputType | null
    _min: SessionNoteMinAggregateOutputType | null
    _max: SessionNoteMaxAggregateOutputType | null
  }

  export type SessionNoteAvgAggregateOutputType = {
    sessionDuration: number | null
  }

  export type SessionNoteSumAggregateOutputType = {
    sessionDuration: number | null
  }

  export type SessionNoteMinAggregateOutputType = {
    id: string | null
    clientId: string | null
    therapistId: string | null
    sessionDate: Date | null
    noteType: $Enums.NoteType | null
    subjective: string | null
    objective: string | null
    assessment: string | null
    plan: string | null
    sessionDuration: number | null
    interventions: string | null
    clientMood: string | null
    isFinalized: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type SessionNoteMaxAggregateOutputType = {
    id: string | null
    clientId: string | null
    therapistId: string | null
    sessionDate: Date | null
    noteType: $Enums.NoteType | null
    subjective: string | null
    objective: string | null
    assessment: string | null
    plan: string | null
    sessionDuration: number | null
    interventions: string | null
    clientMood: string | null
    isFinalized: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type SessionNoteCountAggregateOutputType = {
    id: number
    clientId: number
    therapistId: number
    sessionDate: number
    noteType: number
    subjective: number
    objective: number
    assessment: number
    plan: number
    sessionDuration: number
    interventions: number
    clientMood: number
    isFinalized: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type SessionNoteAvgAggregateInputType = {
    sessionDuration?: true
  }

  export type SessionNoteSumAggregateInputType = {
    sessionDuration?: true
  }

  export type SessionNoteMinAggregateInputType = {
    id?: true
    clientId?: true
    therapistId?: true
    sessionDate?: true
    noteType?: true
    subjective?: true
    objective?: true
    assessment?: true
    plan?: true
    sessionDuration?: true
    interventions?: true
    clientMood?: true
    isFinalized?: true
    createdAt?: true
    updatedAt?: true
  }

  export type SessionNoteMaxAggregateInputType = {
    id?: true
    clientId?: true
    therapistId?: true
    sessionDate?: true
    noteType?: true
    subjective?: true
    objective?: true
    assessment?: true
    plan?: true
    sessionDuration?: true
    interventions?: true
    clientMood?: true
    isFinalized?: true
    createdAt?: true
    updatedAt?: true
  }

  export type SessionNoteCountAggregateInputType = {
    id?: true
    clientId?: true
    therapistId?: true
    sessionDate?: true
    noteType?: true
    subjective?: true
    objective?: true
    assessment?: true
    plan?: true
    sessionDuration?: true
    interventions?: true
    clientMood?: true
    isFinalized?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type SessionNoteAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which SessionNote to aggregate.
     */
    where?: SessionNoteWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of SessionNotes to fetch.
     */
    orderBy?: SessionNoteOrderByWithRelationInput | SessionNoteOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: SessionNoteWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` SessionNotes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` SessionNotes.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned SessionNotes
    **/
    _count?: true | SessionNoteCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: SessionNoteAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: SessionNoteSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: SessionNoteMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: SessionNoteMaxAggregateInputType
  }

  export type GetSessionNoteAggregateType<T extends SessionNoteAggregateArgs> = {
        [P in keyof T & keyof AggregateSessionNote]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateSessionNote[P]>
      : GetScalarType<T[P], AggregateSessionNote[P]>
  }




  export type SessionNoteGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: SessionNoteWhereInput
    orderBy?: SessionNoteOrderByWithAggregationInput | SessionNoteOrderByWithAggregationInput[]
    by: SessionNoteScalarFieldEnum[] | SessionNoteScalarFieldEnum
    having?: SessionNoteScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: SessionNoteCountAggregateInputType | true
    _avg?: SessionNoteAvgAggregateInputType
    _sum?: SessionNoteSumAggregateInputType
    _min?: SessionNoteMinAggregateInputType
    _max?: SessionNoteMaxAggregateInputType
  }

  export type SessionNoteGroupByOutputType = {
    id: string
    clientId: string
    therapistId: string
    sessionDate: Date
    noteType: $Enums.NoteType
    subjective: string | null
    objective: string | null
    assessment: string | null
    plan: string | null
    sessionDuration: number | null
    interventions: string | null
    clientMood: string | null
    isFinalized: boolean
    createdAt: Date
    updatedAt: Date
    _count: SessionNoteCountAggregateOutputType | null
    _avg: SessionNoteAvgAggregateOutputType | null
    _sum: SessionNoteSumAggregateOutputType | null
    _min: SessionNoteMinAggregateOutputType | null
    _max: SessionNoteMaxAggregateOutputType | null
  }

  type GetSessionNoteGroupByPayload<T extends SessionNoteGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<SessionNoteGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof SessionNoteGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], SessionNoteGroupByOutputType[P]>
            : GetScalarType<T[P], SessionNoteGroupByOutputType[P]>
        }
      >
    >


  export type SessionNoteSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    clientId?: boolean
    therapistId?: boolean
    sessionDate?: boolean
    noteType?: boolean
    subjective?: boolean
    objective?: boolean
    assessment?: boolean
    plan?: boolean
    sessionDuration?: boolean
    interventions?: boolean
    clientMood?: boolean
    isFinalized?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    client?: boolean | ClientDefaultArgs<ExtArgs>
    therapist?: boolean | UserDefaultArgs<ExtArgs>
    multimedia?: boolean | SessionNote$multimediaArgs<ExtArgs>
    aiSuggestions?: boolean | SessionNote$aiSuggestionsArgs<ExtArgs>
    _count?: boolean | SessionNoteCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["sessionNote"]>

  export type SessionNoteSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    clientId?: boolean
    therapistId?: boolean
    sessionDate?: boolean
    noteType?: boolean
    subjective?: boolean
    objective?: boolean
    assessment?: boolean
    plan?: boolean
    sessionDuration?: boolean
    interventions?: boolean
    clientMood?: boolean
    isFinalized?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    client?: boolean | ClientDefaultArgs<ExtArgs>
    therapist?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["sessionNote"]>

  export type SessionNoteSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    clientId?: boolean
    therapistId?: boolean
    sessionDate?: boolean
    noteType?: boolean
    subjective?: boolean
    objective?: boolean
    assessment?: boolean
    plan?: boolean
    sessionDuration?: boolean
    interventions?: boolean
    clientMood?: boolean
    isFinalized?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    client?: boolean | ClientDefaultArgs<ExtArgs>
    therapist?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["sessionNote"]>

  export type SessionNoteSelectScalar = {
    id?: boolean
    clientId?: boolean
    therapistId?: boolean
    sessionDate?: boolean
    noteType?: boolean
    subjective?: boolean
    objective?: boolean
    assessment?: boolean
    plan?: boolean
    sessionDuration?: boolean
    interventions?: boolean
    clientMood?: boolean
    isFinalized?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type SessionNoteOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "clientId" | "therapistId" | "sessionDate" | "noteType" | "subjective" | "objective" | "assessment" | "plan" | "sessionDuration" | "interventions" | "clientMood" | "isFinalized" | "createdAt" | "updatedAt", ExtArgs["result"]["sessionNote"]>
  export type SessionNoteInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    client?: boolean | ClientDefaultArgs<ExtArgs>
    therapist?: boolean | UserDefaultArgs<ExtArgs>
    multimedia?: boolean | SessionNote$multimediaArgs<ExtArgs>
    aiSuggestions?: boolean | SessionNote$aiSuggestionsArgs<ExtArgs>
    _count?: boolean | SessionNoteCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type SessionNoteIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    client?: boolean | ClientDefaultArgs<ExtArgs>
    therapist?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type SessionNoteIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    client?: boolean | ClientDefaultArgs<ExtArgs>
    therapist?: boolean | UserDefaultArgs<ExtArgs>
  }

  export type $SessionNotePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "SessionNote"
    objects: {
      client: Prisma.$ClientPayload<ExtArgs>
      therapist: Prisma.$UserPayload<ExtArgs>
      multimedia: Prisma.$MultimediaFilePayload<ExtArgs>[]
      aiSuggestions: Prisma.$AiSuggestionPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      clientId: string
      therapistId: string
      sessionDate: Date
      noteType: $Enums.NoteType
      subjective: string | null
      objective: string | null
      assessment: string | null
      plan: string | null
      sessionDuration: number | null
      interventions: string | null
      clientMood: string | null
      isFinalized: boolean
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["sessionNote"]>
    composites: {}
  }

  type SessionNoteGetPayload<S extends boolean | null | undefined | SessionNoteDefaultArgs> = $Result.GetResult<Prisma.$SessionNotePayload, S>

  type SessionNoteCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<SessionNoteFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: SessionNoteCountAggregateInputType | true
    }

  export interface SessionNoteDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['SessionNote'], meta: { name: 'SessionNote' } }
    /**
     * Find zero or one SessionNote that matches the filter.
     * @param {SessionNoteFindUniqueArgs} args - Arguments to find a SessionNote
     * @example
     * // Get one SessionNote
     * const sessionNote = await prisma.sessionNote.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends SessionNoteFindUniqueArgs>(args: SelectSubset<T, SessionNoteFindUniqueArgs<ExtArgs>>): Prisma__SessionNoteClient<$Result.GetResult<Prisma.$SessionNotePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one SessionNote that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {SessionNoteFindUniqueOrThrowArgs} args - Arguments to find a SessionNote
     * @example
     * // Get one SessionNote
     * const sessionNote = await prisma.sessionNote.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends SessionNoteFindUniqueOrThrowArgs>(args: SelectSubset<T, SessionNoteFindUniqueOrThrowArgs<ExtArgs>>): Prisma__SessionNoteClient<$Result.GetResult<Prisma.$SessionNotePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first SessionNote that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SessionNoteFindFirstArgs} args - Arguments to find a SessionNote
     * @example
     * // Get one SessionNote
     * const sessionNote = await prisma.sessionNote.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends SessionNoteFindFirstArgs>(args?: SelectSubset<T, SessionNoteFindFirstArgs<ExtArgs>>): Prisma__SessionNoteClient<$Result.GetResult<Prisma.$SessionNotePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first SessionNote that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SessionNoteFindFirstOrThrowArgs} args - Arguments to find a SessionNote
     * @example
     * // Get one SessionNote
     * const sessionNote = await prisma.sessionNote.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends SessionNoteFindFirstOrThrowArgs>(args?: SelectSubset<T, SessionNoteFindFirstOrThrowArgs<ExtArgs>>): Prisma__SessionNoteClient<$Result.GetResult<Prisma.$SessionNotePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more SessionNotes that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SessionNoteFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all SessionNotes
     * const sessionNotes = await prisma.sessionNote.findMany()
     * 
     * // Get first 10 SessionNotes
     * const sessionNotes = await prisma.sessionNote.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const sessionNoteWithIdOnly = await prisma.sessionNote.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends SessionNoteFindManyArgs>(args?: SelectSubset<T, SessionNoteFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SessionNotePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a SessionNote.
     * @param {SessionNoteCreateArgs} args - Arguments to create a SessionNote.
     * @example
     * // Create one SessionNote
     * const SessionNote = await prisma.sessionNote.create({
     *   data: {
     *     // ... data to create a SessionNote
     *   }
     * })
     * 
     */
    create<T extends SessionNoteCreateArgs>(args: SelectSubset<T, SessionNoteCreateArgs<ExtArgs>>): Prisma__SessionNoteClient<$Result.GetResult<Prisma.$SessionNotePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many SessionNotes.
     * @param {SessionNoteCreateManyArgs} args - Arguments to create many SessionNotes.
     * @example
     * // Create many SessionNotes
     * const sessionNote = await prisma.sessionNote.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends SessionNoteCreateManyArgs>(args?: SelectSubset<T, SessionNoteCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many SessionNotes and returns the data saved in the database.
     * @param {SessionNoteCreateManyAndReturnArgs} args - Arguments to create many SessionNotes.
     * @example
     * // Create many SessionNotes
     * const sessionNote = await prisma.sessionNote.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many SessionNotes and only return the `id`
     * const sessionNoteWithIdOnly = await prisma.sessionNote.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends SessionNoteCreateManyAndReturnArgs>(args?: SelectSubset<T, SessionNoteCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SessionNotePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a SessionNote.
     * @param {SessionNoteDeleteArgs} args - Arguments to delete one SessionNote.
     * @example
     * // Delete one SessionNote
     * const SessionNote = await prisma.sessionNote.delete({
     *   where: {
     *     // ... filter to delete one SessionNote
     *   }
     * })
     * 
     */
    delete<T extends SessionNoteDeleteArgs>(args: SelectSubset<T, SessionNoteDeleteArgs<ExtArgs>>): Prisma__SessionNoteClient<$Result.GetResult<Prisma.$SessionNotePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one SessionNote.
     * @param {SessionNoteUpdateArgs} args - Arguments to update one SessionNote.
     * @example
     * // Update one SessionNote
     * const sessionNote = await prisma.sessionNote.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends SessionNoteUpdateArgs>(args: SelectSubset<T, SessionNoteUpdateArgs<ExtArgs>>): Prisma__SessionNoteClient<$Result.GetResult<Prisma.$SessionNotePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more SessionNotes.
     * @param {SessionNoteDeleteManyArgs} args - Arguments to filter SessionNotes to delete.
     * @example
     * // Delete a few SessionNotes
     * const { count } = await prisma.sessionNote.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends SessionNoteDeleteManyArgs>(args?: SelectSubset<T, SessionNoteDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more SessionNotes.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SessionNoteUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many SessionNotes
     * const sessionNote = await prisma.sessionNote.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends SessionNoteUpdateManyArgs>(args: SelectSubset<T, SessionNoteUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more SessionNotes and returns the data updated in the database.
     * @param {SessionNoteUpdateManyAndReturnArgs} args - Arguments to update many SessionNotes.
     * @example
     * // Update many SessionNotes
     * const sessionNote = await prisma.sessionNote.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more SessionNotes and only return the `id`
     * const sessionNoteWithIdOnly = await prisma.sessionNote.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends SessionNoteUpdateManyAndReturnArgs>(args: SelectSubset<T, SessionNoteUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SessionNotePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one SessionNote.
     * @param {SessionNoteUpsertArgs} args - Arguments to update or create a SessionNote.
     * @example
     * // Update or create a SessionNote
     * const sessionNote = await prisma.sessionNote.upsert({
     *   create: {
     *     // ... data to create a SessionNote
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the SessionNote we want to update
     *   }
     * })
     */
    upsert<T extends SessionNoteUpsertArgs>(args: SelectSubset<T, SessionNoteUpsertArgs<ExtArgs>>): Prisma__SessionNoteClient<$Result.GetResult<Prisma.$SessionNotePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of SessionNotes.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SessionNoteCountArgs} args - Arguments to filter SessionNotes to count.
     * @example
     * // Count the number of SessionNotes
     * const count = await prisma.sessionNote.count({
     *   where: {
     *     // ... the filter for the SessionNotes we want to count
     *   }
     * })
    **/
    count<T extends SessionNoteCountArgs>(
      args?: Subset<T, SessionNoteCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], SessionNoteCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a SessionNote.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SessionNoteAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends SessionNoteAggregateArgs>(args: Subset<T, SessionNoteAggregateArgs>): Prisma.PrismaPromise<GetSessionNoteAggregateType<T>>

    /**
     * Group by SessionNote.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SessionNoteGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends SessionNoteGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: SessionNoteGroupByArgs['orderBy'] }
        : { orderBy?: SessionNoteGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, SessionNoteGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetSessionNoteGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the SessionNote model
   */
  readonly fields: SessionNoteFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for SessionNote.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__SessionNoteClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    client<T extends ClientDefaultArgs<ExtArgs> = {}>(args?: Subset<T, ClientDefaultArgs<ExtArgs>>): Prisma__ClientClient<$Result.GetResult<Prisma.$ClientPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    therapist<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    multimedia<T extends SessionNote$multimediaArgs<ExtArgs> = {}>(args?: Subset<T, SessionNote$multimediaArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$MultimediaFilePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    aiSuggestions<T extends SessionNote$aiSuggestionsArgs<ExtArgs> = {}>(args?: Subset<T, SessionNote$aiSuggestionsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AiSuggestionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the SessionNote model
   */
  interface SessionNoteFieldRefs {
    readonly id: FieldRef<"SessionNote", 'String'>
    readonly clientId: FieldRef<"SessionNote", 'String'>
    readonly therapistId: FieldRef<"SessionNote", 'String'>
    readonly sessionDate: FieldRef<"SessionNote", 'DateTime'>
    readonly noteType: FieldRef<"SessionNote", 'NoteType'>
    readonly subjective: FieldRef<"SessionNote", 'String'>
    readonly objective: FieldRef<"SessionNote", 'String'>
    readonly assessment: FieldRef<"SessionNote", 'String'>
    readonly plan: FieldRef<"SessionNote", 'String'>
    readonly sessionDuration: FieldRef<"SessionNote", 'Int'>
    readonly interventions: FieldRef<"SessionNote", 'String'>
    readonly clientMood: FieldRef<"SessionNote", 'String'>
    readonly isFinalized: FieldRef<"SessionNote", 'Boolean'>
    readonly createdAt: FieldRef<"SessionNote", 'DateTime'>
    readonly updatedAt: FieldRef<"SessionNote", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * SessionNote findUnique
   */
  export type SessionNoteFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SessionNote
     */
    select?: SessionNoteSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SessionNote
     */
    omit?: SessionNoteOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionNoteInclude<ExtArgs> | null
    /**
     * Filter, which SessionNote to fetch.
     */
    where: SessionNoteWhereUniqueInput
  }

  /**
   * SessionNote findUniqueOrThrow
   */
  export type SessionNoteFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SessionNote
     */
    select?: SessionNoteSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SessionNote
     */
    omit?: SessionNoteOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionNoteInclude<ExtArgs> | null
    /**
     * Filter, which SessionNote to fetch.
     */
    where: SessionNoteWhereUniqueInput
  }

  /**
   * SessionNote findFirst
   */
  export type SessionNoteFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SessionNote
     */
    select?: SessionNoteSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SessionNote
     */
    omit?: SessionNoteOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionNoteInclude<ExtArgs> | null
    /**
     * Filter, which SessionNote to fetch.
     */
    where?: SessionNoteWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of SessionNotes to fetch.
     */
    orderBy?: SessionNoteOrderByWithRelationInput | SessionNoteOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for SessionNotes.
     */
    cursor?: SessionNoteWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` SessionNotes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` SessionNotes.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of SessionNotes.
     */
    distinct?: SessionNoteScalarFieldEnum | SessionNoteScalarFieldEnum[]
  }

  /**
   * SessionNote findFirstOrThrow
   */
  export type SessionNoteFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SessionNote
     */
    select?: SessionNoteSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SessionNote
     */
    omit?: SessionNoteOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionNoteInclude<ExtArgs> | null
    /**
     * Filter, which SessionNote to fetch.
     */
    where?: SessionNoteWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of SessionNotes to fetch.
     */
    orderBy?: SessionNoteOrderByWithRelationInput | SessionNoteOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for SessionNotes.
     */
    cursor?: SessionNoteWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` SessionNotes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` SessionNotes.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of SessionNotes.
     */
    distinct?: SessionNoteScalarFieldEnum | SessionNoteScalarFieldEnum[]
  }

  /**
   * SessionNote findMany
   */
  export type SessionNoteFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SessionNote
     */
    select?: SessionNoteSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SessionNote
     */
    omit?: SessionNoteOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionNoteInclude<ExtArgs> | null
    /**
     * Filter, which SessionNotes to fetch.
     */
    where?: SessionNoteWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of SessionNotes to fetch.
     */
    orderBy?: SessionNoteOrderByWithRelationInput | SessionNoteOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing SessionNotes.
     */
    cursor?: SessionNoteWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` SessionNotes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` SessionNotes.
     */
    skip?: number
    distinct?: SessionNoteScalarFieldEnum | SessionNoteScalarFieldEnum[]
  }

  /**
   * SessionNote create
   */
  export type SessionNoteCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SessionNote
     */
    select?: SessionNoteSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SessionNote
     */
    omit?: SessionNoteOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionNoteInclude<ExtArgs> | null
    /**
     * The data needed to create a SessionNote.
     */
    data: XOR<SessionNoteCreateInput, SessionNoteUncheckedCreateInput>
  }

  /**
   * SessionNote createMany
   */
  export type SessionNoteCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many SessionNotes.
     */
    data: SessionNoteCreateManyInput | SessionNoteCreateManyInput[]
  }

  /**
   * SessionNote createManyAndReturn
   */
  export type SessionNoteCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SessionNote
     */
    select?: SessionNoteSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the SessionNote
     */
    omit?: SessionNoteOmit<ExtArgs> | null
    /**
     * The data used to create many SessionNotes.
     */
    data: SessionNoteCreateManyInput | SessionNoteCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionNoteIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * SessionNote update
   */
  export type SessionNoteUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SessionNote
     */
    select?: SessionNoteSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SessionNote
     */
    omit?: SessionNoteOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionNoteInclude<ExtArgs> | null
    /**
     * The data needed to update a SessionNote.
     */
    data: XOR<SessionNoteUpdateInput, SessionNoteUncheckedUpdateInput>
    /**
     * Choose, which SessionNote to update.
     */
    where: SessionNoteWhereUniqueInput
  }

  /**
   * SessionNote updateMany
   */
  export type SessionNoteUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update SessionNotes.
     */
    data: XOR<SessionNoteUpdateManyMutationInput, SessionNoteUncheckedUpdateManyInput>
    /**
     * Filter which SessionNotes to update
     */
    where?: SessionNoteWhereInput
    /**
     * Limit how many SessionNotes to update.
     */
    limit?: number
  }

  /**
   * SessionNote updateManyAndReturn
   */
  export type SessionNoteUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SessionNote
     */
    select?: SessionNoteSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the SessionNote
     */
    omit?: SessionNoteOmit<ExtArgs> | null
    /**
     * The data used to update SessionNotes.
     */
    data: XOR<SessionNoteUpdateManyMutationInput, SessionNoteUncheckedUpdateManyInput>
    /**
     * Filter which SessionNotes to update
     */
    where?: SessionNoteWhereInput
    /**
     * Limit how many SessionNotes to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionNoteIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * SessionNote upsert
   */
  export type SessionNoteUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SessionNote
     */
    select?: SessionNoteSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SessionNote
     */
    omit?: SessionNoteOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionNoteInclude<ExtArgs> | null
    /**
     * The filter to search for the SessionNote to update in case it exists.
     */
    where: SessionNoteWhereUniqueInput
    /**
     * In case the SessionNote found by the `where` argument doesn't exist, create a new SessionNote with this data.
     */
    create: XOR<SessionNoteCreateInput, SessionNoteUncheckedCreateInput>
    /**
     * In case the SessionNote was found with the provided `where` argument, update it with this data.
     */
    update: XOR<SessionNoteUpdateInput, SessionNoteUncheckedUpdateInput>
  }

  /**
   * SessionNote delete
   */
  export type SessionNoteDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SessionNote
     */
    select?: SessionNoteSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SessionNote
     */
    omit?: SessionNoteOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionNoteInclude<ExtArgs> | null
    /**
     * Filter which SessionNote to delete.
     */
    where: SessionNoteWhereUniqueInput
  }

  /**
   * SessionNote deleteMany
   */
  export type SessionNoteDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which SessionNotes to delete
     */
    where?: SessionNoteWhereInput
    /**
     * Limit how many SessionNotes to delete.
     */
    limit?: number
  }

  /**
   * SessionNote.multimedia
   */
  export type SessionNote$multimediaArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MultimediaFile
     */
    select?: MultimediaFileSelect<ExtArgs> | null
    /**
     * Omit specific fields from the MultimediaFile
     */
    omit?: MultimediaFileOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MultimediaFileInclude<ExtArgs> | null
    where?: MultimediaFileWhereInput
    orderBy?: MultimediaFileOrderByWithRelationInput | MultimediaFileOrderByWithRelationInput[]
    cursor?: MultimediaFileWhereUniqueInput
    take?: number
    skip?: number
    distinct?: MultimediaFileScalarFieldEnum | MultimediaFileScalarFieldEnum[]
  }

  /**
   * SessionNote.aiSuggestions
   */
  export type SessionNote$aiSuggestionsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AiSuggestion
     */
    select?: AiSuggestionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AiSuggestion
     */
    omit?: AiSuggestionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AiSuggestionInclude<ExtArgs> | null
    where?: AiSuggestionWhereInput
    orderBy?: AiSuggestionOrderByWithRelationInput | AiSuggestionOrderByWithRelationInput[]
    cursor?: AiSuggestionWhereUniqueInput
    take?: number
    skip?: number
    distinct?: AiSuggestionScalarFieldEnum | AiSuggestionScalarFieldEnum[]
  }

  /**
   * SessionNote without action
   */
  export type SessionNoteDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SessionNote
     */
    select?: SessionNoteSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SessionNote
     */
    omit?: SessionNoteOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SessionNoteInclude<ExtArgs> | null
  }


  /**
   * Model MultimediaFile
   */

  export type AggregateMultimediaFile = {
    _count: MultimediaFileCountAggregateOutputType | null
    _avg: MultimediaFileAvgAggregateOutputType | null
    _sum: MultimediaFileSumAggregateOutputType | null
    _min: MultimediaFileMinAggregateOutputType | null
    _max: MultimediaFileMaxAggregateOutputType | null
  }

  export type MultimediaFileAvgAggregateOutputType = {
    fileSize: number | null
  }

  export type MultimediaFileSumAggregateOutputType = {
    fileSize: number | null
  }

  export type MultimediaFileMinAggregateOutputType = {
    id: string | null
    sessionNoteId: string | null
    fileName: string | null
    originalName: string | null
    fileType: $Enums.MediaType | null
    fileSize: number | null
    s3Key: string | null
    s3Bucket: string | null
    description: string | null
    aiDescription: string | null
    uploadedAt: Date | null
  }

  export type MultimediaFileMaxAggregateOutputType = {
    id: string | null
    sessionNoteId: string | null
    fileName: string | null
    originalName: string | null
    fileType: $Enums.MediaType | null
    fileSize: number | null
    s3Key: string | null
    s3Bucket: string | null
    description: string | null
    aiDescription: string | null
    uploadedAt: Date | null
  }

  export type MultimediaFileCountAggregateOutputType = {
    id: number
    sessionNoteId: number
    fileName: number
    originalName: number
    fileType: number
    fileSize: number
    s3Key: number
    s3Bucket: number
    description: number
    aiDescription: number
    uploadedAt: number
    _all: number
  }


  export type MultimediaFileAvgAggregateInputType = {
    fileSize?: true
  }

  export type MultimediaFileSumAggregateInputType = {
    fileSize?: true
  }

  export type MultimediaFileMinAggregateInputType = {
    id?: true
    sessionNoteId?: true
    fileName?: true
    originalName?: true
    fileType?: true
    fileSize?: true
    s3Key?: true
    s3Bucket?: true
    description?: true
    aiDescription?: true
    uploadedAt?: true
  }

  export type MultimediaFileMaxAggregateInputType = {
    id?: true
    sessionNoteId?: true
    fileName?: true
    originalName?: true
    fileType?: true
    fileSize?: true
    s3Key?: true
    s3Bucket?: true
    description?: true
    aiDescription?: true
    uploadedAt?: true
  }

  export type MultimediaFileCountAggregateInputType = {
    id?: true
    sessionNoteId?: true
    fileName?: true
    originalName?: true
    fileType?: true
    fileSize?: true
    s3Key?: true
    s3Bucket?: true
    description?: true
    aiDescription?: true
    uploadedAt?: true
    _all?: true
  }

  export type MultimediaFileAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which MultimediaFile to aggregate.
     */
    where?: MultimediaFileWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of MultimediaFiles to fetch.
     */
    orderBy?: MultimediaFileOrderByWithRelationInput | MultimediaFileOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: MultimediaFileWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` MultimediaFiles from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` MultimediaFiles.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned MultimediaFiles
    **/
    _count?: true | MultimediaFileCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: MultimediaFileAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: MultimediaFileSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: MultimediaFileMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: MultimediaFileMaxAggregateInputType
  }

  export type GetMultimediaFileAggregateType<T extends MultimediaFileAggregateArgs> = {
        [P in keyof T & keyof AggregateMultimediaFile]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateMultimediaFile[P]>
      : GetScalarType<T[P], AggregateMultimediaFile[P]>
  }




  export type MultimediaFileGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: MultimediaFileWhereInput
    orderBy?: MultimediaFileOrderByWithAggregationInput | MultimediaFileOrderByWithAggregationInput[]
    by: MultimediaFileScalarFieldEnum[] | MultimediaFileScalarFieldEnum
    having?: MultimediaFileScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: MultimediaFileCountAggregateInputType | true
    _avg?: MultimediaFileAvgAggregateInputType
    _sum?: MultimediaFileSumAggregateInputType
    _min?: MultimediaFileMinAggregateInputType
    _max?: MultimediaFileMaxAggregateInputType
  }

  export type MultimediaFileGroupByOutputType = {
    id: string
    sessionNoteId: string
    fileName: string
    originalName: string
    fileType: $Enums.MediaType
    fileSize: number
    s3Key: string
    s3Bucket: string
    description: string | null
    aiDescription: string | null
    uploadedAt: Date
    _count: MultimediaFileCountAggregateOutputType | null
    _avg: MultimediaFileAvgAggregateOutputType | null
    _sum: MultimediaFileSumAggregateOutputType | null
    _min: MultimediaFileMinAggregateOutputType | null
    _max: MultimediaFileMaxAggregateOutputType | null
  }

  type GetMultimediaFileGroupByPayload<T extends MultimediaFileGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<MultimediaFileGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof MultimediaFileGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], MultimediaFileGroupByOutputType[P]>
            : GetScalarType<T[P], MultimediaFileGroupByOutputType[P]>
        }
      >
    >


  export type MultimediaFileSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    sessionNoteId?: boolean
    fileName?: boolean
    originalName?: boolean
    fileType?: boolean
    fileSize?: boolean
    s3Key?: boolean
    s3Bucket?: boolean
    description?: boolean
    aiDescription?: boolean
    uploadedAt?: boolean
    sessionNote?: boolean | SessionNoteDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["multimediaFile"]>

  export type MultimediaFileSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    sessionNoteId?: boolean
    fileName?: boolean
    originalName?: boolean
    fileType?: boolean
    fileSize?: boolean
    s3Key?: boolean
    s3Bucket?: boolean
    description?: boolean
    aiDescription?: boolean
    uploadedAt?: boolean
    sessionNote?: boolean | SessionNoteDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["multimediaFile"]>

  export type MultimediaFileSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    sessionNoteId?: boolean
    fileName?: boolean
    originalName?: boolean
    fileType?: boolean
    fileSize?: boolean
    s3Key?: boolean
    s3Bucket?: boolean
    description?: boolean
    aiDescription?: boolean
    uploadedAt?: boolean
    sessionNote?: boolean | SessionNoteDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["multimediaFile"]>

  export type MultimediaFileSelectScalar = {
    id?: boolean
    sessionNoteId?: boolean
    fileName?: boolean
    originalName?: boolean
    fileType?: boolean
    fileSize?: boolean
    s3Key?: boolean
    s3Bucket?: boolean
    description?: boolean
    aiDescription?: boolean
    uploadedAt?: boolean
  }

  export type MultimediaFileOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "sessionNoteId" | "fileName" | "originalName" | "fileType" | "fileSize" | "s3Key" | "s3Bucket" | "description" | "aiDescription" | "uploadedAt", ExtArgs["result"]["multimediaFile"]>
  export type MultimediaFileInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    sessionNote?: boolean | SessionNoteDefaultArgs<ExtArgs>
  }
  export type MultimediaFileIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    sessionNote?: boolean | SessionNoteDefaultArgs<ExtArgs>
  }
  export type MultimediaFileIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    sessionNote?: boolean | SessionNoteDefaultArgs<ExtArgs>
  }

  export type $MultimediaFilePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "MultimediaFile"
    objects: {
      sessionNote: Prisma.$SessionNotePayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      sessionNoteId: string
      fileName: string
      originalName: string
      fileType: $Enums.MediaType
      fileSize: number
      s3Key: string
      s3Bucket: string
      description: string | null
      aiDescription: string | null
      uploadedAt: Date
    }, ExtArgs["result"]["multimediaFile"]>
    composites: {}
  }

  type MultimediaFileGetPayload<S extends boolean | null | undefined | MultimediaFileDefaultArgs> = $Result.GetResult<Prisma.$MultimediaFilePayload, S>

  type MultimediaFileCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<MultimediaFileFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: MultimediaFileCountAggregateInputType | true
    }

  export interface MultimediaFileDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['MultimediaFile'], meta: { name: 'MultimediaFile' } }
    /**
     * Find zero or one MultimediaFile that matches the filter.
     * @param {MultimediaFileFindUniqueArgs} args - Arguments to find a MultimediaFile
     * @example
     * // Get one MultimediaFile
     * const multimediaFile = await prisma.multimediaFile.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends MultimediaFileFindUniqueArgs>(args: SelectSubset<T, MultimediaFileFindUniqueArgs<ExtArgs>>): Prisma__MultimediaFileClient<$Result.GetResult<Prisma.$MultimediaFilePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one MultimediaFile that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {MultimediaFileFindUniqueOrThrowArgs} args - Arguments to find a MultimediaFile
     * @example
     * // Get one MultimediaFile
     * const multimediaFile = await prisma.multimediaFile.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends MultimediaFileFindUniqueOrThrowArgs>(args: SelectSubset<T, MultimediaFileFindUniqueOrThrowArgs<ExtArgs>>): Prisma__MultimediaFileClient<$Result.GetResult<Prisma.$MultimediaFilePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first MultimediaFile that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {MultimediaFileFindFirstArgs} args - Arguments to find a MultimediaFile
     * @example
     * // Get one MultimediaFile
     * const multimediaFile = await prisma.multimediaFile.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends MultimediaFileFindFirstArgs>(args?: SelectSubset<T, MultimediaFileFindFirstArgs<ExtArgs>>): Prisma__MultimediaFileClient<$Result.GetResult<Prisma.$MultimediaFilePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first MultimediaFile that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {MultimediaFileFindFirstOrThrowArgs} args - Arguments to find a MultimediaFile
     * @example
     * // Get one MultimediaFile
     * const multimediaFile = await prisma.multimediaFile.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends MultimediaFileFindFirstOrThrowArgs>(args?: SelectSubset<T, MultimediaFileFindFirstOrThrowArgs<ExtArgs>>): Prisma__MultimediaFileClient<$Result.GetResult<Prisma.$MultimediaFilePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more MultimediaFiles that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {MultimediaFileFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all MultimediaFiles
     * const multimediaFiles = await prisma.multimediaFile.findMany()
     * 
     * // Get first 10 MultimediaFiles
     * const multimediaFiles = await prisma.multimediaFile.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const multimediaFileWithIdOnly = await prisma.multimediaFile.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends MultimediaFileFindManyArgs>(args?: SelectSubset<T, MultimediaFileFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$MultimediaFilePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a MultimediaFile.
     * @param {MultimediaFileCreateArgs} args - Arguments to create a MultimediaFile.
     * @example
     * // Create one MultimediaFile
     * const MultimediaFile = await prisma.multimediaFile.create({
     *   data: {
     *     // ... data to create a MultimediaFile
     *   }
     * })
     * 
     */
    create<T extends MultimediaFileCreateArgs>(args: SelectSubset<T, MultimediaFileCreateArgs<ExtArgs>>): Prisma__MultimediaFileClient<$Result.GetResult<Prisma.$MultimediaFilePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many MultimediaFiles.
     * @param {MultimediaFileCreateManyArgs} args - Arguments to create many MultimediaFiles.
     * @example
     * // Create many MultimediaFiles
     * const multimediaFile = await prisma.multimediaFile.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends MultimediaFileCreateManyArgs>(args?: SelectSubset<T, MultimediaFileCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many MultimediaFiles and returns the data saved in the database.
     * @param {MultimediaFileCreateManyAndReturnArgs} args - Arguments to create many MultimediaFiles.
     * @example
     * // Create many MultimediaFiles
     * const multimediaFile = await prisma.multimediaFile.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many MultimediaFiles and only return the `id`
     * const multimediaFileWithIdOnly = await prisma.multimediaFile.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends MultimediaFileCreateManyAndReturnArgs>(args?: SelectSubset<T, MultimediaFileCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$MultimediaFilePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a MultimediaFile.
     * @param {MultimediaFileDeleteArgs} args - Arguments to delete one MultimediaFile.
     * @example
     * // Delete one MultimediaFile
     * const MultimediaFile = await prisma.multimediaFile.delete({
     *   where: {
     *     // ... filter to delete one MultimediaFile
     *   }
     * })
     * 
     */
    delete<T extends MultimediaFileDeleteArgs>(args: SelectSubset<T, MultimediaFileDeleteArgs<ExtArgs>>): Prisma__MultimediaFileClient<$Result.GetResult<Prisma.$MultimediaFilePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one MultimediaFile.
     * @param {MultimediaFileUpdateArgs} args - Arguments to update one MultimediaFile.
     * @example
     * // Update one MultimediaFile
     * const multimediaFile = await prisma.multimediaFile.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends MultimediaFileUpdateArgs>(args: SelectSubset<T, MultimediaFileUpdateArgs<ExtArgs>>): Prisma__MultimediaFileClient<$Result.GetResult<Prisma.$MultimediaFilePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more MultimediaFiles.
     * @param {MultimediaFileDeleteManyArgs} args - Arguments to filter MultimediaFiles to delete.
     * @example
     * // Delete a few MultimediaFiles
     * const { count } = await prisma.multimediaFile.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends MultimediaFileDeleteManyArgs>(args?: SelectSubset<T, MultimediaFileDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more MultimediaFiles.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {MultimediaFileUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many MultimediaFiles
     * const multimediaFile = await prisma.multimediaFile.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends MultimediaFileUpdateManyArgs>(args: SelectSubset<T, MultimediaFileUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more MultimediaFiles and returns the data updated in the database.
     * @param {MultimediaFileUpdateManyAndReturnArgs} args - Arguments to update many MultimediaFiles.
     * @example
     * // Update many MultimediaFiles
     * const multimediaFile = await prisma.multimediaFile.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more MultimediaFiles and only return the `id`
     * const multimediaFileWithIdOnly = await prisma.multimediaFile.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends MultimediaFileUpdateManyAndReturnArgs>(args: SelectSubset<T, MultimediaFileUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$MultimediaFilePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one MultimediaFile.
     * @param {MultimediaFileUpsertArgs} args - Arguments to update or create a MultimediaFile.
     * @example
     * // Update or create a MultimediaFile
     * const multimediaFile = await prisma.multimediaFile.upsert({
     *   create: {
     *     // ... data to create a MultimediaFile
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the MultimediaFile we want to update
     *   }
     * })
     */
    upsert<T extends MultimediaFileUpsertArgs>(args: SelectSubset<T, MultimediaFileUpsertArgs<ExtArgs>>): Prisma__MultimediaFileClient<$Result.GetResult<Prisma.$MultimediaFilePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of MultimediaFiles.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {MultimediaFileCountArgs} args - Arguments to filter MultimediaFiles to count.
     * @example
     * // Count the number of MultimediaFiles
     * const count = await prisma.multimediaFile.count({
     *   where: {
     *     // ... the filter for the MultimediaFiles we want to count
     *   }
     * })
    **/
    count<T extends MultimediaFileCountArgs>(
      args?: Subset<T, MultimediaFileCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], MultimediaFileCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a MultimediaFile.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {MultimediaFileAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends MultimediaFileAggregateArgs>(args: Subset<T, MultimediaFileAggregateArgs>): Prisma.PrismaPromise<GetMultimediaFileAggregateType<T>>

    /**
     * Group by MultimediaFile.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {MultimediaFileGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends MultimediaFileGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: MultimediaFileGroupByArgs['orderBy'] }
        : { orderBy?: MultimediaFileGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, MultimediaFileGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetMultimediaFileGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the MultimediaFile model
   */
  readonly fields: MultimediaFileFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for MultimediaFile.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__MultimediaFileClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    sessionNote<T extends SessionNoteDefaultArgs<ExtArgs> = {}>(args?: Subset<T, SessionNoteDefaultArgs<ExtArgs>>): Prisma__SessionNoteClient<$Result.GetResult<Prisma.$SessionNotePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the MultimediaFile model
   */
  interface MultimediaFileFieldRefs {
    readonly id: FieldRef<"MultimediaFile", 'String'>
    readonly sessionNoteId: FieldRef<"MultimediaFile", 'String'>
    readonly fileName: FieldRef<"MultimediaFile", 'String'>
    readonly originalName: FieldRef<"MultimediaFile", 'String'>
    readonly fileType: FieldRef<"MultimediaFile", 'MediaType'>
    readonly fileSize: FieldRef<"MultimediaFile", 'Int'>
    readonly s3Key: FieldRef<"MultimediaFile", 'String'>
    readonly s3Bucket: FieldRef<"MultimediaFile", 'String'>
    readonly description: FieldRef<"MultimediaFile", 'String'>
    readonly aiDescription: FieldRef<"MultimediaFile", 'String'>
    readonly uploadedAt: FieldRef<"MultimediaFile", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * MultimediaFile findUnique
   */
  export type MultimediaFileFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MultimediaFile
     */
    select?: MultimediaFileSelect<ExtArgs> | null
    /**
     * Omit specific fields from the MultimediaFile
     */
    omit?: MultimediaFileOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MultimediaFileInclude<ExtArgs> | null
    /**
     * Filter, which MultimediaFile to fetch.
     */
    where: MultimediaFileWhereUniqueInput
  }

  /**
   * MultimediaFile findUniqueOrThrow
   */
  export type MultimediaFileFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MultimediaFile
     */
    select?: MultimediaFileSelect<ExtArgs> | null
    /**
     * Omit specific fields from the MultimediaFile
     */
    omit?: MultimediaFileOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MultimediaFileInclude<ExtArgs> | null
    /**
     * Filter, which MultimediaFile to fetch.
     */
    where: MultimediaFileWhereUniqueInput
  }

  /**
   * MultimediaFile findFirst
   */
  export type MultimediaFileFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MultimediaFile
     */
    select?: MultimediaFileSelect<ExtArgs> | null
    /**
     * Omit specific fields from the MultimediaFile
     */
    omit?: MultimediaFileOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MultimediaFileInclude<ExtArgs> | null
    /**
     * Filter, which MultimediaFile to fetch.
     */
    where?: MultimediaFileWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of MultimediaFiles to fetch.
     */
    orderBy?: MultimediaFileOrderByWithRelationInput | MultimediaFileOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for MultimediaFiles.
     */
    cursor?: MultimediaFileWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` MultimediaFiles from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` MultimediaFiles.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of MultimediaFiles.
     */
    distinct?: MultimediaFileScalarFieldEnum | MultimediaFileScalarFieldEnum[]
  }

  /**
   * MultimediaFile findFirstOrThrow
   */
  export type MultimediaFileFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MultimediaFile
     */
    select?: MultimediaFileSelect<ExtArgs> | null
    /**
     * Omit specific fields from the MultimediaFile
     */
    omit?: MultimediaFileOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MultimediaFileInclude<ExtArgs> | null
    /**
     * Filter, which MultimediaFile to fetch.
     */
    where?: MultimediaFileWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of MultimediaFiles to fetch.
     */
    orderBy?: MultimediaFileOrderByWithRelationInput | MultimediaFileOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for MultimediaFiles.
     */
    cursor?: MultimediaFileWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` MultimediaFiles from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` MultimediaFiles.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of MultimediaFiles.
     */
    distinct?: MultimediaFileScalarFieldEnum | MultimediaFileScalarFieldEnum[]
  }

  /**
   * MultimediaFile findMany
   */
  export type MultimediaFileFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MultimediaFile
     */
    select?: MultimediaFileSelect<ExtArgs> | null
    /**
     * Omit specific fields from the MultimediaFile
     */
    omit?: MultimediaFileOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MultimediaFileInclude<ExtArgs> | null
    /**
     * Filter, which MultimediaFiles to fetch.
     */
    where?: MultimediaFileWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of MultimediaFiles to fetch.
     */
    orderBy?: MultimediaFileOrderByWithRelationInput | MultimediaFileOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing MultimediaFiles.
     */
    cursor?: MultimediaFileWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` MultimediaFiles from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` MultimediaFiles.
     */
    skip?: number
    distinct?: MultimediaFileScalarFieldEnum | MultimediaFileScalarFieldEnum[]
  }

  /**
   * MultimediaFile create
   */
  export type MultimediaFileCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MultimediaFile
     */
    select?: MultimediaFileSelect<ExtArgs> | null
    /**
     * Omit specific fields from the MultimediaFile
     */
    omit?: MultimediaFileOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MultimediaFileInclude<ExtArgs> | null
    /**
     * The data needed to create a MultimediaFile.
     */
    data: XOR<MultimediaFileCreateInput, MultimediaFileUncheckedCreateInput>
  }

  /**
   * MultimediaFile createMany
   */
  export type MultimediaFileCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many MultimediaFiles.
     */
    data: MultimediaFileCreateManyInput | MultimediaFileCreateManyInput[]
  }

  /**
   * MultimediaFile createManyAndReturn
   */
  export type MultimediaFileCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MultimediaFile
     */
    select?: MultimediaFileSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the MultimediaFile
     */
    omit?: MultimediaFileOmit<ExtArgs> | null
    /**
     * The data used to create many MultimediaFiles.
     */
    data: MultimediaFileCreateManyInput | MultimediaFileCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MultimediaFileIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * MultimediaFile update
   */
  export type MultimediaFileUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MultimediaFile
     */
    select?: MultimediaFileSelect<ExtArgs> | null
    /**
     * Omit specific fields from the MultimediaFile
     */
    omit?: MultimediaFileOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MultimediaFileInclude<ExtArgs> | null
    /**
     * The data needed to update a MultimediaFile.
     */
    data: XOR<MultimediaFileUpdateInput, MultimediaFileUncheckedUpdateInput>
    /**
     * Choose, which MultimediaFile to update.
     */
    where: MultimediaFileWhereUniqueInput
  }

  /**
   * MultimediaFile updateMany
   */
  export type MultimediaFileUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update MultimediaFiles.
     */
    data: XOR<MultimediaFileUpdateManyMutationInput, MultimediaFileUncheckedUpdateManyInput>
    /**
     * Filter which MultimediaFiles to update
     */
    where?: MultimediaFileWhereInput
    /**
     * Limit how many MultimediaFiles to update.
     */
    limit?: number
  }

  /**
   * MultimediaFile updateManyAndReturn
   */
  export type MultimediaFileUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MultimediaFile
     */
    select?: MultimediaFileSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the MultimediaFile
     */
    omit?: MultimediaFileOmit<ExtArgs> | null
    /**
     * The data used to update MultimediaFiles.
     */
    data: XOR<MultimediaFileUpdateManyMutationInput, MultimediaFileUncheckedUpdateManyInput>
    /**
     * Filter which MultimediaFiles to update
     */
    where?: MultimediaFileWhereInput
    /**
     * Limit how many MultimediaFiles to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MultimediaFileIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * MultimediaFile upsert
   */
  export type MultimediaFileUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MultimediaFile
     */
    select?: MultimediaFileSelect<ExtArgs> | null
    /**
     * Omit specific fields from the MultimediaFile
     */
    omit?: MultimediaFileOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MultimediaFileInclude<ExtArgs> | null
    /**
     * The filter to search for the MultimediaFile to update in case it exists.
     */
    where: MultimediaFileWhereUniqueInput
    /**
     * In case the MultimediaFile found by the `where` argument doesn't exist, create a new MultimediaFile with this data.
     */
    create: XOR<MultimediaFileCreateInput, MultimediaFileUncheckedCreateInput>
    /**
     * In case the MultimediaFile was found with the provided `where` argument, update it with this data.
     */
    update: XOR<MultimediaFileUpdateInput, MultimediaFileUncheckedUpdateInput>
  }

  /**
   * MultimediaFile delete
   */
  export type MultimediaFileDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MultimediaFile
     */
    select?: MultimediaFileSelect<ExtArgs> | null
    /**
     * Omit specific fields from the MultimediaFile
     */
    omit?: MultimediaFileOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MultimediaFileInclude<ExtArgs> | null
    /**
     * Filter which MultimediaFile to delete.
     */
    where: MultimediaFileWhereUniqueInput
  }

  /**
   * MultimediaFile deleteMany
   */
  export type MultimediaFileDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which MultimediaFiles to delete
     */
    where?: MultimediaFileWhereInput
    /**
     * Limit how many MultimediaFiles to delete.
     */
    limit?: number
  }

  /**
   * MultimediaFile without action
   */
  export type MultimediaFileDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MultimediaFile
     */
    select?: MultimediaFileSelect<ExtArgs> | null
    /**
     * Omit specific fields from the MultimediaFile
     */
    omit?: MultimediaFileOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MultimediaFileInclude<ExtArgs> | null
  }


  /**
   * Model AiSuggestion
   */

  export type AggregateAiSuggestion = {
    _count: AiSuggestionCountAggregateOutputType | null
    _min: AiSuggestionMinAggregateOutputType | null
    _max: AiSuggestionMaxAggregateOutputType | null
  }

  export type AiSuggestionMinAggregateOutputType = {
    id: string | null
    sessionNoteId: string | null
    multimediaId: string | null
    suggestionType: $Enums.SuggestionType | null
    originalText: string | null
    suggestedText: string | null
    isAccepted: boolean | null
    isRejected: boolean | null
    createdAt: Date | null
  }

  export type AiSuggestionMaxAggregateOutputType = {
    id: string | null
    sessionNoteId: string | null
    multimediaId: string | null
    suggestionType: $Enums.SuggestionType | null
    originalText: string | null
    suggestedText: string | null
    isAccepted: boolean | null
    isRejected: boolean | null
    createdAt: Date | null
  }

  export type AiSuggestionCountAggregateOutputType = {
    id: number
    sessionNoteId: number
    multimediaId: number
    suggestionType: number
    originalText: number
    suggestedText: number
    isAccepted: number
    isRejected: number
    createdAt: number
    _all: number
  }


  export type AiSuggestionMinAggregateInputType = {
    id?: true
    sessionNoteId?: true
    multimediaId?: true
    suggestionType?: true
    originalText?: true
    suggestedText?: true
    isAccepted?: true
    isRejected?: true
    createdAt?: true
  }

  export type AiSuggestionMaxAggregateInputType = {
    id?: true
    sessionNoteId?: true
    multimediaId?: true
    suggestionType?: true
    originalText?: true
    suggestedText?: true
    isAccepted?: true
    isRejected?: true
    createdAt?: true
  }

  export type AiSuggestionCountAggregateInputType = {
    id?: true
    sessionNoteId?: true
    multimediaId?: true
    suggestionType?: true
    originalText?: true
    suggestedText?: true
    isAccepted?: true
    isRejected?: true
    createdAt?: true
    _all?: true
  }

  export type AiSuggestionAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which AiSuggestion to aggregate.
     */
    where?: AiSuggestionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AiSuggestions to fetch.
     */
    orderBy?: AiSuggestionOrderByWithRelationInput | AiSuggestionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: AiSuggestionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AiSuggestions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AiSuggestions.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned AiSuggestions
    **/
    _count?: true | AiSuggestionCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: AiSuggestionMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: AiSuggestionMaxAggregateInputType
  }

  export type GetAiSuggestionAggregateType<T extends AiSuggestionAggregateArgs> = {
        [P in keyof T & keyof AggregateAiSuggestion]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateAiSuggestion[P]>
      : GetScalarType<T[P], AggregateAiSuggestion[P]>
  }




  export type AiSuggestionGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: AiSuggestionWhereInput
    orderBy?: AiSuggestionOrderByWithAggregationInput | AiSuggestionOrderByWithAggregationInput[]
    by: AiSuggestionScalarFieldEnum[] | AiSuggestionScalarFieldEnum
    having?: AiSuggestionScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: AiSuggestionCountAggregateInputType | true
    _min?: AiSuggestionMinAggregateInputType
    _max?: AiSuggestionMaxAggregateInputType
  }

  export type AiSuggestionGroupByOutputType = {
    id: string
    sessionNoteId: string
    multimediaId: string | null
    suggestionType: $Enums.SuggestionType
    originalText: string | null
    suggestedText: string
    isAccepted: boolean
    isRejected: boolean
    createdAt: Date
    _count: AiSuggestionCountAggregateOutputType | null
    _min: AiSuggestionMinAggregateOutputType | null
    _max: AiSuggestionMaxAggregateOutputType | null
  }

  type GetAiSuggestionGroupByPayload<T extends AiSuggestionGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<AiSuggestionGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof AiSuggestionGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], AiSuggestionGroupByOutputType[P]>
            : GetScalarType<T[P], AiSuggestionGroupByOutputType[P]>
        }
      >
    >


  export type AiSuggestionSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    sessionNoteId?: boolean
    multimediaId?: boolean
    suggestionType?: boolean
    originalText?: boolean
    suggestedText?: boolean
    isAccepted?: boolean
    isRejected?: boolean
    createdAt?: boolean
    sessionNote?: boolean | SessionNoteDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["aiSuggestion"]>

  export type AiSuggestionSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    sessionNoteId?: boolean
    multimediaId?: boolean
    suggestionType?: boolean
    originalText?: boolean
    suggestedText?: boolean
    isAccepted?: boolean
    isRejected?: boolean
    createdAt?: boolean
    sessionNote?: boolean | SessionNoteDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["aiSuggestion"]>

  export type AiSuggestionSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    sessionNoteId?: boolean
    multimediaId?: boolean
    suggestionType?: boolean
    originalText?: boolean
    suggestedText?: boolean
    isAccepted?: boolean
    isRejected?: boolean
    createdAt?: boolean
    sessionNote?: boolean | SessionNoteDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["aiSuggestion"]>

  export type AiSuggestionSelectScalar = {
    id?: boolean
    sessionNoteId?: boolean
    multimediaId?: boolean
    suggestionType?: boolean
    originalText?: boolean
    suggestedText?: boolean
    isAccepted?: boolean
    isRejected?: boolean
    createdAt?: boolean
  }

  export type AiSuggestionOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "sessionNoteId" | "multimediaId" | "suggestionType" | "originalText" | "suggestedText" | "isAccepted" | "isRejected" | "createdAt", ExtArgs["result"]["aiSuggestion"]>
  export type AiSuggestionInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    sessionNote?: boolean | SessionNoteDefaultArgs<ExtArgs>
  }
  export type AiSuggestionIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    sessionNote?: boolean | SessionNoteDefaultArgs<ExtArgs>
  }
  export type AiSuggestionIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    sessionNote?: boolean | SessionNoteDefaultArgs<ExtArgs>
  }

  export type $AiSuggestionPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "AiSuggestion"
    objects: {
      sessionNote: Prisma.$SessionNotePayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      sessionNoteId: string
      multimediaId: string | null
      suggestionType: $Enums.SuggestionType
      originalText: string | null
      suggestedText: string
      isAccepted: boolean
      isRejected: boolean
      createdAt: Date
    }, ExtArgs["result"]["aiSuggestion"]>
    composites: {}
  }

  type AiSuggestionGetPayload<S extends boolean | null | undefined | AiSuggestionDefaultArgs> = $Result.GetResult<Prisma.$AiSuggestionPayload, S>

  type AiSuggestionCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<AiSuggestionFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: AiSuggestionCountAggregateInputType | true
    }

  export interface AiSuggestionDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['AiSuggestion'], meta: { name: 'AiSuggestion' } }
    /**
     * Find zero or one AiSuggestion that matches the filter.
     * @param {AiSuggestionFindUniqueArgs} args - Arguments to find a AiSuggestion
     * @example
     * // Get one AiSuggestion
     * const aiSuggestion = await prisma.aiSuggestion.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends AiSuggestionFindUniqueArgs>(args: SelectSubset<T, AiSuggestionFindUniqueArgs<ExtArgs>>): Prisma__AiSuggestionClient<$Result.GetResult<Prisma.$AiSuggestionPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one AiSuggestion that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {AiSuggestionFindUniqueOrThrowArgs} args - Arguments to find a AiSuggestion
     * @example
     * // Get one AiSuggestion
     * const aiSuggestion = await prisma.aiSuggestion.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends AiSuggestionFindUniqueOrThrowArgs>(args: SelectSubset<T, AiSuggestionFindUniqueOrThrowArgs<ExtArgs>>): Prisma__AiSuggestionClient<$Result.GetResult<Prisma.$AiSuggestionPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first AiSuggestion that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AiSuggestionFindFirstArgs} args - Arguments to find a AiSuggestion
     * @example
     * // Get one AiSuggestion
     * const aiSuggestion = await prisma.aiSuggestion.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends AiSuggestionFindFirstArgs>(args?: SelectSubset<T, AiSuggestionFindFirstArgs<ExtArgs>>): Prisma__AiSuggestionClient<$Result.GetResult<Prisma.$AiSuggestionPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first AiSuggestion that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AiSuggestionFindFirstOrThrowArgs} args - Arguments to find a AiSuggestion
     * @example
     * // Get one AiSuggestion
     * const aiSuggestion = await prisma.aiSuggestion.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends AiSuggestionFindFirstOrThrowArgs>(args?: SelectSubset<T, AiSuggestionFindFirstOrThrowArgs<ExtArgs>>): Prisma__AiSuggestionClient<$Result.GetResult<Prisma.$AiSuggestionPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more AiSuggestions that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AiSuggestionFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all AiSuggestions
     * const aiSuggestions = await prisma.aiSuggestion.findMany()
     * 
     * // Get first 10 AiSuggestions
     * const aiSuggestions = await prisma.aiSuggestion.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const aiSuggestionWithIdOnly = await prisma.aiSuggestion.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends AiSuggestionFindManyArgs>(args?: SelectSubset<T, AiSuggestionFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AiSuggestionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a AiSuggestion.
     * @param {AiSuggestionCreateArgs} args - Arguments to create a AiSuggestion.
     * @example
     * // Create one AiSuggestion
     * const AiSuggestion = await prisma.aiSuggestion.create({
     *   data: {
     *     // ... data to create a AiSuggestion
     *   }
     * })
     * 
     */
    create<T extends AiSuggestionCreateArgs>(args: SelectSubset<T, AiSuggestionCreateArgs<ExtArgs>>): Prisma__AiSuggestionClient<$Result.GetResult<Prisma.$AiSuggestionPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many AiSuggestions.
     * @param {AiSuggestionCreateManyArgs} args - Arguments to create many AiSuggestions.
     * @example
     * // Create many AiSuggestions
     * const aiSuggestion = await prisma.aiSuggestion.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends AiSuggestionCreateManyArgs>(args?: SelectSubset<T, AiSuggestionCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many AiSuggestions and returns the data saved in the database.
     * @param {AiSuggestionCreateManyAndReturnArgs} args - Arguments to create many AiSuggestions.
     * @example
     * // Create many AiSuggestions
     * const aiSuggestion = await prisma.aiSuggestion.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many AiSuggestions and only return the `id`
     * const aiSuggestionWithIdOnly = await prisma.aiSuggestion.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends AiSuggestionCreateManyAndReturnArgs>(args?: SelectSubset<T, AiSuggestionCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AiSuggestionPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a AiSuggestion.
     * @param {AiSuggestionDeleteArgs} args - Arguments to delete one AiSuggestion.
     * @example
     * // Delete one AiSuggestion
     * const AiSuggestion = await prisma.aiSuggestion.delete({
     *   where: {
     *     // ... filter to delete one AiSuggestion
     *   }
     * })
     * 
     */
    delete<T extends AiSuggestionDeleteArgs>(args: SelectSubset<T, AiSuggestionDeleteArgs<ExtArgs>>): Prisma__AiSuggestionClient<$Result.GetResult<Prisma.$AiSuggestionPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one AiSuggestion.
     * @param {AiSuggestionUpdateArgs} args - Arguments to update one AiSuggestion.
     * @example
     * // Update one AiSuggestion
     * const aiSuggestion = await prisma.aiSuggestion.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends AiSuggestionUpdateArgs>(args: SelectSubset<T, AiSuggestionUpdateArgs<ExtArgs>>): Prisma__AiSuggestionClient<$Result.GetResult<Prisma.$AiSuggestionPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more AiSuggestions.
     * @param {AiSuggestionDeleteManyArgs} args - Arguments to filter AiSuggestions to delete.
     * @example
     * // Delete a few AiSuggestions
     * const { count } = await prisma.aiSuggestion.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends AiSuggestionDeleteManyArgs>(args?: SelectSubset<T, AiSuggestionDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more AiSuggestions.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AiSuggestionUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many AiSuggestions
     * const aiSuggestion = await prisma.aiSuggestion.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends AiSuggestionUpdateManyArgs>(args: SelectSubset<T, AiSuggestionUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more AiSuggestions and returns the data updated in the database.
     * @param {AiSuggestionUpdateManyAndReturnArgs} args - Arguments to update many AiSuggestions.
     * @example
     * // Update many AiSuggestions
     * const aiSuggestion = await prisma.aiSuggestion.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more AiSuggestions and only return the `id`
     * const aiSuggestionWithIdOnly = await prisma.aiSuggestion.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends AiSuggestionUpdateManyAndReturnArgs>(args: SelectSubset<T, AiSuggestionUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AiSuggestionPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one AiSuggestion.
     * @param {AiSuggestionUpsertArgs} args - Arguments to update or create a AiSuggestion.
     * @example
     * // Update or create a AiSuggestion
     * const aiSuggestion = await prisma.aiSuggestion.upsert({
     *   create: {
     *     // ... data to create a AiSuggestion
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the AiSuggestion we want to update
     *   }
     * })
     */
    upsert<T extends AiSuggestionUpsertArgs>(args: SelectSubset<T, AiSuggestionUpsertArgs<ExtArgs>>): Prisma__AiSuggestionClient<$Result.GetResult<Prisma.$AiSuggestionPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of AiSuggestions.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AiSuggestionCountArgs} args - Arguments to filter AiSuggestions to count.
     * @example
     * // Count the number of AiSuggestions
     * const count = await prisma.aiSuggestion.count({
     *   where: {
     *     // ... the filter for the AiSuggestions we want to count
     *   }
     * })
    **/
    count<T extends AiSuggestionCountArgs>(
      args?: Subset<T, AiSuggestionCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], AiSuggestionCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a AiSuggestion.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AiSuggestionAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends AiSuggestionAggregateArgs>(args: Subset<T, AiSuggestionAggregateArgs>): Prisma.PrismaPromise<GetAiSuggestionAggregateType<T>>

    /**
     * Group by AiSuggestion.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AiSuggestionGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends AiSuggestionGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: AiSuggestionGroupByArgs['orderBy'] }
        : { orderBy?: AiSuggestionGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, AiSuggestionGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetAiSuggestionGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the AiSuggestion model
   */
  readonly fields: AiSuggestionFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for AiSuggestion.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__AiSuggestionClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    sessionNote<T extends SessionNoteDefaultArgs<ExtArgs> = {}>(args?: Subset<T, SessionNoteDefaultArgs<ExtArgs>>): Prisma__SessionNoteClient<$Result.GetResult<Prisma.$SessionNotePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the AiSuggestion model
   */
  interface AiSuggestionFieldRefs {
    readonly id: FieldRef<"AiSuggestion", 'String'>
    readonly sessionNoteId: FieldRef<"AiSuggestion", 'String'>
    readonly multimediaId: FieldRef<"AiSuggestion", 'String'>
    readonly suggestionType: FieldRef<"AiSuggestion", 'SuggestionType'>
    readonly originalText: FieldRef<"AiSuggestion", 'String'>
    readonly suggestedText: FieldRef<"AiSuggestion", 'String'>
    readonly isAccepted: FieldRef<"AiSuggestion", 'Boolean'>
    readonly isRejected: FieldRef<"AiSuggestion", 'Boolean'>
    readonly createdAt: FieldRef<"AiSuggestion", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * AiSuggestion findUnique
   */
  export type AiSuggestionFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AiSuggestion
     */
    select?: AiSuggestionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AiSuggestion
     */
    omit?: AiSuggestionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AiSuggestionInclude<ExtArgs> | null
    /**
     * Filter, which AiSuggestion to fetch.
     */
    where: AiSuggestionWhereUniqueInput
  }

  /**
   * AiSuggestion findUniqueOrThrow
   */
  export type AiSuggestionFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AiSuggestion
     */
    select?: AiSuggestionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AiSuggestion
     */
    omit?: AiSuggestionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AiSuggestionInclude<ExtArgs> | null
    /**
     * Filter, which AiSuggestion to fetch.
     */
    where: AiSuggestionWhereUniqueInput
  }

  /**
   * AiSuggestion findFirst
   */
  export type AiSuggestionFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AiSuggestion
     */
    select?: AiSuggestionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AiSuggestion
     */
    omit?: AiSuggestionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AiSuggestionInclude<ExtArgs> | null
    /**
     * Filter, which AiSuggestion to fetch.
     */
    where?: AiSuggestionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AiSuggestions to fetch.
     */
    orderBy?: AiSuggestionOrderByWithRelationInput | AiSuggestionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for AiSuggestions.
     */
    cursor?: AiSuggestionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AiSuggestions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AiSuggestions.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of AiSuggestions.
     */
    distinct?: AiSuggestionScalarFieldEnum | AiSuggestionScalarFieldEnum[]
  }

  /**
   * AiSuggestion findFirstOrThrow
   */
  export type AiSuggestionFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AiSuggestion
     */
    select?: AiSuggestionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AiSuggestion
     */
    omit?: AiSuggestionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AiSuggestionInclude<ExtArgs> | null
    /**
     * Filter, which AiSuggestion to fetch.
     */
    where?: AiSuggestionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AiSuggestions to fetch.
     */
    orderBy?: AiSuggestionOrderByWithRelationInput | AiSuggestionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for AiSuggestions.
     */
    cursor?: AiSuggestionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AiSuggestions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AiSuggestions.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of AiSuggestions.
     */
    distinct?: AiSuggestionScalarFieldEnum | AiSuggestionScalarFieldEnum[]
  }

  /**
   * AiSuggestion findMany
   */
  export type AiSuggestionFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AiSuggestion
     */
    select?: AiSuggestionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AiSuggestion
     */
    omit?: AiSuggestionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AiSuggestionInclude<ExtArgs> | null
    /**
     * Filter, which AiSuggestions to fetch.
     */
    where?: AiSuggestionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AiSuggestions to fetch.
     */
    orderBy?: AiSuggestionOrderByWithRelationInput | AiSuggestionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing AiSuggestions.
     */
    cursor?: AiSuggestionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AiSuggestions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AiSuggestions.
     */
    skip?: number
    distinct?: AiSuggestionScalarFieldEnum | AiSuggestionScalarFieldEnum[]
  }

  /**
   * AiSuggestion create
   */
  export type AiSuggestionCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AiSuggestion
     */
    select?: AiSuggestionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AiSuggestion
     */
    omit?: AiSuggestionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AiSuggestionInclude<ExtArgs> | null
    /**
     * The data needed to create a AiSuggestion.
     */
    data: XOR<AiSuggestionCreateInput, AiSuggestionUncheckedCreateInput>
  }

  /**
   * AiSuggestion createMany
   */
  export type AiSuggestionCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many AiSuggestions.
     */
    data: AiSuggestionCreateManyInput | AiSuggestionCreateManyInput[]
  }

  /**
   * AiSuggestion createManyAndReturn
   */
  export type AiSuggestionCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AiSuggestion
     */
    select?: AiSuggestionSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the AiSuggestion
     */
    omit?: AiSuggestionOmit<ExtArgs> | null
    /**
     * The data used to create many AiSuggestions.
     */
    data: AiSuggestionCreateManyInput | AiSuggestionCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AiSuggestionIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * AiSuggestion update
   */
  export type AiSuggestionUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AiSuggestion
     */
    select?: AiSuggestionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AiSuggestion
     */
    omit?: AiSuggestionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AiSuggestionInclude<ExtArgs> | null
    /**
     * The data needed to update a AiSuggestion.
     */
    data: XOR<AiSuggestionUpdateInput, AiSuggestionUncheckedUpdateInput>
    /**
     * Choose, which AiSuggestion to update.
     */
    where: AiSuggestionWhereUniqueInput
  }

  /**
   * AiSuggestion updateMany
   */
  export type AiSuggestionUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update AiSuggestions.
     */
    data: XOR<AiSuggestionUpdateManyMutationInput, AiSuggestionUncheckedUpdateManyInput>
    /**
     * Filter which AiSuggestions to update
     */
    where?: AiSuggestionWhereInput
    /**
     * Limit how many AiSuggestions to update.
     */
    limit?: number
  }

  /**
   * AiSuggestion updateManyAndReturn
   */
  export type AiSuggestionUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AiSuggestion
     */
    select?: AiSuggestionSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the AiSuggestion
     */
    omit?: AiSuggestionOmit<ExtArgs> | null
    /**
     * The data used to update AiSuggestions.
     */
    data: XOR<AiSuggestionUpdateManyMutationInput, AiSuggestionUncheckedUpdateManyInput>
    /**
     * Filter which AiSuggestions to update
     */
    where?: AiSuggestionWhereInput
    /**
     * Limit how many AiSuggestions to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AiSuggestionIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * AiSuggestion upsert
   */
  export type AiSuggestionUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AiSuggestion
     */
    select?: AiSuggestionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AiSuggestion
     */
    omit?: AiSuggestionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AiSuggestionInclude<ExtArgs> | null
    /**
     * The filter to search for the AiSuggestion to update in case it exists.
     */
    where: AiSuggestionWhereUniqueInput
    /**
     * In case the AiSuggestion found by the `where` argument doesn't exist, create a new AiSuggestion with this data.
     */
    create: XOR<AiSuggestionCreateInput, AiSuggestionUncheckedCreateInput>
    /**
     * In case the AiSuggestion was found with the provided `where` argument, update it with this data.
     */
    update: XOR<AiSuggestionUpdateInput, AiSuggestionUncheckedUpdateInput>
  }

  /**
   * AiSuggestion delete
   */
  export type AiSuggestionDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AiSuggestion
     */
    select?: AiSuggestionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AiSuggestion
     */
    omit?: AiSuggestionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AiSuggestionInclude<ExtArgs> | null
    /**
     * Filter which AiSuggestion to delete.
     */
    where: AiSuggestionWhereUniqueInput
  }

  /**
   * AiSuggestion deleteMany
   */
  export type AiSuggestionDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which AiSuggestions to delete
     */
    where?: AiSuggestionWhereInput
    /**
     * Limit how many AiSuggestions to delete.
     */
    limit?: number
  }

  /**
   * AiSuggestion without action
   */
  export type AiSuggestionDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AiSuggestion
     */
    select?: AiSuggestionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AiSuggestion
     */
    omit?: AiSuggestionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AiSuggestionInclude<ExtArgs> | null
  }


  /**
   * Model AuditLog
   */

  export type AggregateAuditLog = {
    _count: AuditLogCountAggregateOutputType | null
    _min: AuditLogMinAggregateOutputType | null
    _max: AuditLogMaxAggregateOutputType | null
  }

  export type AuditLogMinAggregateOutputType = {
    id: string | null
    userId: string | null
    action: string | null
    resourceType: string | null
    resourceId: string | null
    ipAddress: string | null
    userAgent: string | null
    timestamp: Date | null
  }

  export type AuditLogMaxAggregateOutputType = {
    id: string | null
    userId: string | null
    action: string | null
    resourceType: string | null
    resourceId: string | null
    ipAddress: string | null
    userAgent: string | null
    timestamp: Date | null
  }

  export type AuditLogCountAggregateOutputType = {
    id: number
    userId: number
    action: number
    resourceType: number
    resourceId: number
    details: number
    ipAddress: number
    userAgent: number
    timestamp: number
    _all: number
  }


  export type AuditLogMinAggregateInputType = {
    id?: true
    userId?: true
    action?: true
    resourceType?: true
    resourceId?: true
    ipAddress?: true
    userAgent?: true
    timestamp?: true
  }

  export type AuditLogMaxAggregateInputType = {
    id?: true
    userId?: true
    action?: true
    resourceType?: true
    resourceId?: true
    ipAddress?: true
    userAgent?: true
    timestamp?: true
  }

  export type AuditLogCountAggregateInputType = {
    id?: true
    userId?: true
    action?: true
    resourceType?: true
    resourceId?: true
    details?: true
    ipAddress?: true
    userAgent?: true
    timestamp?: true
    _all?: true
  }

  export type AuditLogAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which AuditLog to aggregate.
     */
    where?: AuditLogWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AuditLogs to fetch.
     */
    orderBy?: AuditLogOrderByWithRelationInput | AuditLogOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: AuditLogWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AuditLogs from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AuditLogs.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned AuditLogs
    **/
    _count?: true | AuditLogCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: AuditLogMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: AuditLogMaxAggregateInputType
  }

  export type GetAuditLogAggregateType<T extends AuditLogAggregateArgs> = {
        [P in keyof T & keyof AggregateAuditLog]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateAuditLog[P]>
      : GetScalarType<T[P], AggregateAuditLog[P]>
  }




  export type AuditLogGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: AuditLogWhereInput
    orderBy?: AuditLogOrderByWithAggregationInput | AuditLogOrderByWithAggregationInput[]
    by: AuditLogScalarFieldEnum[] | AuditLogScalarFieldEnum
    having?: AuditLogScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: AuditLogCountAggregateInputType | true
    _min?: AuditLogMinAggregateInputType
    _max?: AuditLogMaxAggregateInputType
  }

  export type AuditLogGroupByOutputType = {
    id: string
    userId: string
    action: string
    resourceType: string
    resourceId: string
    details: JsonValue | null
    ipAddress: string | null
    userAgent: string | null
    timestamp: Date
    _count: AuditLogCountAggregateOutputType | null
    _min: AuditLogMinAggregateOutputType | null
    _max: AuditLogMaxAggregateOutputType | null
  }

  type GetAuditLogGroupByPayload<T extends AuditLogGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<AuditLogGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof AuditLogGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], AuditLogGroupByOutputType[P]>
            : GetScalarType<T[P], AuditLogGroupByOutputType[P]>
        }
      >
    >


  export type AuditLogSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    action?: boolean
    resourceType?: boolean
    resourceId?: boolean
    details?: boolean
    ipAddress?: boolean
    userAgent?: boolean
    timestamp?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["auditLog"]>

  export type AuditLogSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    action?: boolean
    resourceType?: boolean
    resourceId?: boolean
    details?: boolean
    ipAddress?: boolean
    userAgent?: boolean
    timestamp?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["auditLog"]>

  export type AuditLogSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    action?: boolean
    resourceType?: boolean
    resourceId?: boolean
    details?: boolean
    ipAddress?: boolean
    userAgent?: boolean
    timestamp?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["auditLog"]>

  export type AuditLogSelectScalar = {
    id?: boolean
    userId?: boolean
    action?: boolean
    resourceType?: boolean
    resourceId?: boolean
    details?: boolean
    ipAddress?: boolean
    userAgent?: boolean
    timestamp?: boolean
  }

  export type AuditLogOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "userId" | "action" | "resourceType" | "resourceId" | "details" | "ipAddress" | "userAgent" | "timestamp", ExtArgs["result"]["auditLog"]>
  export type AuditLogInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type AuditLogIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type AuditLogIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }

  export type $AuditLogPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "AuditLog"
    objects: {
      user: Prisma.$UserPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      userId: string
      action: string
      resourceType: string
      resourceId: string
      details: Prisma.JsonValue | null
      ipAddress: string | null
      userAgent: string | null
      timestamp: Date
    }, ExtArgs["result"]["auditLog"]>
    composites: {}
  }

  type AuditLogGetPayload<S extends boolean | null | undefined | AuditLogDefaultArgs> = $Result.GetResult<Prisma.$AuditLogPayload, S>

  type AuditLogCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<AuditLogFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: AuditLogCountAggregateInputType | true
    }

  export interface AuditLogDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['AuditLog'], meta: { name: 'AuditLog' } }
    /**
     * Find zero or one AuditLog that matches the filter.
     * @param {AuditLogFindUniqueArgs} args - Arguments to find a AuditLog
     * @example
     * // Get one AuditLog
     * const auditLog = await prisma.auditLog.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends AuditLogFindUniqueArgs>(args: SelectSubset<T, AuditLogFindUniqueArgs<ExtArgs>>): Prisma__AuditLogClient<$Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one AuditLog that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {AuditLogFindUniqueOrThrowArgs} args - Arguments to find a AuditLog
     * @example
     * // Get one AuditLog
     * const auditLog = await prisma.auditLog.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends AuditLogFindUniqueOrThrowArgs>(args: SelectSubset<T, AuditLogFindUniqueOrThrowArgs<ExtArgs>>): Prisma__AuditLogClient<$Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first AuditLog that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AuditLogFindFirstArgs} args - Arguments to find a AuditLog
     * @example
     * // Get one AuditLog
     * const auditLog = await prisma.auditLog.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends AuditLogFindFirstArgs>(args?: SelectSubset<T, AuditLogFindFirstArgs<ExtArgs>>): Prisma__AuditLogClient<$Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first AuditLog that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AuditLogFindFirstOrThrowArgs} args - Arguments to find a AuditLog
     * @example
     * // Get one AuditLog
     * const auditLog = await prisma.auditLog.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends AuditLogFindFirstOrThrowArgs>(args?: SelectSubset<T, AuditLogFindFirstOrThrowArgs<ExtArgs>>): Prisma__AuditLogClient<$Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more AuditLogs that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AuditLogFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all AuditLogs
     * const auditLogs = await prisma.auditLog.findMany()
     * 
     * // Get first 10 AuditLogs
     * const auditLogs = await prisma.auditLog.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const auditLogWithIdOnly = await prisma.auditLog.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends AuditLogFindManyArgs>(args?: SelectSubset<T, AuditLogFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a AuditLog.
     * @param {AuditLogCreateArgs} args - Arguments to create a AuditLog.
     * @example
     * // Create one AuditLog
     * const AuditLog = await prisma.auditLog.create({
     *   data: {
     *     // ... data to create a AuditLog
     *   }
     * })
     * 
     */
    create<T extends AuditLogCreateArgs>(args: SelectSubset<T, AuditLogCreateArgs<ExtArgs>>): Prisma__AuditLogClient<$Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many AuditLogs.
     * @param {AuditLogCreateManyArgs} args - Arguments to create many AuditLogs.
     * @example
     * // Create many AuditLogs
     * const auditLog = await prisma.auditLog.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends AuditLogCreateManyArgs>(args?: SelectSubset<T, AuditLogCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many AuditLogs and returns the data saved in the database.
     * @param {AuditLogCreateManyAndReturnArgs} args - Arguments to create many AuditLogs.
     * @example
     * // Create many AuditLogs
     * const auditLog = await prisma.auditLog.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many AuditLogs and only return the `id`
     * const auditLogWithIdOnly = await prisma.auditLog.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends AuditLogCreateManyAndReturnArgs>(args?: SelectSubset<T, AuditLogCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a AuditLog.
     * @param {AuditLogDeleteArgs} args - Arguments to delete one AuditLog.
     * @example
     * // Delete one AuditLog
     * const AuditLog = await prisma.auditLog.delete({
     *   where: {
     *     // ... filter to delete one AuditLog
     *   }
     * })
     * 
     */
    delete<T extends AuditLogDeleteArgs>(args: SelectSubset<T, AuditLogDeleteArgs<ExtArgs>>): Prisma__AuditLogClient<$Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one AuditLog.
     * @param {AuditLogUpdateArgs} args - Arguments to update one AuditLog.
     * @example
     * // Update one AuditLog
     * const auditLog = await prisma.auditLog.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends AuditLogUpdateArgs>(args: SelectSubset<T, AuditLogUpdateArgs<ExtArgs>>): Prisma__AuditLogClient<$Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more AuditLogs.
     * @param {AuditLogDeleteManyArgs} args - Arguments to filter AuditLogs to delete.
     * @example
     * // Delete a few AuditLogs
     * const { count } = await prisma.auditLog.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends AuditLogDeleteManyArgs>(args?: SelectSubset<T, AuditLogDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more AuditLogs.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AuditLogUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many AuditLogs
     * const auditLog = await prisma.auditLog.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends AuditLogUpdateManyArgs>(args: SelectSubset<T, AuditLogUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more AuditLogs and returns the data updated in the database.
     * @param {AuditLogUpdateManyAndReturnArgs} args - Arguments to update many AuditLogs.
     * @example
     * // Update many AuditLogs
     * const auditLog = await prisma.auditLog.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more AuditLogs and only return the `id`
     * const auditLogWithIdOnly = await prisma.auditLog.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends AuditLogUpdateManyAndReturnArgs>(args: SelectSubset<T, AuditLogUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one AuditLog.
     * @param {AuditLogUpsertArgs} args - Arguments to update or create a AuditLog.
     * @example
     * // Update or create a AuditLog
     * const auditLog = await prisma.auditLog.upsert({
     *   create: {
     *     // ... data to create a AuditLog
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the AuditLog we want to update
     *   }
     * })
     */
    upsert<T extends AuditLogUpsertArgs>(args: SelectSubset<T, AuditLogUpsertArgs<ExtArgs>>): Prisma__AuditLogClient<$Result.GetResult<Prisma.$AuditLogPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of AuditLogs.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AuditLogCountArgs} args - Arguments to filter AuditLogs to count.
     * @example
     * // Count the number of AuditLogs
     * const count = await prisma.auditLog.count({
     *   where: {
     *     // ... the filter for the AuditLogs we want to count
     *   }
     * })
    **/
    count<T extends AuditLogCountArgs>(
      args?: Subset<T, AuditLogCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], AuditLogCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a AuditLog.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AuditLogAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends AuditLogAggregateArgs>(args: Subset<T, AuditLogAggregateArgs>): Prisma.PrismaPromise<GetAuditLogAggregateType<T>>

    /**
     * Group by AuditLog.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AuditLogGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends AuditLogGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: AuditLogGroupByArgs['orderBy'] }
        : { orderBy?: AuditLogGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, AuditLogGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetAuditLogGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the AuditLog model
   */
  readonly fields: AuditLogFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for AuditLog.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__AuditLogClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the AuditLog model
   */
  interface AuditLogFieldRefs {
    readonly id: FieldRef<"AuditLog", 'String'>
    readonly userId: FieldRef<"AuditLog", 'String'>
    readonly action: FieldRef<"AuditLog", 'String'>
    readonly resourceType: FieldRef<"AuditLog", 'String'>
    readonly resourceId: FieldRef<"AuditLog", 'String'>
    readonly details: FieldRef<"AuditLog", 'Json'>
    readonly ipAddress: FieldRef<"AuditLog", 'String'>
    readonly userAgent: FieldRef<"AuditLog", 'String'>
    readonly timestamp: FieldRef<"AuditLog", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * AuditLog findUnique
   */
  export type AuditLogFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AuditLog
     */
    select?: AuditLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AuditLog
     */
    omit?: AuditLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuditLogInclude<ExtArgs> | null
    /**
     * Filter, which AuditLog to fetch.
     */
    where: AuditLogWhereUniqueInput
  }

  /**
   * AuditLog findUniqueOrThrow
   */
  export type AuditLogFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AuditLog
     */
    select?: AuditLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AuditLog
     */
    omit?: AuditLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuditLogInclude<ExtArgs> | null
    /**
     * Filter, which AuditLog to fetch.
     */
    where: AuditLogWhereUniqueInput
  }

  /**
   * AuditLog findFirst
   */
  export type AuditLogFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AuditLog
     */
    select?: AuditLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AuditLog
     */
    omit?: AuditLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuditLogInclude<ExtArgs> | null
    /**
     * Filter, which AuditLog to fetch.
     */
    where?: AuditLogWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AuditLogs to fetch.
     */
    orderBy?: AuditLogOrderByWithRelationInput | AuditLogOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for AuditLogs.
     */
    cursor?: AuditLogWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AuditLogs from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AuditLogs.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of AuditLogs.
     */
    distinct?: AuditLogScalarFieldEnum | AuditLogScalarFieldEnum[]
  }

  /**
   * AuditLog findFirstOrThrow
   */
  export type AuditLogFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AuditLog
     */
    select?: AuditLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AuditLog
     */
    omit?: AuditLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuditLogInclude<ExtArgs> | null
    /**
     * Filter, which AuditLog to fetch.
     */
    where?: AuditLogWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AuditLogs to fetch.
     */
    orderBy?: AuditLogOrderByWithRelationInput | AuditLogOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for AuditLogs.
     */
    cursor?: AuditLogWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AuditLogs from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AuditLogs.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of AuditLogs.
     */
    distinct?: AuditLogScalarFieldEnum | AuditLogScalarFieldEnum[]
  }

  /**
   * AuditLog findMany
   */
  export type AuditLogFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AuditLog
     */
    select?: AuditLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AuditLog
     */
    omit?: AuditLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuditLogInclude<ExtArgs> | null
    /**
     * Filter, which AuditLogs to fetch.
     */
    where?: AuditLogWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AuditLogs to fetch.
     */
    orderBy?: AuditLogOrderByWithRelationInput | AuditLogOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing AuditLogs.
     */
    cursor?: AuditLogWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AuditLogs from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AuditLogs.
     */
    skip?: number
    distinct?: AuditLogScalarFieldEnum | AuditLogScalarFieldEnum[]
  }

  /**
   * AuditLog create
   */
  export type AuditLogCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AuditLog
     */
    select?: AuditLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AuditLog
     */
    omit?: AuditLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuditLogInclude<ExtArgs> | null
    /**
     * The data needed to create a AuditLog.
     */
    data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>
  }

  /**
   * AuditLog createMany
   */
  export type AuditLogCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many AuditLogs.
     */
    data: AuditLogCreateManyInput | AuditLogCreateManyInput[]
  }

  /**
   * AuditLog createManyAndReturn
   */
  export type AuditLogCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AuditLog
     */
    select?: AuditLogSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the AuditLog
     */
    omit?: AuditLogOmit<ExtArgs> | null
    /**
     * The data used to create many AuditLogs.
     */
    data: AuditLogCreateManyInput | AuditLogCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuditLogIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * AuditLog update
   */
  export type AuditLogUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AuditLog
     */
    select?: AuditLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AuditLog
     */
    omit?: AuditLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuditLogInclude<ExtArgs> | null
    /**
     * The data needed to update a AuditLog.
     */
    data: XOR<AuditLogUpdateInput, AuditLogUncheckedUpdateInput>
    /**
     * Choose, which AuditLog to update.
     */
    where: AuditLogWhereUniqueInput
  }

  /**
   * AuditLog updateMany
   */
  export type AuditLogUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update AuditLogs.
     */
    data: XOR<AuditLogUpdateManyMutationInput, AuditLogUncheckedUpdateManyInput>
    /**
     * Filter which AuditLogs to update
     */
    where?: AuditLogWhereInput
    /**
     * Limit how many AuditLogs to update.
     */
    limit?: number
  }

  /**
   * AuditLog updateManyAndReturn
   */
  export type AuditLogUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AuditLog
     */
    select?: AuditLogSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the AuditLog
     */
    omit?: AuditLogOmit<ExtArgs> | null
    /**
     * The data used to update AuditLogs.
     */
    data: XOR<AuditLogUpdateManyMutationInput, AuditLogUncheckedUpdateManyInput>
    /**
     * Filter which AuditLogs to update
     */
    where?: AuditLogWhereInput
    /**
     * Limit how many AuditLogs to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuditLogIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * AuditLog upsert
   */
  export type AuditLogUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AuditLog
     */
    select?: AuditLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AuditLog
     */
    omit?: AuditLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuditLogInclude<ExtArgs> | null
    /**
     * The filter to search for the AuditLog to update in case it exists.
     */
    where: AuditLogWhereUniqueInput
    /**
     * In case the AuditLog found by the `where` argument doesn't exist, create a new AuditLog with this data.
     */
    create: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>
    /**
     * In case the AuditLog was found with the provided `where` argument, update it with this data.
     */
    update: XOR<AuditLogUpdateInput, AuditLogUncheckedUpdateInput>
  }

  /**
   * AuditLog delete
   */
  export type AuditLogDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AuditLog
     */
    select?: AuditLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AuditLog
     */
    omit?: AuditLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuditLogInclude<ExtArgs> | null
    /**
     * Filter which AuditLog to delete.
     */
    where: AuditLogWhereUniqueInput
  }

  /**
   * AuditLog deleteMany
   */
  export type AuditLogDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which AuditLogs to delete
     */
    where?: AuditLogWhereInput
    /**
     * Limit how many AuditLogs to delete.
     */
    limit?: number
  }

  /**
   * AuditLog without action
   */
  export type AuditLogDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AuditLog
     */
    select?: AuditLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AuditLog
     */
    omit?: AuditLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AuditLogInclude<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const UserScalarFieldEnum: {
    id: 'id',
    email: 'email',
    name: 'name',
    role: 'role',
    passwordHash: 'passwordHash',
    isActive: 'isActive',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type UserScalarFieldEnum = (typeof UserScalarFieldEnum)[keyof typeof UserScalarFieldEnum]


  export const ClientScalarFieldEnum: {
    id: 'id',
    firstName: 'firstName',
    lastName: 'lastName',
    dateOfBirth: 'dateOfBirth',
    ehrId: 'ehrId',
    isActive: 'isActive',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type ClientScalarFieldEnum = (typeof ClientScalarFieldEnum)[keyof typeof ClientScalarFieldEnum]


  export const SessionNoteScalarFieldEnum: {
    id: 'id',
    clientId: 'clientId',
    therapistId: 'therapistId',
    sessionDate: 'sessionDate',
    noteType: 'noteType',
    subjective: 'subjective',
    objective: 'objective',
    assessment: 'assessment',
    plan: 'plan',
    sessionDuration: 'sessionDuration',
    interventions: 'interventions',
    clientMood: 'clientMood',
    isFinalized: 'isFinalized',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type SessionNoteScalarFieldEnum = (typeof SessionNoteScalarFieldEnum)[keyof typeof SessionNoteScalarFieldEnum]


  export const MultimediaFileScalarFieldEnum: {
    id: 'id',
    sessionNoteId: 'sessionNoteId',
    fileName: 'fileName',
    originalName: 'originalName',
    fileType: 'fileType',
    fileSize: 'fileSize',
    s3Key: 's3Key',
    s3Bucket: 's3Bucket',
    description: 'description',
    aiDescription: 'aiDescription',
    uploadedAt: 'uploadedAt'
  };

  export type MultimediaFileScalarFieldEnum = (typeof MultimediaFileScalarFieldEnum)[keyof typeof MultimediaFileScalarFieldEnum]


  export const AiSuggestionScalarFieldEnum: {
    id: 'id',
    sessionNoteId: 'sessionNoteId',
    multimediaId: 'multimediaId',
    suggestionType: 'suggestionType',
    originalText: 'originalText',
    suggestedText: 'suggestedText',
    isAccepted: 'isAccepted',
    isRejected: 'isRejected',
    createdAt: 'createdAt'
  };

  export type AiSuggestionScalarFieldEnum = (typeof AiSuggestionScalarFieldEnum)[keyof typeof AiSuggestionScalarFieldEnum]


  export const AuditLogScalarFieldEnum: {
    id: 'id',
    userId: 'userId',
    action: 'action',
    resourceType: 'resourceType',
    resourceId: 'resourceId',
    details: 'details',
    ipAddress: 'ipAddress',
    userAgent: 'userAgent',
    timestamp: 'timestamp'
  };

  export type AuditLogScalarFieldEnum = (typeof AuditLogScalarFieldEnum)[keyof typeof AuditLogScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const NullableJsonNullValueInput: {
    DbNull: typeof DbNull,
    JsonNull: typeof JsonNull
  };

  export type NullableJsonNullValueInput = (typeof NullableJsonNullValueInput)[keyof typeof NullableJsonNullValueInput]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  export const JsonNullValueFilter: {
    DbNull: typeof DbNull,
    JsonNull: typeof JsonNull,
    AnyNull: typeof AnyNull
  };

  export type JsonNullValueFilter = (typeof JsonNullValueFilter)[keyof typeof JsonNullValueFilter]


  export const QueryMode: {
    default: 'default',
    insensitive: 'insensitive'
  };

  export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


  /**
   * Field references
   */


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'UserRole'
   */
  export type EnumUserRoleFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'UserRole'>
    


  /**
   * Reference to a field of type 'Boolean'
   */
  export type BooleanFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Boolean'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'NoteType'
   */
  export type EnumNoteTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'NoteType'>
    


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'MediaType'
   */
  export type EnumMediaTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'MediaType'>
    


  /**
   * Reference to a field of type 'SuggestionType'
   */
  export type EnumSuggestionTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'SuggestionType'>
    


  /**
   * Reference to a field of type 'Json'
   */
  export type JsonFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Json'>
    


  /**
   * Reference to a field of type 'QueryMode'
   */
  export type EnumQueryModeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'QueryMode'>
    


  /**
   * Reference to a field of type 'Float'
   */
  export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    
  /**
   * Deep Input Types
   */


  export type UserWhereInput = {
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    id?: StringFilter<"User"> | string
    email?: StringFilter<"User"> | string
    name?: StringNullableFilter<"User"> | string | null
    role?: EnumUserRoleFilter<"User"> | $Enums.UserRole
    passwordHash?: StringFilter<"User"> | string
    isActive?: BoolFilter<"User"> | boolean
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    sessionNotes?: SessionNoteListRelationFilter
    auditLogs?: AuditLogListRelationFilter
  }

  export type UserOrderByWithRelationInput = {
    id?: SortOrder
    email?: SortOrder
    name?: SortOrderInput | SortOrder
    role?: SortOrder
    passwordHash?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    sessionNotes?: SessionNoteOrderByRelationAggregateInput
    auditLogs?: AuditLogOrderByRelationAggregateInput
  }

  export type UserWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    email?: string
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    name?: StringNullableFilter<"User"> | string | null
    role?: EnumUserRoleFilter<"User"> | $Enums.UserRole
    passwordHash?: StringFilter<"User"> | string
    isActive?: BoolFilter<"User"> | boolean
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    sessionNotes?: SessionNoteListRelationFilter
    auditLogs?: AuditLogListRelationFilter
  }, "id" | "email">

  export type UserOrderByWithAggregationInput = {
    id?: SortOrder
    email?: SortOrder
    name?: SortOrderInput | SortOrder
    role?: SortOrder
    passwordHash?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: UserCountOrderByAggregateInput
    _max?: UserMaxOrderByAggregateInput
    _min?: UserMinOrderByAggregateInput
  }

  export type UserScalarWhereWithAggregatesInput = {
    AND?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    OR?: UserScalarWhereWithAggregatesInput[]
    NOT?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"User"> | string
    email?: StringWithAggregatesFilter<"User"> | string
    name?: StringNullableWithAggregatesFilter<"User"> | string | null
    role?: EnumUserRoleWithAggregatesFilter<"User"> | $Enums.UserRole
    passwordHash?: StringWithAggregatesFilter<"User"> | string
    isActive?: BoolWithAggregatesFilter<"User"> | boolean
    createdAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
  }

  export type ClientWhereInput = {
    AND?: ClientWhereInput | ClientWhereInput[]
    OR?: ClientWhereInput[]
    NOT?: ClientWhereInput | ClientWhereInput[]
    id?: StringFilter<"Client"> | string
    firstName?: StringFilter<"Client"> | string
    lastName?: StringFilter<"Client"> | string
    dateOfBirth?: DateTimeNullableFilter<"Client"> | Date | string | null
    ehrId?: StringNullableFilter<"Client"> | string | null
    isActive?: BoolFilter<"Client"> | boolean
    createdAt?: DateTimeFilter<"Client"> | Date | string
    updatedAt?: DateTimeFilter<"Client"> | Date | string
    sessionNotes?: SessionNoteListRelationFilter
  }

  export type ClientOrderByWithRelationInput = {
    id?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    dateOfBirth?: SortOrderInput | SortOrder
    ehrId?: SortOrderInput | SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    sessionNotes?: SessionNoteOrderByRelationAggregateInput
  }

  export type ClientWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    ehrId?: string
    AND?: ClientWhereInput | ClientWhereInput[]
    OR?: ClientWhereInput[]
    NOT?: ClientWhereInput | ClientWhereInput[]
    firstName?: StringFilter<"Client"> | string
    lastName?: StringFilter<"Client"> | string
    dateOfBirth?: DateTimeNullableFilter<"Client"> | Date | string | null
    isActive?: BoolFilter<"Client"> | boolean
    createdAt?: DateTimeFilter<"Client"> | Date | string
    updatedAt?: DateTimeFilter<"Client"> | Date | string
    sessionNotes?: SessionNoteListRelationFilter
  }, "id" | "ehrId">

  export type ClientOrderByWithAggregationInput = {
    id?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    dateOfBirth?: SortOrderInput | SortOrder
    ehrId?: SortOrderInput | SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: ClientCountOrderByAggregateInput
    _max?: ClientMaxOrderByAggregateInput
    _min?: ClientMinOrderByAggregateInput
  }

  export type ClientScalarWhereWithAggregatesInput = {
    AND?: ClientScalarWhereWithAggregatesInput | ClientScalarWhereWithAggregatesInput[]
    OR?: ClientScalarWhereWithAggregatesInput[]
    NOT?: ClientScalarWhereWithAggregatesInput | ClientScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"Client"> | string
    firstName?: StringWithAggregatesFilter<"Client"> | string
    lastName?: StringWithAggregatesFilter<"Client"> | string
    dateOfBirth?: DateTimeNullableWithAggregatesFilter<"Client"> | Date | string | null
    ehrId?: StringNullableWithAggregatesFilter<"Client"> | string | null
    isActive?: BoolWithAggregatesFilter<"Client"> | boolean
    createdAt?: DateTimeWithAggregatesFilter<"Client"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"Client"> | Date | string
  }

  export type SessionNoteWhereInput = {
    AND?: SessionNoteWhereInput | SessionNoteWhereInput[]
    OR?: SessionNoteWhereInput[]
    NOT?: SessionNoteWhereInput | SessionNoteWhereInput[]
    id?: StringFilter<"SessionNote"> | string
    clientId?: StringFilter<"SessionNote"> | string
    therapistId?: StringFilter<"SessionNote"> | string
    sessionDate?: DateTimeFilter<"SessionNote"> | Date | string
    noteType?: EnumNoteTypeFilter<"SessionNote"> | $Enums.NoteType
    subjective?: StringNullableFilter<"SessionNote"> | string | null
    objective?: StringNullableFilter<"SessionNote"> | string | null
    assessment?: StringNullableFilter<"SessionNote"> | string | null
    plan?: StringNullableFilter<"SessionNote"> | string | null
    sessionDuration?: IntNullableFilter<"SessionNote"> | number | null
    interventions?: StringNullableFilter<"SessionNote"> | string | null
    clientMood?: StringNullableFilter<"SessionNote"> | string | null
    isFinalized?: BoolFilter<"SessionNote"> | boolean
    createdAt?: DateTimeFilter<"SessionNote"> | Date | string
    updatedAt?: DateTimeFilter<"SessionNote"> | Date | string
    client?: XOR<ClientScalarRelationFilter, ClientWhereInput>
    therapist?: XOR<UserScalarRelationFilter, UserWhereInput>
    multimedia?: MultimediaFileListRelationFilter
    aiSuggestions?: AiSuggestionListRelationFilter
  }

  export type SessionNoteOrderByWithRelationInput = {
    id?: SortOrder
    clientId?: SortOrder
    therapistId?: SortOrder
    sessionDate?: SortOrder
    noteType?: SortOrder
    subjective?: SortOrderInput | SortOrder
    objective?: SortOrderInput | SortOrder
    assessment?: SortOrderInput | SortOrder
    plan?: SortOrderInput | SortOrder
    sessionDuration?: SortOrderInput | SortOrder
    interventions?: SortOrderInput | SortOrder
    clientMood?: SortOrderInput | SortOrder
    isFinalized?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    client?: ClientOrderByWithRelationInput
    therapist?: UserOrderByWithRelationInput
    multimedia?: MultimediaFileOrderByRelationAggregateInput
    aiSuggestions?: AiSuggestionOrderByRelationAggregateInput
  }

  export type SessionNoteWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: SessionNoteWhereInput | SessionNoteWhereInput[]
    OR?: SessionNoteWhereInput[]
    NOT?: SessionNoteWhereInput | SessionNoteWhereInput[]
    clientId?: StringFilter<"SessionNote"> | string
    therapistId?: StringFilter<"SessionNote"> | string
    sessionDate?: DateTimeFilter<"SessionNote"> | Date | string
    noteType?: EnumNoteTypeFilter<"SessionNote"> | $Enums.NoteType
    subjective?: StringNullableFilter<"SessionNote"> | string | null
    objective?: StringNullableFilter<"SessionNote"> | string | null
    assessment?: StringNullableFilter<"SessionNote"> | string | null
    plan?: StringNullableFilter<"SessionNote"> | string | null
    sessionDuration?: IntNullableFilter<"SessionNote"> | number | null
    interventions?: StringNullableFilter<"SessionNote"> | string | null
    clientMood?: StringNullableFilter<"SessionNote"> | string | null
    isFinalized?: BoolFilter<"SessionNote"> | boolean
    createdAt?: DateTimeFilter<"SessionNote"> | Date | string
    updatedAt?: DateTimeFilter<"SessionNote"> | Date | string
    client?: XOR<ClientScalarRelationFilter, ClientWhereInput>
    therapist?: XOR<UserScalarRelationFilter, UserWhereInput>
    multimedia?: MultimediaFileListRelationFilter
    aiSuggestions?: AiSuggestionListRelationFilter
  }, "id">

  export type SessionNoteOrderByWithAggregationInput = {
    id?: SortOrder
    clientId?: SortOrder
    therapistId?: SortOrder
    sessionDate?: SortOrder
    noteType?: SortOrder
    subjective?: SortOrderInput | SortOrder
    objective?: SortOrderInput | SortOrder
    assessment?: SortOrderInput | SortOrder
    plan?: SortOrderInput | SortOrder
    sessionDuration?: SortOrderInput | SortOrder
    interventions?: SortOrderInput | SortOrder
    clientMood?: SortOrderInput | SortOrder
    isFinalized?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: SessionNoteCountOrderByAggregateInput
    _avg?: SessionNoteAvgOrderByAggregateInput
    _max?: SessionNoteMaxOrderByAggregateInput
    _min?: SessionNoteMinOrderByAggregateInput
    _sum?: SessionNoteSumOrderByAggregateInput
  }

  export type SessionNoteScalarWhereWithAggregatesInput = {
    AND?: SessionNoteScalarWhereWithAggregatesInput | SessionNoteScalarWhereWithAggregatesInput[]
    OR?: SessionNoteScalarWhereWithAggregatesInput[]
    NOT?: SessionNoteScalarWhereWithAggregatesInput | SessionNoteScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"SessionNote"> | string
    clientId?: StringWithAggregatesFilter<"SessionNote"> | string
    therapistId?: StringWithAggregatesFilter<"SessionNote"> | string
    sessionDate?: DateTimeWithAggregatesFilter<"SessionNote"> | Date | string
    noteType?: EnumNoteTypeWithAggregatesFilter<"SessionNote"> | $Enums.NoteType
    subjective?: StringNullableWithAggregatesFilter<"SessionNote"> | string | null
    objective?: StringNullableWithAggregatesFilter<"SessionNote"> | string | null
    assessment?: StringNullableWithAggregatesFilter<"SessionNote"> | string | null
    plan?: StringNullableWithAggregatesFilter<"SessionNote"> | string | null
    sessionDuration?: IntNullableWithAggregatesFilter<"SessionNote"> | number | null
    interventions?: StringNullableWithAggregatesFilter<"SessionNote"> | string | null
    clientMood?: StringNullableWithAggregatesFilter<"SessionNote"> | string | null
    isFinalized?: BoolWithAggregatesFilter<"SessionNote"> | boolean
    createdAt?: DateTimeWithAggregatesFilter<"SessionNote"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"SessionNote"> | Date | string
  }

  export type MultimediaFileWhereInput = {
    AND?: MultimediaFileWhereInput | MultimediaFileWhereInput[]
    OR?: MultimediaFileWhereInput[]
    NOT?: MultimediaFileWhereInput | MultimediaFileWhereInput[]
    id?: StringFilter<"MultimediaFile"> | string
    sessionNoteId?: StringFilter<"MultimediaFile"> | string
    fileName?: StringFilter<"MultimediaFile"> | string
    originalName?: StringFilter<"MultimediaFile"> | string
    fileType?: EnumMediaTypeFilter<"MultimediaFile"> | $Enums.MediaType
    fileSize?: IntFilter<"MultimediaFile"> | number
    s3Key?: StringFilter<"MultimediaFile"> | string
    s3Bucket?: StringFilter<"MultimediaFile"> | string
    description?: StringNullableFilter<"MultimediaFile"> | string | null
    aiDescription?: StringNullableFilter<"MultimediaFile"> | string | null
    uploadedAt?: DateTimeFilter<"MultimediaFile"> | Date | string
    sessionNote?: XOR<SessionNoteScalarRelationFilter, SessionNoteWhereInput>
  }

  export type MultimediaFileOrderByWithRelationInput = {
    id?: SortOrder
    sessionNoteId?: SortOrder
    fileName?: SortOrder
    originalName?: SortOrder
    fileType?: SortOrder
    fileSize?: SortOrder
    s3Key?: SortOrder
    s3Bucket?: SortOrder
    description?: SortOrderInput | SortOrder
    aiDescription?: SortOrderInput | SortOrder
    uploadedAt?: SortOrder
    sessionNote?: SessionNoteOrderByWithRelationInput
  }

  export type MultimediaFileWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: MultimediaFileWhereInput | MultimediaFileWhereInput[]
    OR?: MultimediaFileWhereInput[]
    NOT?: MultimediaFileWhereInput | MultimediaFileWhereInput[]
    sessionNoteId?: StringFilter<"MultimediaFile"> | string
    fileName?: StringFilter<"MultimediaFile"> | string
    originalName?: StringFilter<"MultimediaFile"> | string
    fileType?: EnumMediaTypeFilter<"MultimediaFile"> | $Enums.MediaType
    fileSize?: IntFilter<"MultimediaFile"> | number
    s3Key?: StringFilter<"MultimediaFile"> | string
    s3Bucket?: StringFilter<"MultimediaFile"> | string
    description?: StringNullableFilter<"MultimediaFile"> | string | null
    aiDescription?: StringNullableFilter<"MultimediaFile"> | string | null
    uploadedAt?: DateTimeFilter<"MultimediaFile"> | Date | string
    sessionNote?: XOR<SessionNoteScalarRelationFilter, SessionNoteWhereInput>
  }, "id">

  export type MultimediaFileOrderByWithAggregationInput = {
    id?: SortOrder
    sessionNoteId?: SortOrder
    fileName?: SortOrder
    originalName?: SortOrder
    fileType?: SortOrder
    fileSize?: SortOrder
    s3Key?: SortOrder
    s3Bucket?: SortOrder
    description?: SortOrderInput | SortOrder
    aiDescription?: SortOrderInput | SortOrder
    uploadedAt?: SortOrder
    _count?: MultimediaFileCountOrderByAggregateInput
    _avg?: MultimediaFileAvgOrderByAggregateInput
    _max?: MultimediaFileMaxOrderByAggregateInput
    _min?: MultimediaFileMinOrderByAggregateInput
    _sum?: MultimediaFileSumOrderByAggregateInput
  }

  export type MultimediaFileScalarWhereWithAggregatesInput = {
    AND?: MultimediaFileScalarWhereWithAggregatesInput | MultimediaFileScalarWhereWithAggregatesInput[]
    OR?: MultimediaFileScalarWhereWithAggregatesInput[]
    NOT?: MultimediaFileScalarWhereWithAggregatesInput | MultimediaFileScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"MultimediaFile"> | string
    sessionNoteId?: StringWithAggregatesFilter<"MultimediaFile"> | string
    fileName?: StringWithAggregatesFilter<"MultimediaFile"> | string
    originalName?: StringWithAggregatesFilter<"MultimediaFile"> | string
    fileType?: EnumMediaTypeWithAggregatesFilter<"MultimediaFile"> | $Enums.MediaType
    fileSize?: IntWithAggregatesFilter<"MultimediaFile"> | number
    s3Key?: StringWithAggregatesFilter<"MultimediaFile"> | string
    s3Bucket?: StringWithAggregatesFilter<"MultimediaFile"> | string
    description?: StringNullableWithAggregatesFilter<"MultimediaFile"> | string | null
    aiDescription?: StringNullableWithAggregatesFilter<"MultimediaFile"> | string | null
    uploadedAt?: DateTimeWithAggregatesFilter<"MultimediaFile"> | Date | string
  }

  export type AiSuggestionWhereInput = {
    AND?: AiSuggestionWhereInput | AiSuggestionWhereInput[]
    OR?: AiSuggestionWhereInput[]
    NOT?: AiSuggestionWhereInput | AiSuggestionWhereInput[]
    id?: StringFilter<"AiSuggestion"> | string
    sessionNoteId?: StringFilter<"AiSuggestion"> | string
    multimediaId?: StringNullableFilter<"AiSuggestion"> | string | null
    suggestionType?: EnumSuggestionTypeFilter<"AiSuggestion"> | $Enums.SuggestionType
    originalText?: StringNullableFilter<"AiSuggestion"> | string | null
    suggestedText?: StringFilter<"AiSuggestion"> | string
    isAccepted?: BoolFilter<"AiSuggestion"> | boolean
    isRejected?: BoolFilter<"AiSuggestion"> | boolean
    createdAt?: DateTimeFilter<"AiSuggestion"> | Date | string
    sessionNote?: XOR<SessionNoteScalarRelationFilter, SessionNoteWhereInput>
  }

  export type AiSuggestionOrderByWithRelationInput = {
    id?: SortOrder
    sessionNoteId?: SortOrder
    multimediaId?: SortOrderInput | SortOrder
    suggestionType?: SortOrder
    originalText?: SortOrderInput | SortOrder
    suggestedText?: SortOrder
    isAccepted?: SortOrder
    isRejected?: SortOrder
    createdAt?: SortOrder
    sessionNote?: SessionNoteOrderByWithRelationInput
  }

  export type AiSuggestionWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: AiSuggestionWhereInput | AiSuggestionWhereInput[]
    OR?: AiSuggestionWhereInput[]
    NOT?: AiSuggestionWhereInput | AiSuggestionWhereInput[]
    sessionNoteId?: StringFilter<"AiSuggestion"> | string
    multimediaId?: StringNullableFilter<"AiSuggestion"> | string | null
    suggestionType?: EnumSuggestionTypeFilter<"AiSuggestion"> | $Enums.SuggestionType
    originalText?: StringNullableFilter<"AiSuggestion"> | string | null
    suggestedText?: StringFilter<"AiSuggestion"> | string
    isAccepted?: BoolFilter<"AiSuggestion"> | boolean
    isRejected?: BoolFilter<"AiSuggestion"> | boolean
    createdAt?: DateTimeFilter<"AiSuggestion"> | Date | string
    sessionNote?: XOR<SessionNoteScalarRelationFilter, SessionNoteWhereInput>
  }, "id">

  export type AiSuggestionOrderByWithAggregationInput = {
    id?: SortOrder
    sessionNoteId?: SortOrder
    multimediaId?: SortOrderInput | SortOrder
    suggestionType?: SortOrder
    originalText?: SortOrderInput | SortOrder
    suggestedText?: SortOrder
    isAccepted?: SortOrder
    isRejected?: SortOrder
    createdAt?: SortOrder
    _count?: AiSuggestionCountOrderByAggregateInput
    _max?: AiSuggestionMaxOrderByAggregateInput
    _min?: AiSuggestionMinOrderByAggregateInput
  }

  export type AiSuggestionScalarWhereWithAggregatesInput = {
    AND?: AiSuggestionScalarWhereWithAggregatesInput | AiSuggestionScalarWhereWithAggregatesInput[]
    OR?: AiSuggestionScalarWhereWithAggregatesInput[]
    NOT?: AiSuggestionScalarWhereWithAggregatesInput | AiSuggestionScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"AiSuggestion"> | string
    sessionNoteId?: StringWithAggregatesFilter<"AiSuggestion"> | string
    multimediaId?: StringNullableWithAggregatesFilter<"AiSuggestion"> | string | null
    suggestionType?: EnumSuggestionTypeWithAggregatesFilter<"AiSuggestion"> | $Enums.SuggestionType
    originalText?: StringNullableWithAggregatesFilter<"AiSuggestion"> | string | null
    suggestedText?: StringWithAggregatesFilter<"AiSuggestion"> | string
    isAccepted?: BoolWithAggregatesFilter<"AiSuggestion"> | boolean
    isRejected?: BoolWithAggregatesFilter<"AiSuggestion"> | boolean
    createdAt?: DateTimeWithAggregatesFilter<"AiSuggestion"> | Date | string
  }

  export type AuditLogWhereInput = {
    AND?: AuditLogWhereInput | AuditLogWhereInput[]
    OR?: AuditLogWhereInput[]
    NOT?: AuditLogWhereInput | AuditLogWhereInput[]
    id?: StringFilter<"AuditLog"> | string
    userId?: StringFilter<"AuditLog"> | string
    action?: StringFilter<"AuditLog"> | string
    resourceType?: StringFilter<"AuditLog"> | string
    resourceId?: StringFilter<"AuditLog"> | string
    details?: JsonNullableFilter<"AuditLog">
    ipAddress?: StringNullableFilter<"AuditLog"> | string | null
    userAgent?: StringNullableFilter<"AuditLog"> | string | null
    timestamp?: DateTimeFilter<"AuditLog"> | Date | string
    user?: XOR<UserScalarRelationFilter, UserWhereInput>
  }

  export type AuditLogOrderByWithRelationInput = {
    id?: SortOrder
    userId?: SortOrder
    action?: SortOrder
    resourceType?: SortOrder
    resourceId?: SortOrder
    details?: SortOrderInput | SortOrder
    ipAddress?: SortOrderInput | SortOrder
    userAgent?: SortOrderInput | SortOrder
    timestamp?: SortOrder
    user?: UserOrderByWithRelationInput
  }

  export type AuditLogWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: AuditLogWhereInput | AuditLogWhereInput[]
    OR?: AuditLogWhereInput[]
    NOT?: AuditLogWhereInput | AuditLogWhereInput[]
    userId?: StringFilter<"AuditLog"> | string
    action?: StringFilter<"AuditLog"> | string
    resourceType?: StringFilter<"AuditLog"> | string
    resourceId?: StringFilter<"AuditLog"> | string
    details?: JsonNullableFilter<"AuditLog">
    ipAddress?: StringNullableFilter<"AuditLog"> | string | null
    userAgent?: StringNullableFilter<"AuditLog"> | string | null
    timestamp?: DateTimeFilter<"AuditLog"> | Date | string
    user?: XOR<UserScalarRelationFilter, UserWhereInput>
  }, "id">

  export type AuditLogOrderByWithAggregationInput = {
    id?: SortOrder
    userId?: SortOrder
    action?: SortOrder
    resourceType?: SortOrder
    resourceId?: SortOrder
    details?: SortOrderInput | SortOrder
    ipAddress?: SortOrderInput | SortOrder
    userAgent?: SortOrderInput | SortOrder
    timestamp?: SortOrder
    _count?: AuditLogCountOrderByAggregateInput
    _max?: AuditLogMaxOrderByAggregateInput
    _min?: AuditLogMinOrderByAggregateInput
  }

  export type AuditLogScalarWhereWithAggregatesInput = {
    AND?: AuditLogScalarWhereWithAggregatesInput | AuditLogScalarWhereWithAggregatesInput[]
    OR?: AuditLogScalarWhereWithAggregatesInput[]
    NOT?: AuditLogScalarWhereWithAggregatesInput | AuditLogScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"AuditLog"> | string
    userId?: StringWithAggregatesFilter<"AuditLog"> | string
    action?: StringWithAggregatesFilter<"AuditLog"> | string
    resourceType?: StringWithAggregatesFilter<"AuditLog"> | string
    resourceId?: StringWithAggregatesFilter<"AuditLog"> | string
    details?: JsonNullableWithAggregatesFilter<"AuditLog">
    ipAddress?: StringNullableWithAggregatesFilter<"AuditLog"> | string | null
    userAgent?: StringNullableWithAggregatesFilter<"AuditLog"> | string | null
    timestamp?: DateTimeWithAggregatesFilter<"AuditLog"> | Date | string
  }

  export type UserCreateInput = {
    id?: string
    email: string
    name?: string | null
    role?: $Enums.UserRole
    passwordHash: string
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    sessionNotes?: SessionNoteCreateNestedManyWithoutTherapistInput
    auditLogs?: AuditLogCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateInput = {
    id?: string
    email: string
    name?: string | null
    role?: $Enums.UserRole
    passwordHash: string
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    sessionNotes?: SessionNoteUncheckedCreateNestedManyWithoutTherapistInput
    auditLogs?: AuditLogUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    passwordHash?: StringFieldUpdateOperationsInput | string
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    sessionNotes?: SessionNoteUpdateManyWithoutTherapistNestedInput
    auditLogs?: AuditLogUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    passwordHash?: StringFieldUpdateOperationsInput | string
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    sessionNotes?: SessionNoteUncheckedUpdateManyWithoutTherapistNestedInput
    auditLogs?: AuditLogUncheckedUpdateManyWithoutUserNestedInput
  }

  export type UserCreateManyInput = {
    id?: string
    email: string
    name?: string | null
    role?: $Enums.UserRole
    passwordHash: string
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    passwordHash?: StringFieldUpdateOperationsInput | string
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    passwordHash?: StringFieldUpdateOperationsInput | string
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ClientCreateInput = {
    id?: string
    firstName: string
    lastName: string
    dateOfBirth?: Date | string | null
    ehrId?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    sessionNotes?: SessionNoteCreateNestedManyWithoutClientInput
  }

  export type ClientUncheckedCreateInput = {
    id?: string
    firstName: string
    lastName: string
    dateOfBirth?: Date | string | null
    ehrId?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    sessionNotes?: SessionNoteUncheckedCreateNestedManyWithoutClientInput
  }

  export type ClientUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    dateOfBirth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    ehrId?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    sessionNotes?: SessionNoteUpdateManyWithoutClientNestedInput
  }

  export type ClientUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    dateOfBirth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    ehrId?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    sessionNotes?: SessionNoteUncheckedUpdateManyWithoutClientNestedInput
  }

  export type ClientCreateManyInput = {
    id?: string
    firstName: string
    lastName: string
    dateOfBirth?: Date | string | null
    ehrId?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type ClientUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    dateOfBirth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    ehrId?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ClientUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    dateOfBirth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    ehrId?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type SessionNoteCreateInput = {
    id?: string
    sessionDate: Date | string
    noteType?: $Enums.NoteType
    subjective?: string | null
    objective?: string | null
    assessment?: string | null
    plan?: string | null
    sessionDuration?: number | null
    interventions?: string | null
    clientMood?: string | null
    isFinalized?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    client: ClientCreateNestedOneWithoutSessionNotesInput
    therapist: UserCreateNestedOneWithoutSessionNotesInput
    multimedia?: MultimediaFileCreateNestedManyWithoutSessionNoteInput
    aiSuggestions?: AiSuggestionCreateNestedManyWithoutSessionNoteInput
  }

  export type SessionNoteUncheckedCreateInput = {
    id?: string
    clientId: string
    therapistId: string
    sessionDate: Date | string
    noteType?: $Enums.NoteType
    subjective?: string | null
    objective?: string | null
    assessment?: string | null
    plan?: string | null
    sessionDuration?: number | null
    interventions?: string | null
    clientMood?: string | null
    isFinalized?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    multimedia?: MultimediaFileUncheckedCreateNestedManyWithoutSessionNoteInput
    aiSuggestions?: AiSuggestionUncheckedCreateNestedManyWithoutSessionNoteInput
  }

  export type SessionNoteUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    sessionDate?: DateTimeFieldUpdateOperationsInput | Date | string
    noteType?: EnumNoteTypeFieldUpdateOperationsInput | $Enums.NoteType
    subjective?: NullableStringFieldUpdateOperationsInput | string | null
    objective?: NullableStringFieldUpdateOperationsInput | string | null
    assessment?: NullableStringFieldUpdateOperationsInput | string | null
    plan?: NullableStringFieldUpdateOperationsInput | string | null
    sessionDuration?: NullableIntFieldUpdateOperationsInput | number | null
    interventions?: NullableStringFieldUpdateOperationsInput | string | null
    clientMood?: NullableStringFieldUpdateOperationsInput | string | null
    isFinalized?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    client?: ClientUpdateOneRequiredWithoutSessionNotesNestedInput
    therapist?: UserUpdateOneRequiredWithoutSessionNotesNestedInput
    multimedia?: MultimediaFileUpdateManyWithoutSessionNoteNestedInput
    aiSuggestions?: AiSuggestionUpdateManyWithoutSessionNoteNestedInput
  }

  export type SessionNoteUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    clientId?: StringFieldUpdateOperationsInput | string
    therapistId?: StringFieldUpdateOperationsInput | string
    sessionDate?: DateTimeFieldUpdateOperationsInput | Date | string
    noteType?: EnumNoteTypeFieldUpdateOperationsInput | $Enums.NoteType
    subjective?: NullableStringFieldUpdateOperationsInput | string | null
    objective?: NullableStringFieldUpdateOperationsInput | string | null
    assessment?: NullableStringFieldUpdateOperationsInput | string | null
    plan?: NullableStringFieldUpdateOperationsInput | string | null
    sessionDuration?: NullableIntFieldUpdateOperationsInput | number | null
    interventions?: NullableStringFieldUpdateOperationsInput | string | null
    clientMood?: NullableStringFieldUpdateOperationsInput | string | null
    isFinalized?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    multimedia?: MultimediaFileUncheckedUpdateManyWithoutSessionNoteNestedInput
    aiSuggestions?: AiSuggestionUncheckedUpdateManyWithoutSessionNoteNestedInput
  }

  export type SessionNoteCreateManyInput = {
    id?: string
    clientId: string
    therapistId: string
    sessionDate: Date | string
    noteType?: $Enums.NoteType
    subjective?: string | null
    objective?: string | null
    assessment?: string | null
    plan?: string | null
    sessionDuration?: number | null
    interventions?: string | null
    clientMood?: string | null
    isFinalized?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type SessionNoteUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    sessionDate?: DateTimeFieldUpdateOperationsInput | Date | string
    noteType?: EnumNoteTypeFieldUpdateOperationsInput | $Enums.NoteType
    subjective?: NullableStringFieldUpdateOperationsInput | string | null
    objective?: NullableStringFieldUpdateOperationsInput | string | null
    assessment?: NullableStringFieldUpdateOperationsInput | string | null
    plan?: NullableStringFieldUpdateOperationsInput | string | null
    sessionDuration?: NullableIntFieldUpdateOperationsInput | number | null
    interventions?: NullableStringFieldUpdateOperationsInput | string | null
    clientMood?: NullableStringFieldUpdateOperationsInput | string | null
    isFinalized?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type SessionNoteUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    clientId?: StringFieldUpdateOperationsInput | string
    therapistId?: StringFieldUpdateOperationsInput | string
    sessionDate?: DateTimeFieldUpdateOperationsInput | Date | string
    noteType?: EnumNoteTypeFieldUpdateOperationsInput | $Enums.NoteType
    subjective?: NullableStringFieldUpdateOperationsInput | string | null
    objective?: NullableStringFieldUpdateOperationsInput | string | null
    assessment?: NullableStringFieldUpdateOperationsInput | string | null
    plan?: NullableStringFieldUpdateOperationsInput | string | null
    sessionDuration?: NullableIntFieldUpdateOperationsInput | number | null
    interventions?: NullableStringFieldUpdateOperationsInput | string | null
    clientMood?: NullableStringFieldUpdateOperationsInput | string | null
    isFinalized?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type MultimediaFileCreateInput = {
    id?: string
    fileName: string
    originalName: string
    fileType: $Enums.MediaType
    fileSize: number
    s3Key: string
    s3Bucket: string
    description?: string | null
    aiDescription?: string | null
    uploadedAt?: Date | string
    sessionNote: SessionNoteCreateNestedOneWithoutMultimediaInput
  }

  export type MultimediaFileUncheckedCreateInput = {
    id?: string
    sessionNoteId: string
    fileName: string
    originalName: string
    fileType: $Enums.MediaType
    fileSize: number
    s3Key: string
    s3Bucket: string
    description?: string | null
    aiDescription?: string | null
    uploadedAt?: Date | string
  }

  export type MultimediaFileUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    fileName?: StringFieldUpdateOperationsInput | string
    originalName?: StringFieldUpdateOperationsInput | string
    fileType?: EnumMediaTypeFieldUpdateOperationsInput | $Enums.MediaType
    fileSize?: IntFieldUpdateOperationsInput | number
    s3Key?: StringFieldUpdateOperationsInput | string
    s3Bucket?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    aiDescription?: NullableStringFieldUpdateOperationsInput | string | null
    uploadedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    sessionNote?: SessionNoteUpdateOneRequiredWithoutMultimediaNestedInput
  }

  export type MultimediaFileUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    sessionNoteId?: StringFieldUpdateOperationsInput | string
    fileName?: StringFieldUpdateOperationsInput | string
    originalName?: StringFieldUpdateOperationsInput | string
    fileType?: EnumMediaTypeFieldUpdateOperationsInput | $Enums.MediaType
    fileSize?: IntFieldUpdateOperationsInput | number
    s3Key?: StringFieldUpdateOperationsInput | string
    s3Bucket?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    aiDescription?: NullableStringFieldUpdateOperationsInput | string | null
    uploadedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type MultimediaFileCreateManyInput = {
    id?: string
    sessionNoteId: string
    fileName: string
    originalName: string
    fileType: $Enums.MediaType
    fileSize: number
    s3Key: string
    s3Bucket: string
    description?: string | null
    aiDescription?: string | null
    uploadedAt?: Date | string
  }

  export type MultimediaFileUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    fileName?: StringFieldUpdateOperationsInput | string
    originalName?: StringFieldUpdateOperationsInput | string
    fileType?: EnumMediaTypeFieldUpdateOperationsInput | $Enums.MediaType
    fileSize?: IntFieldUpdateOperationsInput | number
    s3Key?: StringFieldUpdateOperationsInput | string
    s3Bucket?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    aiDescription?: NullableStringFieldUpdateOperationsInput | string | null
    uploadedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type MultimediaFileUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    sessionNoteId?: StringFieldUpdateOperationsInput | string
    fileName?: StringFieldUpdateOperationsInput | string
    originalName?: StringFieldUpdateOperationsInput | string
    fileType?: EnumMediaTypeFieldUpdateOperationsInput | $Enums.MediaType
    fileSize?: IntFieldUpdateOperationsInput | number
    s3Key?: StringFieldUpdateOperationsInput | string
    s3Bucket?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    aiDescription?: NullableStringFieldUpdateOperationsInput | string | null
    uploadedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AiSuggestionCreateInput = {
    id?: string
    multimediaId?: string | null
    suggestionType: $Enums.SuggestionType
    originalText?: string | null
    suggestedText: string
    isAccepted?: boolean
    isRejected?: boolean
    createdAt?: Date | string
    sessionNote: SessionNoteCreateNestedOneWithoutAiSuggestionsInput
  }

  export type AiSuggestionUncheckedCreateInput = {
    id?: string
    sessionNoteId: string
    multimediaId?: string | null
    suggestionType: $Enums.SuggestionType
    originalText?: string | null
    suggestedText: string
    isAccepted?: boolean
    isRejected?: boolean
    createdAt?: Date | string
  }

  export type AiSuggestionUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    multimediaId?: NullableStringFieldUpdateOperationsInput | string | null
    suggestionType?: EnumSuggestionTypeFieldUpdateOperationsInput | $Enums.SuggestionType
    originalText?: NullableStringFieldUpdateOperationsInput | string | null
    suggestedText?: StringFieldUpdateOperationsInput | string
    isAccepted?: BoolFieldUpdateOperationsInput | boolean
    isRejected?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    sessionNote?: SessionNoteUpdateOneRequiredWithoutAiSuggestionsNestedInput
  }

  export type AiSuggestionUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    sessionNoteId?: StringFieldUpdateOperationsInput | string
    multimediaId?: NullableStringFieldUpdateOperationsInput | string | null
    suggestionType?: EnumSuggestionTypeFieldUpdateOperationsInput | $Enums.SuggestionType
    originalText?: NullableStringFieldUpdateOperationsInput | string | null
    suggestedText?: StringFieldUpdateOperationsInput | string
    isAccepted?: BoolFieldUpdateOperationsInput | boolean
    isRejected?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AiSuggestionCreateManyInput = {
    id?: string
    sessionNoteId: string
    multimediaId?: string | null
    suggestionType: $Enums.SuggestionType
    originalText?: string | null
    suggestedText: string
    isAccepted?: boolean
    isRejected?: boolean
    createdAt?: Date | string
  }

  export type AiSuggestionUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    multimediaId?: NullableStringFieldUpdateOperationsInput | string | null
    suggestionType?: EnumSuggestionTypeFieldUpdateOperationsInput | $Enums.SuggestionType
    originalText?: NullableStringFieldUpdateOperationsInput | string | null
    suggestedText?: StringFieldUpdateOperationsInput | string
    isAccepted?: BoolFieldUpdateOperationsInput | boolean
    isRejected?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AiSuggestionUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    sessionNoteId?: StringFieldUpdateOperationsInput | string
    multimediaId?: NullableStringFieldUpdateOperationsInput | string | null
    suggestionType?: EnumSuggestionTypeFieldUpdateOperationsInput | $Enums.SuggestionType
    originalText?: NullableStringFieldUpdateOperationsInput | string | null
    suggestedText?: StringFieldUpdateOperationsInput | string
    isAccepted?: BoolFieldUpdateOperationsInput | boolean
    isRejected?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AuditLogCreateInput = {
    id?: string
    action: string
    resourceType: string
    resourceId: string
    details?: NullableJsonNullValueInput | InputJsonValue
    ipAddress?: string | null
    userAgent?: string | null
    timestamp?: Date | string
    user: UserCreateNestedOneWithoutAuditLogsInput
  }

  export type AuditLogUncheckedCreateInput = {
    id?: string
    userId: string
    action: string
    resourceType: string
    resourceId: string
    details?: NullableJsonNullValueInput | InputJsonValue
    ipAddress?: string | null
    userAgent?: string | null
    timestamp?: Date | string
  }

  export type AuditLogUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    action?: StringFieldUpdateOperationsInput | string
    resourceType?: StringFieldUpdateOperationsInput | string
    resourceId?: StringFieldUpdateOperationsInput | string
    details?: NullableJsonNullValueInput | InputJsonValue
    ipAddress?: NullableStringFieldUpdateOperationsInput | string | null
    userAgent?: NullableStringFieldUpdateOperationsInput | string | null
    timestamp?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutAuditLogsNestedInput
  }

  export type AuditLogUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    action?: StringFieldUpdateOperationsInput | string
    resourceType?: StringFieldUpdateOperationsInput | string
    resourceId?: StringFieldUpdateOperationsInput | string
    details?: NullableJsonNullValueInput | InputJsonValue
    ipAddress?: NullableStringFieldUpdateOperationsInput | string | null
    userAgent?: NullableStringFieldUpdateOperationsInput | string | null
    timestamp?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AuditLogCreateManyInput = {
    id?: string
    userId: string
    action: string
    resourceType: string
    resourceId: string
    details?: NullableJsonNullValueInput | InputJsonValue
    ipAddress?: string | null
    userAgent?: string | null
    timestamp?: Date | string
  }

  export type AuditLogUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    action?: StringFieldUpdateOperationsInput | string
    resourceType?: StringFieldUpdateOperationsInput | string
    resourceId?: StringFieldUpdateOperationsInput | string
    details?: NullableJsonNullValueInput | InputJsonValue
    ipAddress?: NullableStringFieldUpdateOperationsInput | string | null
    userAgent?: NullableStringFieldUpdateOperationsInput | string | null
    timestamp?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AuditLogUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    action?: StringFieldUpdateOperationsInput | string
    resourceType?: StringFieldUpdateOperationsInput | string
    resourceId?: StringFieldUpdateOperationsInput | string
    details?: NullableJsonNullValueInput | InputJsonValue
    ipAddress?: NullableStringFieldUpdateOperationsInput | string | null
    userAgent?: NullableStringFieldUpdateOperationsInput | string | null
    timestamp?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type EnumUserRoleFilter<$PrismaModel = never> = {
    equals?: $Enums.UserRole | EnumUserRoleFieldRefInput<$PrismaModel>
    in?: $Enums.UserRole[]
    notIn?: $Enums.UserRole[]
    not?: NestedEnumUserRoleFilter<$PrismaModel> | $Enums.UserRole
  }

  export type BoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type SessionNoteListRelationFilter = {
    every?: SessionNoteWhereInput
    some?: SessionNoteWhereInput
    none?: SessionNoteWhereInput
  }

  export type AuditLogListRelationFilter = {
    every?: AuditLogWhereInput
    some?: AuditLogWhereInput
    none?: AuditLogWhereInput
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type SessionNoteOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type AuditLogOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type UserCountOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    name?: SortOrder
    role?: SortOrder
    passwordHash?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserMaxOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    name?: SortOrder
    role?: SortOrder
    passwordHash?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserMinOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    name?: SortOrder
    role?: SortOrder
    passwordHash?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type EnumUserRoleWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.UserRole | EnumUserRoleFieldRefInput<$PrismaModel>
    in?: $Enums.UserRole[]
    notIn?: $Enums.UserRole[]
    not?: NestedEnumUserRoleWithAggregatesFilter<$PrismaModel> | $Enums.UserRole
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumUserRoleFilter<$PrismaModel>
    _max?: NestedEnumUserRoleFilter<$PrismaModel>
  }

  export type BoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type DateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type ClientCountOrderByAggregateInput = {
    id?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    dateOfBirth?: SortOrder
    ehrId?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type ClientMaxOrderByAggregateInput = {
    id?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    dateOfBirth?: SortOrder
    ehrId?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type ClientMinOrderByAggregateInput = {
    id?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    dateOfBirth?: SortOrder
    ehrId?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type DateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type EnumNoteTypeFilter<$PrismaModel = never> = {
    equals?: $Enums.NoteType | EnumNoteTypeFieldRefInput<$PrismaModel>
    in?: $Enums.NoteType[]
    notIn?: $Enums.NoteType[]
    not?: NestedEnumNoteTypeFilter<$PrismaModel> | $Enums.NoteType
  }

  export type IntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type ClientScalarRelationFilter = {
    is?: ClientWhereInput
    isNot?: ClientWhereInput
  }

  export type UserScalarRelationFilter = {
    is?: UserWhereInput
    isNot?: UserWhereInput
  }

  export type MultimediaFileListRelationFilter = {
    every?: MultimediaFileWhereInput
    some?: MultimediaFileWhereInput
    none?: MultimediaFileWhereInput
  }

  export type AiSuggestionListRelationFilter = {
    every?: AiSuggestionWhereInput
    some?: AiSuggestionWhereInput
    none?: AiSuggestionWhereInput
  }

  export type MultimediaFileOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type AiSuggestionOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type SessionNoteCountOrderByAggregateInput = {
    id?: SortOrder
    clientId?: SortOrder
    therapistId?: SortOrder
    sessionDate?: SortOrder
    noteType?: SortOrder
    subjective?: SortOrder
    objective?: SortOrder
    assessment?: SortOrder
    plan?: SortOrder
    sessionDuration?: SortOrder
    interventions?: SortOrder
    clientMood?: SortOrder
    isFinalized?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type SessionNoteAvgOrderByAggregateInput = {
    sessionDuration?: SortOrder
  }

  export type SessionNoteMaxOrderByAggregateInput = {
    id?: SortOrder
    clientId?: SortOrder
    therapistId?: SortOrder
    sessionDate?: SortOrder
    noteType?: SortOrder
    subjective?: SortOrder
    objective?: SortOrder
    assessment?: SortOrder
    plan?: SortOrder
    sessionDuration?: SortOrder
    interventions?: SortOrder
    clientMood?: SortOrder
    isFinalized?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type SessionNoteMinOrderByAggregateInput = {
    id?: SortOrder
    clientId?: SortOrder
    therapistId?: SortOrder
    sessionDate?: SortOrder
    noteType?: SortOrder
    subjective?: SortOrder
    objective?: SortOrder
    assessment?: SortOrder
    plan?: SortOrder
    sessionDuration?: SortOrder
    interventions?: SortOrder
    clientMood?: SortOrder
    isFinalized?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type SessionNoteSumOrderByAggregateInput = {
    sessionDuration?: SortOrder
  }

  export type EnumNoteTypeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.NoteType | EnumNoteTypeFieldRefInput<$PrismaModel>
    in?: $Enums.NoteType[]
    notIn?: $Enums.NoteType[]
    not?: NestedEnumNoteTypeWithAggregatesFilter<$PrismaModel> | $Enums.NoteType
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumNoteTypeFilter<$PrismaModel>
    _max?: NestedEnumNoteTypeFilter<$PrismaModel>
  }

  export type IntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type EnumMediaTypeFilter<$PrismaModel = never> = {
    equals?: $Enums.MediaType | EnumMediaTypeFieldRefInput<$PrismaModel>
    in?: $Enums.MediaType[]
    notIn?: $Enums.MediaType[]
    not?: NestedEnumMediaTypeFilter<$PrismaModel> | $Enums.MediaType
  }

  export type IntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type SessionNoteScalarRelationFilter = {
    is?: SessionNoteWhereInput
    isNot?: SessionNoteWhereInput
  }

  export type MultimediaFileCountOrderByAggregateInput = {
    id?: SortOrder
    sessionNoteId?: SortOrder
    fileName?: SortOrder
    originalName?: SortOrder
    fileType?: SortOrder
    fileSize?: SortOrder
    s3Key?: SortOrder
    s3Bucket?: SortOrder
    description?: SortOrder
    aiDescription?: SortOrder
    uploadedAt?: SortOrder
  }

  export type MultimediaFileAvgOrderByAggregateInput = {
    fileSize?: SortOrder
  }

  export type MultimediaFileMaxOrderByAggregateInput = {
    id?: SortOrder
    sessionNoteId?: SortOrder
    fileName?: SortOrder
    originalName?: SortOrder
    fileType?: SortOrder
    fileSize?: SortOrder
    s3Key?: SortOrder
    s3Bucket?: SortOrder
    description?: SortOrder
    aiDescription?: SortOrder
    uploadedAt?: SortOrder
  }

  export type MultimediaFileMinOrderByAggregateInput = {
    id?: SortOrder
    sessionNoteId?: SortOrder
    fileName?: SortOrder
    originalName?: SortOrder
    fileType?: SortOrder
    fileSize?: SortOrder
    s3Key?: SortOrder
    s3Bucket?: SortOrder
    description?: SortOrder
    aiDescription?: SortOrder
    uploadedAt?: SortOrder
  }

  export type MultimediaFileSumOrderByAggregateInput = {
    fileSize?: SortOrder
  }

  export type EnumMediaTypeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.MediaType | EnumMediaTypeFieldRefInput<$PrismaModel>
    in?: $Enums.MediaType[]
    notIn?: $Enums.MediaType[]
    not?: NestedEnumMediaTypeWithAggregatesFilter<$PrismaModel> | $Enums.MediaType
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumMediaTypeFilter<$PrismaModel>
    _max?: NestedEnumMediaTypeFilter<$PrismaModel>
  }

  export type IntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type EnumSuggestionTypeFilter<$PrismaModel = never> = {
    equals?: $Enums.SuggestionType | EnumSuggestionTypeFieldRefInput<$PrismaModel>
    in?: $Enums.SuggestionType[]
    notIn?: $Enums.SuggestionType[]
    not?: NestedEnumSuggestionTypeFilter<$PrismaModel> | $Enums.SuggestionType
  }

  export type AiSuggestionCountOrderByAggregateInput = {
    id?: SortOrder
    sessionNoteId?: SortOrder
    multimediaId?: SortOrder
    suggestionType?: SortOrder
    originalText?: SortOrder
    suggestedText?: SortOrder
    isAccepted?: SortOrder
    isRejected?: SortOrder
    createdAt?: SortOrder
  }

  export type AiSuggestionMaxOrderByAggregateInput = {
    id?: SortOrder
    sessionNoteId?: SortOrder
    multimediaId?: SortOrder
    suggestionType?: SortOrder
    originalText?: SortOrder
    suggestedText?: SortOrder
    isAccepted?: SortOrder
    isRejected?: SortOrder
    createdAt?: SortOrder
  }

  export type AiSuggestionMinOrderByAggregateInput = {
    id?: SortOrder
    sessionNoteId?: SortOrder
    multimediaId?: SortOrder
    suggestionType?: SortOrder
    originalText?: SortOrder
    suggestedText?: SortOrder
    isAccepted?: SortOrder
    isRejected?: SortOrder
    createdAt?: SortOrder
  }

  export type EnumSuggestionTypeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.SuggestionType | EnumSuggestionTypeFieldRefInput<$PrismaModel>
    in?: $Enums.SuggestionType[]
    notIn?: $Enums.SuggestionType[]
    not?: NestedEnumSuggestionTypeWithAggregatesFilter<$PrismaModel> | $Enums.SuggestionType
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumSuggestionTypeFilter<$PrismaModel>
    _max?: NestedEnumSuggestionTypeFilter<$PrismaModel>
  }
  export type JsonNullableFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonNullableFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>

  export type JsonNullableFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type AuditLogCountOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    action?: SortOrder
    resourceType?: SortOrder
    resourceId?: SortOrder
    details?: SortOrder
    ipAddress?: SortOrder
    userAgent?: SortOrder
    timestamp?: SortOrder
  }

  export type AuditLogMaxOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    action?: SortOrder
    resourceType?: SortOrder
    resourceId?: SortOrder
    ipAddress?: SortOrder
    userAgent?: SortOrder
    timestamp?: SortOrder
  }

  export type AuditLogMinOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    action?: SortOrder
    resourceType?: SortOrder
    resourceId?: SortOrder
    ipAddress?: SortOrder
    userAgent?: SortOrder
    timestamp?: SortOrder
  }
  export type JsonNullableWithAggregatesFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>

  export type JsonNullableWithAggregatesFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedJsonNullableFilter<$PrismaModel>
    _max?: NestedJsonNullableFilter<$PrismaModel>
  }

  export type SessionNoteCreateNestedManyWithoutTherapistInput = {
    create?: XOR<SessionNoteCreateWithoutTherapistInput, SessionNoteUncheckedCreateWithoutTherapistInput> | SessionNoteCreateWithoutTherapistInput[] | SessionNoteUncheckedCreateWithoutTherapistInput[]
    connectOrCreate?: SessionNoteCreateOrConnectWithoutTherapistInput | SessionNoteCreateOrConnectWithoutTherapistInput[]
    createMany?: SessionNoteCreateManyTherapistInputEnvelope
    connect?: SessionNoteWhereUniqueInput | SessionNoteWhereUniqueInput[]
  }

  export type AuditLogCreateNestedManyWithoutUserInput = {
    create?: XOR<AuditLogCreateWithoutUserInput, AuditLogUncheckedCreateWithoutUserInput> | AuditLogCreateWithoutUserInput[] | AuditLogUncheckedCreateWithoutUserInput[]
    connectOrCreate?: AuditLogCreateOrConnectWithoutUserInput | AuditLogCreateOrConnectWithoutUserInput[]
    createMany?: AuditLogCreateManyUserInputEnvelope
    connect?: AuditLogWhereUniqueInput | AuditLogWhereUniqueInput[]
  }

  export type SessionNoteUncheckedCreateNestedManyWithoutTherapistInput = {
    create?: XOR<SessionNoteCreateWithoutTherapistInput, SessionNoteUncheckedCreateWithoutTherapistInput> | SessionNoteCreateWithoutTherapistInput[] | SessionNoteUncheckedCreateWithoutTherapistInput[]
    connectOrCreate?: SessionNoteCreateOrConnectWithoutTherapistInput | SessionNoteCreateOrConnectWithoutTherapistInput[]
    createMany?: SessionNoteCreateManyTherapistInputEnvelope
    connect?: SessionNoteWhereUniqueInput | SessionNoteWhereUniqueInput[]
  }

  export type AuditLogUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<AuditLogCreateWithoutUserInput, AuditLogUncheckedCreateWithoutUserInput> | AuditLogCreateWithoutUserInput[] | AuditLogUncheckedCreateWithoutUserInput[]
    connectOrCreate?: AuditLogCreateOrConnectWithoutUserInput | AuditLogCreateOrConnectWithoutUserInput[]
    createMany?: AuditLogCreateManyUserInputEnvelope
    connect?: AuditLogWhereUniqueInput | AuditLogWhereUniqueInput[]
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type EnumUserRoleFieldUpdateOperationsInput = {
    set?: $Enums.UserRole
  }

  export type BoolFieldUpdateOperationsInput = {
    set?: boolean
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type SessionNoteUpdateManyWithoutTherapistNestedInput = {
    create?: XOR<SessionNoteCreateWithoutTherapistInput, SessionNoteUncheckedCreateWithoutTherapistInput> | SessionNoteCreateWithoutTherapistInput[] | SessionNoteUncheckedCreateWithoutTherapistInput[]
    connectOrCreate?: SessionNoteCreateOrConnectWithoutTherapistInput | SessionNoteCreateOrConnectWithoutTherapistInput[]
    upsert?: SessionNoteUpsertWithWhereUniqueWithoutTherapistInput | SessionNoteUpsertWithWhereUniqueWithoutTherapistInput[]
    createMany?: SessionNoteCreateManyTherapistInputEnvelope
    set?: SessionNoteWhereUniqueInput | SessionNoteWhereUniqueInput[]
    disconnect?: SessionNoteWhereUniqueInput | SessionNoteWhereUniqueInput[]
    delete?: SessionNoteWhereUniqueInput | SessionNoteWhereUniqueInput[]
    connect?: SessionNoteWhereUniqueInput | SessionNoteWhereUniqueInput[]
    update?: SessionNoteUpdateWithWhereUniqueWithoutTherapistInput | SessionNoteUpdateWithWhereUniqueWithoutTherapistInput[]
    updateMany?: SessionNoteUpdateManyWithWhereWithoutTherapistInput | SessionNoteUpdateManyWithWhereWithoutTherapistInput[]
    deleteMany?: SessionNoteScalarWhereInput | SessionNoteScalarWhereInput[]
  }

  export type AuditLogUpdateManyWithoutUserNestedInput = {
    create?: XOR<AuditLogCreateWithoutUserInput, AuditLogUncheckedCreateWithoutUserInput> | AuditLogCreateWithoutUserInput[] | AuditLogUncheckedCreateWithoutUserInput[]
    connectOrCreate?: AuditLogCreateOrConnectWithoutUserInput | AuditLogCreateOrConnectWithoutUserInput[]
    upsert?: AuditLogUpsertWithWhereUniqueWithoutUserInput | AuditLogUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: AuditLogCreateManyUserInputEnvelope
    set?: AuditLogWhereUniqueInput | AuditLogWhereUniqueInput[]
    disconnect?: AuditLogWhereUniqueInput | AuditLogWhereUniqueInput[]
    delete?: AuditLogWhereUniqueInput | AuditLogWhereUniqueInput[]
    connect?: AuditLogWhereUniqueInput | AuditLogWhereUniqueInput[]
    update?: AuditLogUpdateWithWhereUniqueWithoutUserInput | AuditLogUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: AuditLogUpdateManyWithWhereWithoutUserInput | AuditLogUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: AuditLogScalarWhereInput | AuditLogScalarWhereInput[]
  }

  export type SessionNoteUncheckedUpdateManyWithoutTherapistNestedInput = {
    create?: XOR<SessionNoteCreateWithoutTherapistInput, SessionNoteUncheckedCreateWithoutTherapistInput> | SessionNoteCreateWithoutTherapistInput[] | SessionNoteUncheckedCreateWithoutTherapistInput[]
    connectOrCreate?: SessionNoteCreateOrConnectWithoutTherapistInput | SessionNoteCreateOrConnectWithoutTherapistInput[]
    upsert?: SessionNoteUpsertWithWhereUniqueWithoutTherapistInput | SessionNoteUpsertWithWhereUniqueWithoutTherapistInput[]
    createMany?: SessionNoteCreateManyTherapistInputEnvelope
    set?: SessionNoteWhereUniqueInput | SessionNoteWhereUniqueInput[]
    disconnect?: SessionNoteWhereUniqueInput | SessionNoteWhereUniqueInput[]
    delete?: SessionNoteWhereUniqueInput | SessionNoteWhereUniqueInput[]
    connect?: SessionNoteWhereUniqueInput | SessionNoteWhereUniqueInput[]
    update?: SessionNoteUpdateWithWhereUniqueWithoutTherapistInput | SessionNoteUpdateWithWhereUniqueWithoutTherapistInput[]
    updateMany?: SessionNoteUpdateManyWithWhereWithoutTherapistInput | SessionNoteUpdateManyWithWhereWithoutTherapistInput[]
    deleteMany?: SessionNoteScalarWhereInput | SessionNoteScalarWhereInput[]
  }

  export type AuditLogUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<AuditLogCreateWithoutUserInput, AuditLogUncheckedCreateWithoutUserInput> | AuditLogCreateWithoutUserInput[] | AuditLogUncheckedCreateWithoutUserInput[]
    connectOrCreate?: AuditLogCreateOrConnectWithoutUserInput | AuditLogCreateOrConnectWithoutUserInput[]
    upsert?: AuditLogUpsertWithWhereUniqueWithoutUserInput | AuditLogUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: AuditLogCreateManyUserInputEnvelope
    set?: AuditLogWhereUniqueInput | AuditLogWhereUniqueInput[]
    disconnect?: AuditLogWhereUniqueInput | AuditLogWhereUniqueInput[]
    delete?: AuditLogWhereUniqueInput | AuditLogWhereUniqueInput[]
    connect?: AuditLogWhereUniqueInput | AuditLogWhereUniqueInput[]
    update?: AuditLogUpdateWithWhereUniqueWithoutUserInput | AuditLogUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: AuditLogUpdateManyWithWhereWithoutUserInput | AuditLogUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: AuditLogScalarWhereInput | AuditLogScalarWhereInput[]
  }

  export type SessionNoteCreateNestedManyWithoutClientInput = {
    create?: XOR<SessionNoteCreateWithoutClientInput, SessionNoteUncheckedCreateWithoutClientInput> | SessionNoteCreateWithoutClientInput[] | SessionNoteUncheckedCreateWithoutClientInput[]
    connectOrCreate?: SessionNoteCreateOrConnectWithoutClientInput | SessionNoteCreateOrConnectWithoutClientInput[]
    createMany?: SessionNoteCreateManyClientInputEnvelope
    connect?: SessionNoteWhereUniqueInput | SessionNoteWhereUniqueInput[]
  }

  export type SessionNoteUncheckedCreateNestedManyWithoutClientInput = {
    create?: XOR<SessionNoteCreateWithoutClientInput, SessionNoteUncheckedCreateWithoutClientInput> | SessionNoteCreateWithoutClientInput[] | SessionNoteUncheckedCreateWithoutClientInput[]
    connectOrCreate?: SessionNoteCreateOrConnectWithoutClientInput | SessionNoteCreateOrConnectWithoutClientInput[]
    createMany?: SessionNoteCreateManyClientInputEnvelope
    connect?: SessionNoteWhereUniqueInput | SessionNoteWhereUniqueInput[]
  }

  export type NullableDateTimeFieldUpdateOperationsInput = {
    set?: Date | string | null
  }

  export type SessionNoteUpdateManyWithoutClientNestedInput = {
    create?: XOR<SessionNoteCreateWithoutClientInput, SessionNoteUncheckedCreateWithoutClientInput> | SessionNoteCreateWithoutClientInput[] | SessionNoteUncheckedCreateWithoutClientInput[]
    connectOrCreate?: SessionNoteCreateOrConnectWithoutClientInput | SessionNoteCreateOrConnectWithoutClientInput[]
    upsert?: SessionNoteUpsertWithWhereUniqueWithoutClientInput | SessionNoteUpsertWithWhereUniqueWithoutClientInput[]
    createMany?: SessionNoteCreateManyClientInputEnvelope
    set?: SessionNoteWhereUniqueInput | SessionNoteWhereUniqueInput[]
    disconnect?: SessionNoteWhereUniqueInput | SessionNoteWhereUniqueInput[]
    delete?: SessionNoteWhereUniqueInput | SessionNoteWhereUniqueInput[]
    connect?: SessionNoteWhereUniqueInput | SessionNoteWhereUniqueInput[]
    update?: SessionNoteUpdateWithWhereUniqueWithoutClientInput | SessionNoteUpdateWithWhereUniqueWithoutClientInput[]
    updateMany?: SessionNoteUpdateManyWithWhereWithoutClientInput | SessionNoteUpdateManyWithWhereWithoutClientInput[]
    deleteMany?: SessionNoteScalarWhereInput | SessionNoteScalarWhereInput[]
  }

  export type SessionNoteUncheckedUpdateManyWithoutClientNestedInput = {
    create?: XOR<SessionNoteCreateWithoutClientInput, SessionNoteUncheckedCreateWithoutClientInput> | SessionNoteCreateWithoutClientInput[] | SessionNoteUncheckedCreateWithoutClientInput[]
    connectOrCreate?: SessionNoteCreateOrConnectWithoutClientInput | SessionNoteCreateOrConnectWithoutClientInput[]
    upsert?: SessionNoteUpsertWithWhereUniqueWithoutClientInput | SessionNoteUpsertWithWhereUniqueWithoutClientInput[]
    createMany?: SessionNoteCreateManyClientInputEnvelope
    set?: SessionNoteWhereUniqueInput | SessionNoteWhereUniqueInput[]
    disconnect?: SessionNoteWhereUniqueInput | SessionNoteWhereUniqueInput[]
    delete?: SessionNoteWhereUniqueInput | SessionNoteWhereUniqueInput[]
    connect?: SessionNoteWhereUniqueInput | SessionNoteWhereUniqueInput[]
    update?: SessionNoteUpdateWithWhereUniqueWithoutClientInput | SessionNoteUpdateWithWhereUniqueWithoutClientInput[]
    updateMany?: SessionNoteUpdateManyWithWhereWithoutClientInput | SessionNoteUpdateManyWithWhereWithoutClientInput[]
    deleteMany?: SessionNoteScalarWhereInput | SessionNoteScalarWhereInput[]
  }

  export type ClientCreateNestedOneWithoutSessionNotesInput = {
    create?: XOR<ClientCreateWithoutSessionNotesInput, ClientUncheckedCreateWithoutSessionNotesInput>
    connectOrCreate?: ClientCreateOrConnectWithoutSessionNotesInput
    connect?: ClientWhereUniqueInput
  }

  export type UserCreateNestedOneWithoutSessionNotesInput = {
    create?: XOR<UserCreateWithoutSessionNotesInput, UserUncheckedCreateWithoutSessionNotesInput>
    connectOrCreate?: UserCreateOrConnectWithoutSessionNotesInput
    connect?: UserWhereUniqueInput
  }

  export type MultimediaFileCreateNestedManyWithoutSessionNoteInput = {
    create?: XOR<MultimediaFileCreateWithoutSessionNoteInput, MultimediaFileUncheckedCreateWithoutSessionNoteInput> | MultimediaFileCreateWithoutSessionNoteInput[] | MultimediaFileUncheckedCreateWithoutSessionNoteInput[]
    connectOrCreate?: MultimediaFileCreateOrConnectWithoutSessionNoteInput | MultimediaFileCreateOrConnectWithoutSessionNoteInput[]
    createMany?: MultimediaFileCreateManySessionNoteInputEnvelope
    connect?: MultimediaFileWhereUniqueInput | MultimediaFileWhereUniqueInput[]
  }

  export type AiSuggestionCreateNestedManyWithoutSessionNoteInput = {
    create?: XOR<AiSuggestionCreateWithoutSessionNoteInput, AiSuggestionUncheckedCreateWithoutSessionNoteInput> | AiSuggestionCreateWithoutSessionNoteInput[] | AiSuggestionUncheckedCreateWithoutSessionNoteInput[]
    connectOrCreate?: AiSuggestionCreateOrConnectWithoutSessionNoteInput | AiSuggestionCreateOrConnectWithoutSessionNoteInput[]
    createMany?: AiSuggestionCreateManySessionNoteInputEnvelope
    connect?: AiSuggestionWhereUniqueInput | AiSuggestionWhereUniqueInput[]
  }

  export type MultimediaFileUncheckedCreateNestedManyWithoutSessionNoteInput = {
    create?: XOR<MultimediaFileCreateWithoutSessionNoteInput, MultimediaFileUncheckedCreateWithoutSessionNoteInput> | MultimediaFileCreateWithoutSessionNoteInput[] | MultimediaFileUncheckedCreateWithoutSessionNoteInput[]
    connectOrCreate?: MultimediaFileCreateOrConnectWithoutSessionNoteInput | MultimediaFileCreateOrConnectWithoutSessionNoteInput[]
    createMany?: MultimediaFileCreateManySessionNoteInputEnvelope
    connect?: MultimediaFileWhereUniqueInput | MultimediaFileWhereUniqueInput[]
  }

  export type AiSuggestionUncheckedCreateNestedManyWithoutSessionNoteInput = {
    create?: XOR<AiSuggestionCreateWithoutSessionNoteInput, AiSuggestionUncheckedCreateWithoutSessionNoteInput> | AiSuggestionCreateWithoutSessionNoteInput[] | AiSuggestionUncheckedCreateWithoutSessionNoteInput[]
    connectOrCreate?: AiSuggestionCreateOrConnectWithoutSessionNoteInput | AiSuggestionCreateOrConnectWithoutSessionNoteInput[]
    createMany?: AiSuggestionCreateManySessionNoteInputEnvelope
    connect?: AiSuggestionWhereUniqueInput | AiSuggestionWhereUniqueInput[]
  }

  export type EnumNoteTypeFieldUpdateOperationsInput = {
    set?: $Enums.NoteType
  }

  export type NullableIntFieldUpdateOperationsInput = {
    set?: number | null
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type ClientUpdateOneRequiredWithoutSessionNotesNestedInput = {
    create?: XOR<ClientCreateWithoutSessionNotesInput, ClientUncheckedCreateWithoutSessionNotesInput>
    connectOrCreate?: ClientCreateOrConnectWithoutSessionNotesInput
    upsert?: ClientUpsertWithoutSessionNotesInput
    connect?: ClientWhereUniqueInput
    update?: XOR<XOR<ClientUpdateToOneWithWhereWithoutSessionNotesInput, ClientUpdateWithoutSessionNotesInput>, ClientUncheckedUpdateWithoutSessionNotesInput>
  }

  export type UserUpdateOneRequiredWithoutSessionNotesNestedInput = {
    create?: XOR<UserCreateWithoutSessionNotesInput, UserUncheckedCreateWithoutSessionNotesInput>
    connectOrCreate?: UserCreateOrConnectWithoutSessionNotesInput
    upsert?: UserUpsertWithoutSessionNotesInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutSessionNotesInput, UserUpdateWithoutSessionNotesInput>, UserUncheckedUpdateWithoutSessionNotesInput>
  }

  export type MultimediaFileUpdateManyWithoutSessionNoteNestedInput = {
    create?: XOR<MultimediaFileCreateWithoutSessionNoteInput, MultimediaFileUncheckedCreateWithoutSessionNoteInput> | MultimediaFileCreateWithoutSessionNoteInput[] | MultimediaFileUncheckedCreateWithoutSessionNoteInput[]
    connectOrCreate?: MultimediaFileCreateOrConnectWithoutSessionNoteInput | MultimediaFileCreateOrConnectWithoutSessionNoteInput[]
    upsert?: MultimediaFileUpsertWithWhereUniqueWithoutSessionNoteInput | MultimediaFileUpsertWithWhereUniqueWithoutSessionNoteInput[]
    createMany?: MultimediaFileCreateManySessionNoteInputEnvelope
    set?: MultimediaFileWhereUniqueInput | MultimediaFileWhereUniqueInput[]
    disconnect?: MultimediaFileWhereUniqueInput | MultimediaFileWhereUniqueInput[]
    delete?: MultimediaFileWhereUniqueInput | MultimediaFileWhereUniqueInput[]
    connect?: MultimediaFileWhereUniqueInput | MultimediaFileWhereUniqueInput[]
    update?: MultimediaFileUpdateWithWhereUniqueWithoutSessionNoteInput | MultimediaFileUpdateWithWhereUniqueWithoutSessionNoteInput[]
    updateMany?: MultimediaFileUpdateManyWithWhereWithoutSessionNoteInput | MultimediaFileUpdateManyWithWhereWithoutSessionNoteInput[]
    deleteMany?: MultimediaFileScalarWhereInput | MultimediaFileScalarWhereInput[]
  }

  export type AiSuggestionUpdateManyWithoutSessionNoteNestedInput = {
    create?: XOR<AiSuggestionCreateWithoutSessionNoteInput, AiSuggestionUncheckedCreateWithoutSessionNoteInput> | AiSuggestionCreateWithoutSessionNoteInput[] | AiSuggestionUncheckedCreateWithoutSessionNoteInput[]
    connectOrCreate?: AiSuggestionCreateOrConnectWithoutSessionNoteInput | AiSuggestionCreateOrConnectWithoutSessionNoteInput[]
    upsert?: AiSuggestionUpsertWithWhereUniqueWithoutSessionNoteInput | AiSuggestionUpsertWithWhereUniqueWithoutSessionNoteInput[]
    createMany?: AiSuggestionCreateManySessionNoteInputEnvelope
    set?: AiSuggestionWhereUniqueInput | AiSuggestionWhereUniqueInput[]
    disconnect?: AiSuggestionWhereUniqueInput | AiSuggestionWhereUniqueInput[]
    delete?: AiSuggestionWhereUniqueInput | AiSuggestionWhereUniqueInput[]
    connect?: AiSuggestionWhereUniqueInput | AiSuggestionWhereUniqueInput[]
    update?: AiSuggestionUpdateWithWhereUniqueWithoutSessionNoteInput | AiSuggestionUpdateWithWhereUniqueWithoutSessionNoteInput[]
    updateMany?: AiSuggestionUpdateManyWithWhereWithoutSessionNoteInput | AiSuggestionUpdateManyWithWhereWithoutSessionNoteInput[]
    deleteMany?: AiSuggestionScalarWhereInput | AiSuggestionScalarWhereInput[]
  }

  export type MultimediaFileUncheckedUpdateManyWithoutSessionNoteNestedInput = {
    create?: XOR<MultimediaFileCreateWithoutSessionNoteInput, MultimediaFileUncheckedCreateWithoutSessionNoteInput> | MultimediaFileCreateWithoutSessionNoteInput[] | MultimediaFileUncheckedCreateWithoutSessionNoteInput[]
    connectOrCreate?: MultimediaFileCreateOrConnectWithoutSessionNoteInput | MultimediaFileCreateOrConnectWithoutSessionNoteInput[]
    upsert?: MultimediaFileUpsertWithWhereUniqueWithoutSessionNoteInput | MultimediaFileUpsertWithWhereUniqueWithoutSessionNoteInput[]
    createMany?: MultimediaFileCreateManySessionNoteInputEnvelope
    set?: MultimediaFileWhereUniqueInput | MultimediaFileWhereUniqueInput[]
    disconnect?: MultimediaFileWhereUniqueInput | MultimediaFileWhereUniqueInput[]
    delete?: MultimediaFileWhereUniqueInput | MultimediaFileWhereUniqueInput[]
    connect?: MultimediaFileWhereUniqueInput | MultimediaFileWhereUniqueInput[]
    update?: MultimediaFileUpdateWithWhereUniqueWithoutSessionNoteInput | MultimediaFileUpdateWithWhereUniqueWithoutSessionNoteInput[]
    updateMany?: MultimediaFileUpdateManyWithWhereWithoutSessionNoteInput | MultimediaFileUpdateManyWithWhereWithoutSessionNoteInput[]
    deleteMany?: MultimediaFileScalarWhereInput | MultimediaFileScalarWhereInput[]
  }

  export type AiSuggestionUncheckedUpdateManyWithoutSessionNoteNestedInput = {
    create?: XOR<AiSuggestionCreateWithoutSessionNoteInput, AiSuggestionUncheckedCreateWithoutSessionNoteInput> | AiSuggestionCreateWithoutSessionNoteInput[] | AiSuggestionUncheckedCreateWithoutSessionNoteInput[]
    connectOrCreate?: AiSuggestionCreateOrConnectWithoutSessionNoteInput | AiSuggestionCreateOrConnectWithoutSessionNoteInput[]
    upsert?: AiSuggestionUpsertWithWhereUniqueWithoutSessionNoteInput | AiSuggestionUpsertWithWhereUniqueWithoutSessionNoteInput[]
    createMany?: AiSuggestionCreateManySessionNoteInputEnvelope
    set?: AiSuggestionWhereUniqueInput | AiSuggestionWhereUniqueInput[]
    disconnect?: AiSuggestionWhereUniqueInput | AiSuggestionWhereUniqueInput[]
    delete?: AiSuggestionWhereUniqueInput | AiSuggestionWhereUniqueInput[]
    connect?: AiSuggestionWhereUniqueInput | AiSuggestionWhereUniqueInput[]
    update?: AiSuggestionUpdateWithWhereUniqueWithoutSessionNoteInput | AiSuggestionUpdateWithWhereUniqueWithoutSessionNoteInput[]
    updateMany?: AiSuggestionUpdateManyWithWhereWithoutSessionNoteInput | AiSuggestionUpdateManyWithWhereWithoutSessionNoteInput[]
    deleteMany?: AiSuggestionScalarWhereInput | AiSuggestionScalarWhereInput[]
  }

  export type SessionNoteCreateNestedOneWithoutMultimediaInput = {
    create?: XOR<SessionNoteCreateWithoutMultimediaInput, SessionNoteUncheckedCreateWithoutMultimediaInput>
    connectOrCreate?: SessionNoteCreateOrConnectWithoutMultimediaInput
    connect?: SessionNoteWhereUniqueInput
  }

  export type EnumMediaTypeFieldUpdateOperationsInput = {
    set?: $Enums.MediaType
  }

  export type IntFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type SessionNoteUpdateOneRequiredWithoutMultimediaNestedInput = {
    create?: XOR<SessionNoteCreateWithoutMultimediaInput, SessionNoteUncheckedCreateWithoutMultimediaInput>
    connectOrCreate?: SessionNoteCreateOrConnectWithoutMultimediaInput
    upsert?: SessionNoteUpsertWithoutMultimediaInput
    connect?: SessionNoteWhereUniqueInput
    update?: XOR<XOR<SessionNoteUpdateToOneWithWhereWithoutMultimediaInput, SessionNoteUpdateWithoutMultimediaInput>, SessionNoteUncheckedUpdateWithoutMultimediaInput>
  }

  export type SessionNoteCreateNestedOneWithoutAiSuggestionsInput = {
    create?: XOR<SessionNoteCreateWithoutAiSuggestionsInput, SessionNoteUncheckedCreateWithoutAiSuggestionsInput>
    connectOrCreate?: SessionNoteCreateOrConnectWithoutAiSuggestionsInput
    connect?: SessionNoteWhereUniqueInput
  }

  export type EnumSuggestionTypeFieldUpdateOperationsInput = {
    set?: $Enums.SuggestionType
  }

  export type SessionNoteUpdateOneRequiredWithoutAiSuggestionsNestedInput = {
    create?: XOR<SessionNoteCreateWithoutAiSuggestionsInput, SessionNoteUncheckedCreateWithoutAiSuggestionsInput>
    connectOrCreate?: SessionNoteCreateOrConnectWithoutAiSuggestionsInput
    upsert?: SessionNoteUpsertWithoutAiSuggestionsInput
    connect?: SessionNoteWhereUniqueInput
    update?: XOR<XOR<SessionNoteUpdateToOneWithWhereWithoutAiSuggestionsInput, SessionNoteUpdateWithoutAiSuggestionsInput>, SessionNoteUncheckedUpdateWithoutAiSuggestionsInput>
  }

  export type UserCreateNestedOneWithoutAuditLogsInput = {
    create?: XOR<UserCreateWithoutAuditLogsInput, UserUncheckedCreateWithoutAuditLogsInput>
    connectOrCreate?: UserCreateOrConnectWithoutAuditLogsInput
    connect?: UserWhereUniqueInput
  }

  export type UserUpdateOneRequiredWithoutAuditLogsNestedInput = {
    create?: XOR<UserCreateWithoutAuditLogsInput, UserUncheckedCreateWithoutAuditLogsInput>
    connectOrCreate?: UserCreateOrConnectWithoutAuditLogsInput
    upsert?: UserUpsertWithoutAuditLogsInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutAuditLogsInput, UserUpdateWithoutAuditLogsInput>, UserUncheckedUpdateWithoutAuditLogsInput>
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedEnumUserRoleFilter<$PrismaModel = never> = {
    equals?: $Enums.UserRole | EnumUserRoleFieldRefInput<$PrismaModel>
    in?: $Enums.UserRole[]
    notIn?: $Enums.UserRole[]
    not?: NestedEnumUserRoleFilter<$PrismaModel> | $Enums.UserRole
  }

  export type NestedBoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type NestedEnumUserRoleWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.UserRole | EnumUserRoleFieldRefInput<$PrismaModel>
    in?: $Enums.UserRole[]
    notIn?: $Enums.UserRole[]
    not?: NestedEnumUserRoleWithAggregatesFilter<$PrismaModel> | $Enums.UserRole
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumUserRoleFilter<$PrismaModel>
    _max?: NestedEnumUserRoleFilter<$PrismaModel>
  }

  export type NestedBoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type NestedDateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type NestedDateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type NestedEnumNoteTypeFilter<$PrismaModel = never> = {
    equals?: $Enums.NoteType | EnumNoteTypeFieldRefInput<$PrismaModel>
    in?: $Enums.NoteType[]
    notIn?: $Enums.NoteType[]
    not?: NestedEnumNoteTypeFilter<$PrismaModel> | $Enums.NoteType
  }

  export type NestedEnumNoteTypeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.NoteType | EnumNoteTypeFieldRefInput<$PrismaModel>
    in?: $Enums.NoteType[]
    notIn?: $Enums.NoteType[]
    not?: NestedEnumNoteTypeWithAggregatesFilter<$PrismaModel> | $Enums.NoteType
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumNoteTypeFilter<$PrismaModel>
    _max?: NestedEnumNoteTypeFilter<$PrismaModel>
  }

  export type NestedIntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type NestedFloatNullableFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableFilter<$PrismaModel> | number | null
  }

  export type NestedEnumMediaTypeFilter<$PrismaModel = never> = {
    equals?: $Enums.MediaType | EnumMediaTypeFieldRefInput<$PrismaModel>
    in?: $Enums.MediaType[]
    notIn?: $Enums.MediaType[]
    not?: NestedEnumMediaTypeFilter<$PrismaModel> | $Enums.MediaType
  }

  export type NestedEnumMediaTypeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.MediaType | EnumMediaTypeFieldRefInput<$PrismaModel>
    in?: $Enums.MediaType[]
    notIn?: $Enums.MediaType[]
    not?: NestedEnumMediaTypeWithAggregatesFilter<$PrismaModel> | $Enums.MediaType
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumMediaTypeFilter<$PrismaModel>
    _max?: NestedEnumMediaTypeFilter<$PrismaModel>
  }

  export type NestedIntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type NestedFloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }

  export type NestedEnumSuggestionTypeFilter<$PrismaModel = never> = {
    equals?: $Enums.SuggestionType | EnumSuggestionTypeFieldRefInput<$PrismaModel>
    in?: $Enums.SuggestionType[]
    notIn?: $Enums.SuggestionType[]
    not?: NestedEnumSuggestionTypeFilter<$PrismaModel> | $Enums.SuggestionType
  }

  export type NestedEnumSuggestionTypeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.SuggestionType | EnumSuggestionTypeFieldRefInput<$PrismaModel>
    in?: $Enums.SuggestionType[]
    notIn?: $Enums.SuggestionType[]
    not?: NestedEnumSuggestionTypeWithAggregatesFilter<$PrismaModel> | $Enums.SuggestionType
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumSuggestionTypeFilter<$PrismaModel>
    _max?: NestedEnumSuggestionTypeFilter<$PrismaModel>
  }
  export type NestedJsonNullableFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<NestedJsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>,
        Required<NestedJsonNullableFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>

  export type NestedJsonNullableFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type SessionNoteCreateWithoutTherapistInput = {
    id?: string
    sessionDate: Date | string
    noteType?: $Enums.NoteType
    subjective?: string | null
    objective?: string | null
    assessment?: string | null
    plan?: string | null
    sessionDuration?: number | null
    interventions?: string | null
    clientMood?: string | null
    isFinalized?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    client: ClientCreateNestedOneWithoutSessionNotesInput
    multimedia?: MultimediaFileCreateNestedManyWithoutSessionNoteInput
    aiSuggestions?: AiSuggestionCreateNestedManyWithoutSessionNoteInput
  }

  export type SessionNoteUncheckedCreateWithoutTherapistInput = {
    id?: string
    clientId: string
    sessionDate: Date | string
    noteType?: $Enums.NoteType
    subjective?: string | null
    objective?: string | null
    assessment?: string | null
    plan?: string | null
    sessionDuration?: number | null
    interventions?: string | null
    clientMood?: string | null
    isFinalized?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    multimedia?: MultimediaFileUncheckedCreateNestedManyWithoutSessionNoteInput
    aiSuggestions?: AiSuggestionUncheckedCreateNestedManyWithoutSessionNoteInput
  }

  export type SessionNoteCreateOrConnectWithoutTherapistInput = {
    where: SessionNoteWhereUniqueInput
    create: XOR<SessionNoteCreateWithoutTherapistInput, SessionNoteUncheckedCreateWithoutTherapistInput>
  }

  export type SessionNoteCreateManyTherapistInputEnvelope = {
    data: SessionNoteCreateManyTherapistInput | SessionNoteCreateManyTherapistInput[]
  }

  export type AuditLogCreateWithoutUserInput = {
    id?: string
    action: string
    resourceType: string
    resourceId: string
    details?: NullableJsonNullValueInput | InputJsonValue
    ipAddress?: string | null
    userAgent?: string | null
    timestamp?: Date | string
  }

  export type AuditLogUncheckedCreateWithoutUserInput = {
    id?: string
    action: string
    resourceType: string
    resourceId: string
    details?: NullableJsonNullValueInput | InputJsonValue
    ipAddress?: string | null
    userAgent?: string | null
    timestamp?: Date | string
  }

  export type AuditLogCreateOrConnectWithoutUserInput = {
    where: AuditLogWhereUniqueInput
    create: XOR<AuditLogCreateWithoutUserInput, AuditLogUncheckedCreateWithoutUserInput>
  }

  export type AuditLogCreateManyUserInputEnvelope = {
    data: AuditLogCreateManyUserInput | AuditLogCreateManyUserInput[]
  }

  export type SessionNoteUpsertWithWhereUniqueWithoutTherapistInput = {
    where: SessionNoteWhereUniqueInput
    update: XOR<SessionNoteUpdateWithoutTherapistInput, SessionNoteUncheckedUpdateWithoutTherapistInput>
    create: XOR<SessionNoteCreateWithoutTherapistInput, SessionNoteUncheckedCreateWithoutTherapistInput>
  }

  export type SessionNoteUpdateWithWhereUniqueWithoutTherapistInput = {
    where: SessionNoteWhereUniqueInput
    data: XOR<SessionNoteUpdateWithoutTherapistInput, SessionNoteUncheckedUpdateWithoutTherapistInput>
  }

  export type SessionNoteUpdateManyWithWhereWithoutTherapistInput = {
    where: SessionNoteScalarWhereInput
    data: XOR<SessionNoteUpdateManyMutationInput, SessionNoteUncheckedUpdateManyWithoutTherapistInput>
  }

  export type SessionNoteScalarWhereInput = {
    AND?: SessionNoteScalarWhereInput | SessionNoteScalarWhereInput[]
    OR?: SessionNoteScalarWhereInput[]
    NOT?: SessionNoteScalarWhereInput | SessionNoteScalarWhereInput[]
    id?: StringFilter<"SessionNote"> | string
    clientId?: StringFilter<"SessionNote"> | string
    therapistId?: StringFilter<"SessionNote"> | string
    sessionDate?: DateTimeFilter<"SessionNote"> | Date | string
    noteType?: EnumNoteTypeFilter<"SessionNote"> | $Enums.NoteType
    subjective?: StringNullableFilter<"SessionNote"> | string | null
    objective?: StringNullableFilter<"SessionNote"> | string | null
    assessment?: StringNullableFilter<"SessionNote"> | string | null
    plan?: StringNullableFilter<"SessionNote"> | string | null
    sessionDuration?: IntNullableFilter<"SessionNote"> | number | null
    interventions?: StringNullableFilter<"SessionNote"> | string | null
    clientMood?: StringNullableFilter<"SessionNote"> | string | null
    isFinalized?: BoolFilter<"SessionNote"> | boolean
    createdAt?: DateTimeFilter<"SessionNote"> | Date | string
    updatedAt?: DateTimeFilter<"SessionNote"> | Date | string
  }

  export type AuditLogUpsertWithWhereUniqueWithoutUserInput = {
    where: AuditLogWhereUniqueInput
    update: XOR<AuditLogUpdateWithoutUserInput, AuditLogUncheckedUpdateWithoutUserInput>
    create: XOR<AuditLogCreateWithoutUserInput, AuditLogUncheckedCreateWithoutUserInput>
  }

  export type AuditLogUpdateWithWhereUniqueWithoutUserInput = {
    where: AuditLogWhereUniqueInput
    data: XOR<AuditLogUpdateWithoutUserInput, AuditLogUncheckedUpdateWithoutUserInput>
  }

  export type AuditLogUpdateManyWithWhereWithoutUserInput = {
    where: AuditLogScalarWhereInput
    data: XOR<AuditLogUpdateManyMutationInput, AuditLogUncheckedUpdateManyWithoutUserInput>
  }

  export type AuditLogScalarWhereInput = {
    AND?: AuditLogScalarWhereInput | AuditLogScalarWhereInput[]
    OR?: AuditLogScalarWhereInput[]
    NOT?: AuditLogScalarWhereInput | AuditLogScalarWhereInput[]
    id?: StringFilter<"AuditLog"> | string
    userId?: StringFilter<"AuditLog"> | string
    action?: StringFilter<"AuditLog"> | string
    resourceType?: StringFilter<"AuditLog"> | string
    resourceId?: StringFilter<"AuditLog"> | string
    details?: JsonNullableFilter<"AuditLog">
    ipAddress?: StringNullableFilter<"AuditLog"> | string | null
    userAgent?: StringNullableFilter<"AuditLog"> | string | null
    timestamp?: DateTimeFilter<"AuditLog"> | Date | string
  }

  export type SessionNoteCreateWithoutClientInput = {
    id?: string
    sessionDate: Date | string
    noteType?: $Enums.NoteType
    subjective?: string | null
    objective?: string | null
    assessment?: string | null
    plan?: string | null
    sessionDuration?: number | null
    interventions?: string | null
    clientMood?: string | null
    isFinalized?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    therapist: UserCreateNestedOneWithoutSessionNotesInput
    multimedia?: MultimediaFileCreateNestedManyWithoutSessionNoteInput
    aiSuggestions?: AiSuggestionCreateNestedManyWithoutSessionNoteInput
  }

  export type SessionNoteUncheckedCreateWithoutClientInput = {
    id?: string
    therapistId: string
    sessionDate: Date | string
    noteType?: $Enums.NoteType
    subjective?: string | null
    objective?: string | null
    assessment?: string | null
    plan?: string | null
    sessionDuration?: number | null
    interventions?: string | null
    clientMood?: string | null
    isFinalized?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    multimedia?: MultimediaFileUncheckedCreateNestedManyWithoutSessionNoteInput
    aiSuggestions?: AiSuggestionUncheckedCreateNestedManyWithoutSessionNoteInput
  }

  export type SessionNoteCreateOrConnectWithoutClientInput = {
    where: SessionNoteWhereUniqueInput
    create: XOR<SessionNoteCreateWithoutClientInput, SessionNoteUncheckedCreateWithoutClientInput>
  }

  export type SessionNoteCreateManyClientInputEnvelope = {
    data: SessionNoteCreateManyClientInput | SessionNoteCreateManyClientInput[]
  }

  export type SessionNoteUpsertWithWhereUniqueWithoutClientInput = {
    where: SessionNoteWhereUniqueInput
    update: XOR<SessionNoteUpdateWithoutClientInput, SessionNoteUncheckedUpdateWithoutClientInput>
    create: XOR<SessionNoteCreateWithoutClientInput, SessionNoteUncheckedCreateWithoutClientInput>
  }

  export type SessionNoteUpdateWithWhereUniqueWithoutClientInput = {
    where: SessionNoteWhereUniqueInput
    data: XOR<SessionNoteUpdateWithoutClientInput, SessionNoteUncheckedUpdateWithoutClientInput>
  }

  export type SessionNoteUpdateManyWithWhereWithoutClientInput = {
    where: SessionNoteScalarWhereInput
    data: XOR<SessionNoteUpdateManyMutationInput, SessionNoteUncheckedUpdateManyWithoutClientInput>
  }

  export type ClientCreateWithoutSessionNotesInput = {
    id?: string
    firstName: string
    lastName: string
    dateOfBirth?: Date | string | null
    ehrId?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type ClientUncheckedCreateWithoutSessionNotesInput = {
    id?: string
    firstName: string
    lastName: string
    dateOfBirth?: Date | string | null
    ehrId?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type ClientCreateOrConnectWithoutSessionNotesInput = {
    where: ClientWhereUniqueInput
    create: XOR<ClientCreateWithoutSessionNotesInput, ClientUncheckedCreateWithoutSessionNotesInput>
  }

  export type UserCreateWithoutSessionNotesInput = {
    id?: string
    email: string
    name?: string | null
    role?: $Enums.UserRole
    passwordHash: string
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    auditLogs?: AuditLogCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateWithoutSessionNotesInput = {
    id?: string
    email: string
    name?: string | null
    role?: $Enums.UserRole
    passwordHash: string
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    auditLogs?: AuditLogUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserCreateOrConnectWithoutSessionNotesInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutSessionNotesInput, UserUncheckedCreateWithoutSessionNotesInput>
  }

  export type MultimediaFileCreateWithoutSessionNoteInput = {
    id?: string
    fileName: string
    originalName: string
    fileType: $Enums.MediaType
    fileSize: number
    s3Key: string
    s3Bucket: string
    description?: string | null
    aiDescription?: string | null
    uploadedAt?: Date | string
  }

  export type MultimediaFileUncheckedCreateWithoutSessionNoteInput = {
    id?: string
    fileName: string
    originalName: string
    fileType: $Enums.MediaType
    fileSize: number
    s3Key: string
    s3Bucket: string
    description?: string | null
    aiDescription?: string | null
    uploadedAt?: Date | string
  }

  export type MultimediaFileCreateOrConnectWithoutSessionNoteInput = {
    where: MultimediaFileWhereUniqueInput
    create: XOR<MultimediaFileCreateWithoutSessionNoteInput, MultimediaFileUncheckedCreateWithoutSessionNoteInput>
  }

  export type MultimediaFileCreateManySessionNoteInputEnvelope = {
    data: MultimediaFileCreateManySessionNoteInput | MultimediaFileCreateManySessionNoteInput[]
  }

  export type AiSuggestionCreateWithoutSessionNoteInput = {
    id?: string
    multimediaId?: string | null
    suggestionType: $Enums.SuggestionType
    originalText?: string | null
    suggestedText: string
    isAccepted?: boolean
    isRejected?: boolean
    createdAt?: Date | string
  }

  export type AiSuggestionUncheckedCreateWithoutSessionNoteInput = {
    id?: string
    multimediaId?: string | null
    suggestionType: $Enums.SuggestionType
    originalText?: string | null
    suggestedText: string
    isAccepted?: boolean
    isRejected?: boolean
    createdAt?: Date | string
  }

  export type AiSuggestionCreateOrConnectWithoutSessionNoteInput = {
    where: AiSuggestionWhereUniqueInput
    create: XOR<AiSuggestionCreateWithoutSessionNoteInput, AiSuggestionUncheckedCreateWithoutSessionNoteInput>
  }

  export type AiSuggestionCreateManySessionNoteInputEnvelope = {
    data: AiSuggestionCreateManySessionNoteInput | AiSuggestionCreateManySessionNoteInput[]
  }

  export type ClientUpsertWithoutSessionNotesInput = {
    update: XOR<ClientUpdateWithoutSessionNotesInput, ClientUncheckedUpdateWithoutSessionNotesInput>
    create: XOR<ClientCreateWithoutSessionNotesInput, ClientUncheckedCreateWithoutSessionNotesInput>
    where?: ClientWhereInput
  }

  export type ClientUpdateToOneWithWhereWithoutSessionNotesInput = {
    where?: ClientWhereInput
    data: XOR<ClientUpdateWithoutSessionNotesInput, ClientUncheckedUpdateWithoutSessionNotesInput>
  }

  export type ClientUpdateWithoutSessionNotesInput = {
    id?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    dateOfBirth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    ehrId?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ClientUncheckedUpdateWithoutSessionNotesInput = {
    id?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    dateOfBirth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    ehrId?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserUpsertWithoutSessionNotesInput = {
    update: XOR<UserUpdateWithoutSessionNotesInput, UserUncheckedUpdateWithoutSessionNotesInput>
    create: XOR<UserCreateWithoutSessionNotesInput, UserUncheckedCreateWithoutSessionNotesInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutSessionNotesInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutSessionNotesInput, UserUncheckedUpdateWithoutSessionNotesInput>
  }

  export type UserUpdateWithoutSessionNotesInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    passwordHash?: StringFieldUpdateOperationsInput | string
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    auditLogs?: AuditLogUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutSessionNotesInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    passwordHash?: StringFieldUpdateOperationsInput | string
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    auditLogs?: AuditLogUncheckedUpdateManyWithoutUserNestedInput
  }

  export type MultimediaFileUpsertWithWhereUniqueWithoutSessionNoteInput = {
    where: MultimediaFileWhereUniqueInput
    update: XOR<MultimediaFileUpdateWithoutSessionNoteInput, MultimediaFileUncheckedUpdateWithoutSessionNoteInput>
    create: XOR<MultimediaFileCreateWithoutSessionNoteInput, MultimediaFileUncheckedCreateWithoutSessionNoteInput>
  }

  export type MultimediaFileUpdateWithWhereUniqueWithoutSessionNoteInput = {
    where: MultimediaFileWhereUniqueInput
    data: XOR<MultimediaFileUpdateWithoutSessionNoteInput, MultimediaFileUncheckedUpdateWithoutSessionNoteInput>
  }

  export type MultimediaFileUpdateManyWithWhereWithoutSessionNoteInput = {
    where: MultimediaFileScalarWhereInput
    data: XOR<MultimediaFileUpdateManyMutationInput, MultimediaFileUncheckedUpdateManyWithoutSessionNoteInput>
  }

  export type MultimediaFileScalarWhereInput = {
    AND?: MultimediaFileScalarWhereInput | MultimediaFileScalarWhereInput[]
    OR?: MultimediaFileScalarWhereInput[]
    NOT?: MultimediaFileScalarWhereInput | MultimediaFileScalarWhereInput[]
    id?: StringFilter<"MultimediaFile"> | string
    sessionNoteId?: StringFilter<"MultimediaFile"> | string
    fileName?: StringFilter<"MultimediaFile"> | string
    originalName?: StringFilter<"MultimediaFile"> | string
    fileType?: EnumMediaTypeFilter<"MultimediaFile"> | $Enums.MediaType
    fileSize?: IntFilter<"MultimediaFile"> | number
    s3Key?: StringFilter<"MultimediaFile"> | string
    s3Bucket?: StringFilter<"MultimediaFile"> | string
    description?: StringNullableFilter<"MultimediaFile"> | string | null
    aiDescription?: StringNullableFilter<"MultimediaFile"> | string | null
    uploadedAt?: DateTimeFilter<"MultimediaFile"> | Date | string
  }

  export type AiSuggestionUpsertWithWhereUniqueWithoutSessionNoteInput = {
    where: AiSuggestionWhereUniqueInput
    update: XOR<AiSuggestionUpdateWithoutSessionNoteInput, AiSuggestionUncheckedUpdateWithoutSessionNoteInput>
    create: XOR<AiSuggestionCreateWithoutSessionNoteInput, AiSuggestionUncheckedCreateWithoutSessionNoteInput>
  }

  export type AiSuggestionUpdateWithWhereUniqueWithoutSessionNoteInput = {
    where: AiSuggestionWhereUniqueInput
    data: XOR<AiSuggestionUpdateWithoutSessionNoteInput, AiSuggestionUncheckedUpdateWithoutSessionNoteInput>
  }

  export type AiSuggestionUpdateManyWithWhereWithoutSessionNoteInput = {
    where: AiSuggestionScalarWhereInput
    data: XOR<AiSuggestionUpdateManyMutationInput, AiSuggestionUncheckedUpdateManyWithoutSessionNoteInput>
  }

  export type AiSuggestionScalarWhereInput = {
    AND?: AiSuggestionScalarWhereInput | AiSuggestionScalarWhereInput[]
    OR?: AiSuggestionScalarWhereInput[]
    NOT?: AiSuggestionScalarWhereInput | AiSuggestionScalarWhereInput[]
    id?: StringFilter<"AiSuggestion"> | string
    sessionNoteId?: StringFilter<"AiSuggestion"> | string
    multimediaId?: StringNullableFilter<"AiSuggestion"> | string | null
    suggestionType?: EnumSuggestionTypeFilter<"AiSuggestion"> | $Enums.SuggestionType
    originalText?: StringNullableFilter<"AiSuggestion"> | string | null
    suggestedText?: StringFilter<"AiSuggestion"> | string
    isAccepted?: BoolFilter<"AiSuggestion"> | boolean
    isRejected?: BoolFilter<"AiSuggestion"> | boolean
    createdAt?: DateTimeFilter<"AiSuggestion"> | Date | string
  }

  export type SessionNoteCreateWithoutMultimediaInput = {
    id?: string
    sessionDate: Date | string
    noteType?: $Enums.NoteType
    subjective?: string | null
    objective?: string | null
    assessment?: string | null
    plan?: string | null
    sessionDuration?: number | null
    interventions?: string | null
    clientMood?: string | null
    isFinalized?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    client: ClientCreateNestedOneWithoutSessionNotesInput
    therapist: UserCreateNestedOneWithoutSessionNotesInput
    aiSuggestions?: AiSuggestionCreateNestedManyWithoutSessionNoteInput
  }

  export type SessionNoteUncheckedCreateWithoutMultimediaInput = {
    id?: string
    clientId: string
    therapistId: string
    sessionDate: Date | string
    noteType?: $Enums.NoteType
    subjective?: string | null
    objective?: string | null
    assessment?: string | null
    plan?: string | null
    sessionDuration?: number | null
    interventions?: string | null
    clientMood?: string | null
    isFinalized?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    aiSuggestions?: AiSuggestionUncheckedCreateNestedManyWithoutSessionNoteInput
  }

  export type SessionNoteCreateOrConnectWithoutMultimediaInput = {
    where: SessionNoteWhereUniqueInput
    create: XOR<SessionNoteCreateWithoutMultimediaInput, SessionNoteUncheckedCreateWithoutMultimediaInput>
  }

  export type SessionNoteUpsertWithoutMultimediaInput = {
    update: XOR<SessionNoteUpdateWithoutMultimediaInput, SessionNoteUncheckedUpdateWithoutMultimediaInput>
    create: XOR<SessionNoteCreateWithoutMultimediaInput, SessionNoteUncheckedCreateWithoutMultimediaInput>
    where?: SessionNoteWhereInput
  }

  export type SessionNoteUpdateToOneWithWhereWithoutMultimediaInput = {
    where?: SessionNoteWhereInput
    data: XOR<SessionNoteUpdateWithoutMultimediaInput, SessionNoteUncheckedUpdateWithoutMultimediaInput>
  }

  export type SessionNoteUpdateWithoutMultimediaInput = {
    id?: StringFieldUpdateOperationsInput | string
    sessionDate?: DateTimeFieldUpdateOperationsInput | Date | string
    noteType?: EnumNoteTypeFieldUpdateOperationsInput | $Enums.NoteType
    subjective?: NullableStringFieldUpdateOperationsInput | string | null
    objective?: NullableStringFieldUpdateOperationsInput | string | null
    assessment?: NullableStringFieldUpdateOperationsInput | string | null
    plan?: NullableStringFieldUpdateOperationsInput | string | null
    sessionDuration?: NullableIntFieldUpdateOperationsInput | number | null
    interventions?: NullableStringFieldUpdateOperationsInput | string | null
    clientMood?: NullableStringFieldUpdateOperationsInput | string | null
    isFinalized?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    client?: ClientUpdateOneRequiredWithoutSessionNotesNestedInput
    therapist?: UserUpdateOneRequiredWithoutSessionNotesNestedInput
    aiSuggestions?: AiSuggestionUpdateManyWithoutSessionNoteNestedInput
  }

  export type SessionNoteUncheckedUpdateWithoutMultimediaInput = {
    id?: StringFieldUpdateOperationsInput | string
    clientId?: StringFieldUpdateOperationsInput | string
    therapistId?: StringFieldUpdateOperationsInput | string
    sessionDate?: DateTimeFieldUpdateOperationsInput | Date | string
    noteType?: EnumNoteTypeFieldUpdateOperationsInput | $Enums.NoteType
    subjective?: NullableStringFieldUpdateOperationsInput | string | null
    objective?: NullableStringFieldUpdateOperationsInput | string | null
    assessment?: NullableStringFieldUpdateOperationsInput | string | null
    plan?: NullableStringFieldUpdateOperationsInput | string | null
    sessionDuration?: NullableIntFieldUpdateOperationsInput | number | null
    interventions?: NullableStringFieldUpdateOperationsInput | string | null
    clientMood?: NullableStringFieldUpdateOperationsInput | string | null
    isFinalized?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    aiSuggestions?: AiSuggestionUncheckedUpdateManyWithoutSessionNoteNestedInput
  }

  export type SessionNoteCreateWithoutAiSuggestionsInput = {
    id?: string
    sessionDate: Date | string
    noteType?: $Enums.NoteType
    subjective?: string | null
    objective?: string | null
    assessment?: string | null
    plan?: string | null
    sessionDuration?: number | null
    interventions?: string | null
    clientMood?: string | null
    isFinalized?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    client: ClientCreateNestedOneWithoutSessionNotesInput
    therapist: UserCreateNestedOneWithoutSessionNotesInput
    multimedia?: MultimediaFileCreateNestedManyWithoutSessionNoteInput
  }

  export type SessionNoteUncheckedCreateWithoutAiSuggestionsInput = {
    id?: string
    clientId: string
    therapistId: string
    sessionDate: Date | string
    noteType?: $Enums.NoteType
    subjective?: string | null
    objective?: string | null
    assessment?: string | null
    plan?: string | null
    sessionDuration?: number | null
    interventions?: string | null
    clientMood?: string | null
    isFinalized?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    multimedia?: MultimediaFileUncheckedCreateNestedManyWithoutSessionNoteInput
  }

  export type SessionNoteCreateOrConnectWithoutAiSuggestionsInput = {
    where: SessionNoteWhereUniqueInput
    create: XOR<SessionNoteCreateWithoutAiSuggestionsInput, SessionNoteUncheckedCreateWithoutAiSuggestionsInput>
  }

  export type SessionNoteUpsertWithoutAiSuggestionsInput = {
    update: XOR<SessionNoteUpdateWithoutAiSuggestionsInput, SessionNoteUncheckedUpdateWithoutAiSuggestionsInput>
    create: XOR<SessionNoteCreateWithoutAiSuggestionsInput, SessionNoteUncheckedCreateWithoutAiSuggestionsInput>
    where?: SessionNoteWhereInput
  }

  export type SessionNoteUpdateToOneWithWhereWithoutAiSuggestionsInput = {
    where?: SessionNoteWhereInput
    data: XOR<SessionNoteUpdateWithoutAiSuggestionsInput, SessionNoteUncheckedUpdateWithoutAiSuggestionsInput>
  }

  export type SessionNoteUpdateWithoutAiSuggestionsInput = {
    id?: StringFieldUpdateOperationsInput | string
    sessionDate?: DateTimeFieldUpdateOperationsInput | Date | string
    noteType?: EnumNoteTypeFieldUpdateOperationsInput | $Enums.NoteType
    subjective?: NullableStringFieldUpdateOperationsInput | string | null
    objective?: NullableStringFieldUpdateOperationsInput | string | null
    assessment?: NullableStringFieldUpdateOperationsInput | string | null
    plan?: NullableStringFieldUpdateOperationsInput | string | null
    sessionDuration?: NullableIntFieldUpdateOperationsInput | number | null
    interventions?: NullableStringFieldUpdateOperationsInput | string | null
    clientMood?: NullableStringFieldUpdateOperationsInput | string | null
    isFinalized?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    client?: ClientUpdateOneRequiredWithoutSessionNotesNestedInput
    therapist?: UserUpdateOneRequiredWithoutSessionNotesNestedInput
    multimedia?: MultimediaFileUpdateManyWithoutSessionNoteNestedInput
  }

  export type SessionNoteUncheckedUpdateWithoutAiSuggestionsInput = {
    id?: StringFieldUpdateOperationsInput | string
    clientId?: StringFieldUpdateOperationsInput | string
    therapistId?: StringFieldUpdateOperationsInput | string
    sessionDate?: DateTimeFieldUpdateOperationsInput | Date | string
    noteType?: EnumNoteTypeFieldUpdateOperationsInput | $Enums.NoteType
    subjective?: NullableStringFieldUpdateOperationsInput | string | null
    objective?: NullableStringFieldUpdateOperationsInput | string | null
    assessment?: NullableStringFieldUpdateOperationsInput | string | null
    plan?: NullableStringFieldUpdateOperationsInput | string | null
    sessionDuration?: NullableIntFieldUpdateOperationsInput | number | null
    interventions?: NullableStringFieldUpdateOperationsInput | string | null
    clientMood?: NullableStringFieldUpdateOperationsInput | string | null
    isFinalized?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    multimedia?: MultimediaFileUncheckedUpdateManyWithoutSessionNoteNestedInput
  }

  export type UserCreateWithoutAuditLogsInput = {
    id?: string
    email: string
    name?: string | null
    role?: $Enums.UserRole
    passwordHash: string
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    sessionNotes?: SessionNoteCreateNestedManyWithoutTherapistInput
  }

  export type UserUncheckedCreateWithoutAuditLogsInput = {
    id?: string
    email: string
    name?: string | null
    role?: $Enums.UserRole
    passwordHash: string
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    sessionNotes?: SessionNoteUncheckedCreateNestedManyWithoutTherapistInput
  }

  export type UserCreateOrConnectWithoutAuditLogsInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutAuditLogsInput, UserUncheckedCreateWithoutAuditLogsInput>
  }

  export type UserUpsertWithoutAuditLogsInput = {
    update: XOR<UserUpdateWithoutAuditLogsInput, UserUncheckedUpdateWithoutAuditLogsInput>
    create: XOR<UserCreateWithoutAuditLogsInput, UserUncheckedCreateWithoutAuditLogsInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutAuditLogsInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutAuditLogsInput, UserUncheckedUpdateWithoutAuditLogsInput>
  }

  export type UserUpdateWithoutAuditLogsInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    passwordHash?: StringFieldUpdateOperationsInput | string
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    sessionNotes?: SessionNoteUpdateManyWithoutTherapistNestedInput
  }

  export type UserUncheckedUpdateWithoutAuditLogsInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    passwordHash?: StringFieldUpdateOperationsInput | string
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    sessionNotes?: SessionNoteUncheckedUpdateManyWithoutTherapistNestedInput
  }

  export type SessionNoteCreateManyTherapistInput = {
    id?: string
    clientId: string
    sessionDate: Date | string
    noteType?: $Enums.NoteType
    subjective?: string | null
    objective?: string | null
    assessment?: string | null
    plan?: string | null
    sessionDuration?: number | null
    interventions?: string | null
    clientMood?: string | null
    isFinalized?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AuditLogCreateManyUserInput = {
    id?: string
    action: string
    resourceType: string
    resourceId: string
    details?: NullableJsonNullValueInput | InputJsonValue
    ipAddress?: string | null
    userAgent?: string | null
    timestamp?: Date | string
  }

  export type SessionNoteUpdateWithoutTherapistInput = {
    id?: StringFieldUpdateOperationsInput | string
    sessionDate?: DateTimeFieldUpdateOperationsInput | Date | string
    noteType?: EnumNoteTypeFieldUpdateOperationsInput | $Enums.NoteType
    subjective?: NullableStringFieldUpdateOperationsInput | string | null
    objective?: NullableStringFieldUpdateOperationsInput | string | null
    assessment?: NullableStringFieldUpdateOperationsInput | string | null
    plan?: NullableStringFieldUpdateOperationsInput | string | null
    sessionDuration?: NullableIntFieldUpdateOperationsInput | number | null
    interventions?: NullableStringFieldUpdateOperationsInput | string | null
    clientMood?: NullableStringFieldUpdateOperationsInput | string | null
    isFinalized?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    client?: ClientUpdateOneRequiredWithoutSessionNotesNestedInput
    multimedia?: MultimediaFileUpdateManyWithoutSessionNoteNestedInput
    aiSuggestions?: AiSuggestionUpdateManyWithoutSessionNoteNestedInput
  }

  export type SessionNoteUncheckedUpdateWithoutTherapistInput = {
    id?: StringFieldUpdateOperationsInput | string
    clientId?: StringFieldUpdateOperationsInput | string
    sessionDate?: DateTimeFieldUpdateOperationsInput | Date | string
    noteType?: EnumNoteTypeFieldUpdateOperationsInput | $Enums.NoteType
    subjective?: NullableStringFieldUpdateOperationsInput | string | null
    objective?: NullableStringFieldUpdateOperationsInput | string | null
    assessment?: NullableStringFieldUpdateOperationsInput | string | null
    plan?: NullableStringFieldUpdateOperationsInput | string | null
    sessionDuration?: NullableIntFieldUpdateOperationsInput | number | null
    interventions?: NullableStringFieldUpdateOperationsInput | string | null
    clientMood?: NullableStringFieldUpdateOperationsInput | string | null
    isFinalized?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    multimedia?: MultimediaFileUncheckedUpdateManyWithoutSessionNoteNestedInput
    aiSuggestions?: AiSuggestionUncheckedUpdateManyWithoutSessionNoteNestedInput
  }

  export type SessionNoteUncheckedUpdateManyWithoutTherapistInput = {
    id?: StringFieldUpdateOperationsInput | string
    clientId?: StringFieldUpdateOperationsInput | string
    sessionDate?: DateTimeFieldUpdateOperationsInput | Date | string
    noteType?: EnumNoteTypeFieldUpdateOperationsInput | $Enums.NoteType
    subjective?: NullableStringFieldUpdateOperationsInput | string | null
    objective?: NullableStringFieldUpdateOperationsInput | string | null
    assessment?: NullableStringFieldUpdateOperationsInput | string | null
    plan?: NullableStringFieldUpdateOperationsInput | string | null
    sessionDuration?: NullableIntFieldUpdateOperationsInput | number | null
    interventions?: NullableStringFieldUpdateOperationsInput | string | null
    clientMood?: NullableStringFieldUpdateOperationsInput | string | null
    isFinalized?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AuditLogUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    action?: StringFieldUpdateOperationsInput | string
    resourceType?: StringFieldUpdateOperationsInput | string
    resourceId?: StringFieldUpdateOperationsInput | string
    details?: NullableJsonNullValueInput | InputJsonValue
    ipAddress?: NullableStringFieldUpdateOperationsInput | string | null
    userAgent?: NullableStringFieldUpdateOperationsInput | string | null
    timestamp?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AuditLogUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    action?: StringFieldUpdateOperationsInput | string
    resourceType?: StringFieldUpdateOperationsInput | string
    resourceId?: StringFieldUpdateOperationsInput | string
    details?: NullableJsonNullValueInput | InputJsonValue
    ipAddress?: NullableStringFieldUpdateOperationsInput | string | null
    userAgent?: NullableStringFieldUpdateOperationsInput | string | null
    timestamp?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AuditLogUncheckedUpdateManyWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    action?: StringFieldUpdateOperationsInput | string
    resourceType?: StringFieldUpdateOperationsInput | string
    resourceId?: StringFieldUpdateOperationsInput | string
    details?: NullableJsonNullValueInput | InputJsonValue
    ipAddress?: NullableStringFieldUpdateOperationsInput | string | null
    userAgent?: NullableStringFieldUpdateOperationsInput | string | null
    timestamp?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type SessionNoteCreateManyClientInput = {
    id?: string
    therapistId: string
    sessionDate: Date | string
    noteType?: $Enums.NoteType
    subjective?: string | null
    objective?: string | null
    assessment?: string | null
    plan?: string | null
    sessionDuration?: number | null
    interventions?: string | null
    clientMood?: string | null
    isFinalized?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type SessionNoteUpdateWithoutClientInput = {
    id?: StringFieldUpdateOperationsInput | string
    sessionDate?: DateTimeFieldUpdateOperationsInput | Date | string
    noteType?: EnumNoteTypeFieldUpdateOperationsInput | $Enums.NoteType
    subjective?: NullableStringFieldUpdateOperationsInput | string | null
    objective?: NullableStringFieldUpdateOperationsInput | string | null
    assessment?: NullableStringFieldUpdateOperationsInput | string | null
    plan?: NullableStringFieldUpdateOperationsInput | string | null
    sessionDuration?: NullableIntFieldUpdateOperationsInput | number | null
    interventions?: NullableStringFieldUpdateOperationsInput | string | null
    clientMood?: NullableStringFieldUpdateOperationsInput | string | null
    isFinalized?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    therapist?: UserUpdateOneRequiredWithoutSessionNotesNestedInput
    multimedia?: MultimediaFileUpdateManyWithoutSessionNoteNestedInput
    aiSuggestions?: AiSuggestionUpdateManyWithoutSessionNoteNestedInput
  }

  export type SessionNoteUncheckedUpdateWithoutClientInput = {
    id?: StringFieldUpdateOperationsInput | string
    therapistId?: StringFieldUpdateOperationsInput | string
    sessionDate?: DateTimeFieldUpdateOperationsInput | Date | string
    noteType?: EnumNoteTypeFieldUpdateOperationsInput | $Enums.NoteType
    subjective?: NullableStringFieldUpdateOperationsInput | string | null
    objective?: NullableStringFieldUpdateOperationsInput | string | null
    assessment?: NullableStringFieldUpdateOperationsInput | string | null
    plan?: NullableStringFieldUpdateOperationsInput | string | null
    sessionDuration?: NullableIntFieldUpdateOperationsInput | number | null
    interventions?: NullableStringFieldUpdateOperationsInput | string | null
    clientMood?: NullableStringFieldUpdateOperationsInput | string | null
    isFinalized?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    multimedia?: MultimediaFileUncheckedUpdateManyWithoutSessionNoteNestedInput
    aiSuggestions?: AiSuggestionUncheckedUpdateManyWithoutSessionNoteNestedInput
  }

  export type SessionNoteUncheckedUpdateManyWithoutClientInput = {
    id?: StringFieldUpdateOperationsInput | string
    therapistId?: StringFieldUpdateOperationsInput | string
    sessionDate?: DateTimeFieldUpdateOperationsInput | Date | string
    noteType?: EnumNoteTypeFieldUpdateOperationsInput | $Enums.NoteType
    subjective?: NullableStringFieldUpdateOperationsInput | string | null
    objective?: NullableStringFieldUpdateOperationsInput | string | null
    assessment?: NullableStringFieldUpdateOperationsInput | string | null
    plan?: NullableStringFieldUpdateOperationsInput | string | null
    sessionDuration?: NullableIntFieldUpdateOperationsInput | number | null
    interventions?: NullableStringFieldUpdateOperationsInput | string | null
    clientMood?: NullableStringFieldUpdateOperationsInput | string | null
    isFinalized?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type MultimediaFileCreateManySessionNoteInput = {
    id?: string
    fileName: string
    originalName: string
    fileType: $Enums.MediaType
    fileSize: number
    s3Key: string
    s3Bucket: string
    description?: string | null
    aiDescription?: string | null
    uploadedAt?: Date | string
  }

  export type AiSuggestionCreateManySessionNoteInput = {
    id?: string
    multimediaId?: string | null
    suggestionType: $Enums.SuggestionType
    originalText?: string | null
    suggestedText: string
    isAccepted?: boolean
    isRejected?: boolean
    createdAt?: Date | string
  }

  export type MultimediaFileUpdateWithoutSessionNoteInput = {
    id?: StringFieldUpdateOperationsInput | string
    fileName?: StringFieldUpdateOperationsInput | string
    originalName?: StringFieldUpdateOperationsInput | string
    fileType?: EnumMediaTypeFieldUpdateOperationsInput | $Enums.MediaType
    fileSize?: IntFieldUpdateOperationsInput | number
    s3Key?: StringFieldUpdateOperationsInput | string
    s3Bucket?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    aiDescription?: NullableStringFieldUpdateOperationsInput | string | null
    uploadedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type MultimediaFileUncheckedUpdateWithoutSessionNoteInput = {
    id?: StringFieldUpdateOperationsInput | string
    fileName?: StringFieldUpdateOperationsInput | string
    originalName?: StringFieldUpdateOperationsInput | string
    fileType?: EnumMediaTypeFieldUpdateOperationsInput | $Enums.MediaType
    fileSize?: IntFieldUpdateOperationsInput | number
    s3Key?: StringFieldUpdateOperationsInput | string
    s3Bucket?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    aiDescription?: NullableStringFieldUpdateOperationsInput | string | null
    uploadedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type MultimediaFileUncheckedUpdateManyWithoutSessionNoteInput = {
    id?: StringFieldUpdateOperationsInput | string
    fileName?: StringFieldUpdateOperationsInput | string
    originalName?: StringFieldUpdateOperationsInput | string
    fileType?: EnumMediaTypeFieldUpdateOperationsInput | $Enums.MediaType
    fileSize?: IntFieldUpdateOperationsInput | number
    s3Key?: StringFieldUpdateOperationsInput | string
    s3Bucket?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    aiDescription?: NullableStringFieldUpdateOperationsInput | string | null
    uploadedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AiSuggestionUpdateWithoutSessionNoteInput = {
    id?: StringFieldUpdateOperationsInput | string
    multimediaId?: NullableStringFieldUpdateOperationsInput | string | null
    suggestionType?: EnumSuggestionTypeFieldUpdateOperationsInput | $Enums.SuggestionType
    originalText?: NullableStringFieldUpdateOperationsInput | string | null
    suggestedText?: StringFieldUpdateOperationsInput | string
    isAccepted?: BoolFieldUpdateOperationsInput | boolean
    isRejected?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AiSuggestionUncheckedUpdateWithoutSessionNoteInput = {
    id?: StringFieldUpdateOperationsInput | string
    multimediaId?: NullableStringFieldUpdateOperationsInput | string | null
    suggestionType?: EnumSuggestionTypeFieldUpdateOperationsInput | $Enums.SuggestionType
    originalText?: NullableStringFieldUpdateOperationsInput | string | null
    suggestedText?: StringFieldUpdateOperationsInput | string
    isAccepted?: BoolFieldUpdateOperationsInput | boolean
    isRejected?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AiSuggestionUncheckedUpdateManyWithoutSessionNoteInput = {
    id?: StringFieldUpdateOperationsInput | string
    multimediaId?: NullableStringFieldUpdateOperationsInput | string | null
    suggestionType?: EnumSuggestionTypeFieldUpdateOperationsInput | $Enums.SuggestionType
    originalText?: NullableStringFieldUpdateOperationsInput | string | null
    suggestedText?: StringFieldUpdateOperationsInput | string
    isAccepted?: BoolFieldUpdateOperationsInput | boolean
    isRejected?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }



  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}