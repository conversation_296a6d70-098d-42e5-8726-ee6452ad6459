import { NextRequest, NextResponse } from 'next/server';
import { generateAISuggestion, AiSuggestionRequest } from '@/lib/ai';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, context, mediaType, clientAge, sessionGoals, clientHistory, sessionDuration } = body;

    // Validate required fields
    if (!type) {
      return NextResponse.json(
        { error: 'Missing required field: type' },
        { status: 400 }
      );
    }

    // Build the AI request
    const aiRequest: AiSuggestionRequest = {
      type,
      context: context || '',
      mediaType,
      clientAge,
      sessionGoals,
      clientHistory,
      sessionDuration
    };

    // Use AI API (Groq or OpenAI with fallback to mock)
    const response = await generateAISuggestion(aiRequest);

    return NextResponse.json({
      suggestion: response.suggestion,
      confidence: response.confidence,
      alternatives: response.alternatives
    });

  } catch (error) {
    console.error('Error generating AI suggestion:', error);
    return NextResponse.json(
      { error: 'Failed to generate AI suggestion' },
      { status: 500 }
    );
  }
}

async function generateMockAIResponse(
  type: string, 
  context: string, 
  mediaType?: string
): Promise<{
  suggestion: string;
  confidence: number;
  alternatives?: string[];
}> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  const responses = {
    DESCRIPTION: {
      IMAGE: {
        suggestion: "Client created a mixed-media artwork featuring bold, expressive brushstrokes in warm colors (reds, oranges, yellows). The composition shows dynamic movement with overlapping forms and varied textures. The client demonstrated sustained focus and deliberate color choices throughout the 25-minute creative process.",
        confidence: 0.85,
        alternatives: [
          "Artwork displays vibrant color palette with energetic brushwork, suggesting positive emotional engagement and creative expression.",
          "Client's painting demonstrates confident mark-making and intentional composition, indicating therapeutic progress in self-expression."
        ]
      },
      VIDEO: {
        suggestion: "Client engaged in fluid movement therapy, demonstrating progressive relaxation and increased range of motion. Initial movements were tentative and restricted, evolving to more expansive and confident gestures. Client maintained appropriate spatial awareness and responded positively to musical cues.",
        confidence: 0.82,
        alternatives: [
          "Movement session showed gradual increase in client's comfort level and willingness to explore physical expression.",
          "Client demonstrated improved body awareness and emotional regulation through structured movement activities."
        ]
      },
      AUDIO: {
        suggestion: "Client's musical expression included rhythmic drumming with steady tempo and occasional dynamic variations. Demonstrated improved coordination and timing compared to previous sessions. Client verbalized positive feelings about the musical experience and showed increased confidence in creative expression.",
        confidence: 0.88,
        alternatives: [
          "Audio recording captures client's growing musical confidence and improved rhythmic stability.",
          "Musical session demonstrates client's enhanced focus and emotional regulation through sound exploration."
        ]
      }
    },
    INTERPRETATION: {
      IMAGE: {
        suggestion: "The client's use of warm, vibrant colors may indicate improved mood and emotional accessibility. The bold, confident brushstrokes suggest increased self-assurance and willingness to take creative risks. The central composition with radiating elements could represent the client's growing sense of personal agency and expanding social connections.",
        confidence: 0.78,
        alternatives: [
          "Artwork suggests positive therapeutic progress with increased emotional expression and creative confidence.",
          "Color choices and composition indicate client's developing sense of empowerment and emotional regulation."
        ]
      },
      DEFAULT: {
        suggestion: "Client's creative expression demonstrates therapeutic progress in emotional regulation and self-awareness. The willingness to engage in creative risk-taking suggests increased confidence and trust in the therapeutic process. Observable improvements in focus and sustained attention indicate positive response to creative arts interventions.",
        confidence: 0.80
      }
    },
    OBJECTIVE: {
      suggestion: "Client arrived punctually and engaged cooperatively in session activities. Demonstrated sustained attention for 35 minutes during art-making process. Made appropriate eye contact and responded verbally to therapeutic prompts. Exhibited calm demeanor with no observable signs of distress. Completed creative task independently with minimal guidance.",
      confidence: 0.90,
      alternatives: [
        "Client showed active participation and positive engagement throughout the 45-minute session with appropriate social interaction.",
        "Observable behaviors included focused attention, cooperative attitude, and willingness to discuss creative process and emotional content."
      ]
    },
    ASSESSMENT: {
      suggestion: "Client continues to demonstrate progress in emotional expression and self-regulation through creative arts interventions. Increased confidence in artistic choices and willingness to discuss emotional content of creative work indicates positive therapeutic alliance. Current presentation suggests stable mood with continued engagement in treatment goals.",
      confidence: 0.85,
      alternatives: [
        "Client shows sustained improvement in creative self-expression and emotional processing capabilities.",
        "Therapeutic progress evident in client's increased openness and willingness to explore emotional themes through art."
      ]
    },
    PLAN: {
      suggestion: "Continue weekly creative arts therapy sessions focusing on emotional expression through visual media. Introduce new artistic materials (clay, pastels) to expand creative vocabulary. Schedule follow-up assessment in 4 weeks to evaluate progress toward treatment goals. Consider group therapy referral to enhance social skills development.",
      confidence: 0.87,
      alternatives: [
        "Maintain current treatment frequency with gradual introduction of more challenging creative tasks to build confidence.",
        "Continue individual sessions while exploring opportunities for peer interaction through structured creative activities."
      ]
    }
  };

  // Get response based on type and media type
  const typeResponses = responses[type as keyof typeof responses];
  if (!typeResponses) {
    return {
      suggestion: "AI-generated clinical suggestion based on session context and observations.",
      confidence: 0.75
    };
  }

  if (typeof typeResponses === 'object' && 'suggestion' in typeResponses) {
    return typeResponses;
  }

  const mediaResponse = typeResponses[mediaType as keyof typeof typeResponses] || typeResponses['DEFAULT'];
  return mediaResponse || {
    suggestion: "AI-generated clinical suggestion based on session context and observations.",
    confidence: 0.75
  };
}
