# VistaNotes Production Deployment Guide

## 🚀 Quick Start Deployment

### Prerequisites
- Docker and Docker Compose installed
- Domain name configured
- SSL certificates ready
- Environment variables configured

### 1. Environment Setup

Create a `.env.production` file with your production values:

```bash
# Copy the template
cp .env.production.example .env.production

# Edit with your values
nano .env.production
```

**Required Environment Variables:**
```bash
NODE_ENV=production
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your-secure-secret-key
DATABASE_URL=******************************/vistanotes
GROQ_API_KEY=your-groq-api-key
POSTGRES_PASSWORD=your-secure-db-password
```

### 2. SSL Certificate Setup

Place your SSL certificates in the `ssl/` directory:
```bash
mkdir ssl
cp your-cert.pem ssl/cert.pem
cp your-key.pem ssl/key.pem
```

### 3. Deploy with Docker Compose

```bash
# Build and start all services
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f app
```

### 4. Database Migration

```bash
# Run the migration script
chmod +x scripts/migrate-to-production.sh
./scripts/migrate-to-production.sh
```

### 5. Verify Deployment

```bash
# Health check
curl https://your-domain.com/api/health

# Test authentication
curl -X POST https://your-domain.com/api/auth/signin \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'
```

## 🔧 Advanced Configuration

### Scaling the Application

```bash
# Scale to 3 app instances
docker-compose up -d --scale app=3

# Update nginx upstream configuration
# Edit nginx.conf to include multiple app servers
```

### Database Backup Configuration

```bash
# Manual backup
docker-compose exec backup pg_dump -h db -U vistanotes vistanotes > backup.sql

# Restore from backup
docker-compose exec db psql -h db -U vistanotes vistanotes < backup.sql
```

### Monitoring Setup

1. **Application Metrics**: Available at `/api/health`
2. **Database Monitoring**: PostgreSQL stats enabled
3. **Nginx Logs**: Available in `logs/` directory
4. **Container Monitoring**: Use `docker stats`

### Performance Optimization

1. **Enable Redis Caching**:
   ```bash
   # Redis is included in docker-compose.yml
   # Configure REDIS_URL in environment
   REDIS_URL=redis://redis:6379
   ```

2. **CDN Configuration**:
   ```bash
   # Set CDN URL for static assets
   CDN_URL=https://cdn.your-domain.com
   ```

3. **Database Optimization**:
   ```sql
   -- Create indexes for better performance
   CREATE INDEX idx_session_notes_client_id ON session_notes(client_id);
   CREATE INDEX idx_session_notes_therapist_id ON session_notes(therapist_id);
   CREATE INDEX idx_session_notes_date ON session_notes(session_date);
   ```

## 🔒 Security Configuration

### HIPAA Compliance Checklist

- [ ] SSL/TLS encryption enabled
- [ ] Database encryption at rest
- [ ] Audit logging enabled
- [ ] Access controls configured
- [ ] Regular security updates
- [ ] Backup encryption
- [ ] Network security (firewall)
- [ ] User authentication (2FA recommended)

### Security Headers

The nginx configuration includes:
- Content Security Policy
- HSTS (HTTP Strict Transport Security)
- X-Frame-Options
- X-Content-Type-Options
- Referrer Policy

### Rate Limiting

- API endpoints: 10 requests/second
- Login endpoints: 1 request/second
- Burst allowance: 20 requests

## 📊 Monitoring & Maintenance

### Health Checks

```bash
# Application health
curl https://your-domain.com/api/health

# Database health
docker-compose exec db pg_isready -U vistanotes

# Redis health
docker-compose exec redis redis-cli ping
```

### Log Management

```bash
# Application logs
docker-compose logs -f app

# Database logs
docker-compose logs -f db

# Nginx access logs
tail -f logs/access.log

# Nginx error logs
tail -f logs/error.log
```

### Backup Strategy

1. **Automated Daily Backups**: Configured in docker-compose.yml
2. **Retention Policy**: 30 days (configurable)
3. **Backup Location**: `./backups/` directory
4. **Backup Verification**: Test restore monthly

### Updates and Maintenance

```bash
# Update application
git pull origin main
docker-compose build app
docker-compose up -d app

# Update database schema
docker-compose exec app npx prisma db push

# Update dependencies
docker-compose exec app npm update
docker-compose build app
docker-compose up -d app
```

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```bash
   # Check database status
   docker-compose exec db pg_isready -U vistanotes
   
   # Check connection string
   echo $DATABASE_URL
   ```

2. **SSL Certificate Issues**
   ```bash
   # Verify certificate files
   ls -la ssl/
   
   # Test certificate
   openssl x509 -in ssl/cert.pem -text -noout
   ```

3. **Application Won't Start**
   ```bash
   # Check logs
   docker-compose logs app
   
   # Check environment variables
   docker-compose exec app env | grep -E "(NODE_ENV|DATABASE_URL|GROQ_API_KEY)"
   ```

4. **Performance Issues**
   ```bash
   # Check resource usage
   docker stats
   
   # Check database performance
   docker-compose exec db psql -U vistanotes -c "SELECT * FROM pg_stat_activity;"
   ```

### Emergency Procedures

1. **Service Restart**
   ```bash
   docker-compose restart app
   ```

2. **Database Recovery**
   ```bash
   # Stop services
   docker-compose down
   
   # Restore from backup
   docker-compose up -d db
   docker-compose exec db psql -U vistanotes vistanotes < backups/latest_backup.sql
   
   # Start all services
   docker-compose up -d
   ```

3. **Rollback Deployment**
   ```bash
   # Revert to previous version
   git checkout previous-tag
   docker-compose build app
   docker-compose up -d app
   ```

## 📞 Support

For deployment support:
1. Check logs first: `docker-compose logs -f`
2. Verify environment variables
3. Test individual components
4. Review this deployment guide
5. Check application health endpoint

## 🔄 CI/CD Integration

Example GitHub Actions workflow available in `.github/workflows/deploy.yml`

For automated deployments, configure:
1. Docker registry access
2. Production server SSH access
3. Environment variable secrets
4. Database migration automation
