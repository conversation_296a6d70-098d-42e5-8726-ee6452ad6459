const CHUNK_PUBLIC_PATH = "server/app/api/ai/suggestions/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_formdata-node_lib_esm_fileFromPath_c0ea7ea4.js");
runtime.loadChunk("server/chunks/node_modules_next_12e32c7c._.js");
runtime.loadChunk("server/chunks/node_modules_openai_1a6eb0e6._.js");
runtime.loadChunk("server/chunks/node_modules_tr46_816df9d9._.js");
runtime.loadChunk("server/chunks/node_modules_e7bac7e7._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__8114aeaf._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/ai/suggestions/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/ai/suggestions/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/ai/suggestions/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
