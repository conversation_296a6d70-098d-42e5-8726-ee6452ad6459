@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* VistaNotes UI/UX Improvements for Better Accessibility */

/* Improved form inputs with better contrast and readability */
.form-input {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm
         placeholder-gray-500 text-gray-900 bg-white
         focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500
         disabled:bg-gray-50 disabled:text-gray-500 disabled:border-gray-200
         transition-colors duration-200;
}

.form-input::placeholder {
  color: #6b7280 !important; /* Darker gray for better readability */
  opacity: 1 !important;
}

.form-textarea {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm
         placeholder-gray-500 text-gray-900 bg-white
         focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500
         disabled:bg-gray-50 disabled:text-gray-500 disabled:border-gray-200
         transition-colors duration-200 min-h-[100px];
  resize: vertical;
}

.form-textarea::placeholder {
  color: #6b7280 !important; /* Darker gray for better readability */
  opacity: 1 !important;
}

.form-select {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm
         text-gray-900 bg-white
         focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500
         disabled:bg-gray-50 disabled:text-gray-500 disabled:border-gray-200
         transition-colors duration-200;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-error {
  @apply mt-1 text-sm text-red-600;
}

.form-help {
  @apply mt-1 text-sm text-gray-600;
}

/* Button improvements with better contrast */
.btn-primary {
  @apply inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm
         text-sm font-medium text-white bg-indigo-600
         hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500
         disabled:opacity-50 disabled:cursor-not-allowed
         transition-colors duration-200;
}

.btn-secondary {
  @apply inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm
         text-sm font-medium text-gray-700 bg-white
         hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500
         disabled:opacity-50 disabled:cursor-not-allowed
         transition-colors duration-200;
}

.btn-success {
  @apply inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm
         text-sm font-medium text-white bg-green-600
         hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500
         disabled:opacity-50 disabled:cursor-not-allowed
         transition-colors duration-200;
}

/* Card and container improvements */
.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.card-header {
  @apply border-b border-gray-200 pb-4 mb-4;
}

.card-title {
  @apply text-lg font-semibold text-gray-900;
}

.card-subtitle {
  @apply text-sm text-gray-600 mt-1;
}

/* Status indicators with better contrast */
.status-active {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
         bg-green-100 text-green-800 border border-green-200;
}

.status-inactive {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
         bg-gray-100 text-gray-800 border border-gray-200;
}

.status-completed {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
         bg-blue-100 text-blue-800 border border-blue-200;
}

.status-draft {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
         bg-yellow-100 text-yellow-800 border border-yellow-200;
}

/* Loading states */
.loading-spinner {
  @apply animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600;
}

.loading-skeleton {
  @apply animate-pulse bg-gray-200 rounded;
}

/* Focus improvements for accessibility */
button:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible,
a:focus-visible {
  @apply ring-2 ring-offset-2 ring-indigo-500 outline-none;
}

/* Improved text contrast for better readability */
input::placeholder,
textarea::placeholder {
  color: #6b7280 !important; /* Gray-500 for better contrast */
  opacity: 1 !important;
}

/* Ensure proper contrast for disabled states */
input:disabled,
textarea:disabled,
select:disabled {
  background-color: #f9fafb !important; /* Gray-50 */
  color: #6b7280 !important; /* Gray-500 */
  border-color: #e5e7eb !important; /* Gray-200 */
}
