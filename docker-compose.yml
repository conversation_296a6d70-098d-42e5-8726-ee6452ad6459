version: '3.8'

services:
  # VistaNotes Application
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://vistanotes:${POSTGRES_PASSWORD}@db:5432/vistanotes
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - GROQ_API_KEY=${GROQ_API_KEY}
      - AI_PROVIDER=groq
    depends_on:
      - db
      - redis
    volumes:
      - ./uploads:/app/uploads
    restart: unless-stopped
    networks:
      - vistanotes-network

  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=vistanotes
      - POSTGRES_USER=vistanotes
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - vistanotes-network
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=200

  # Redis for Caching and Sessions
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - vistanotes-network
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - ./logs:/var/log/nginx
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - vistanotes-network

  # Database Backup Service
  backup:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=vistanotes
      - POSTGRES_USER=vistanotes
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - PGPASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - ./backups:/backups
      - ./scripts:/scripts
    depends_on:
      - db
    restart: "no"
    networks:
      - vistanotes-network
    command: >
      sh -c "
      while true; do
        sleep 86400
        pg_dump -h db -U vistanotes vistanotes > /backups/backup_$(date +%Y%m%d_%H%M%S).sql
        find /backups -name '*.sql' -mtime +30 -delete
      done
      "

volumes:
  postgres_data:
  redis_data:

networks:
  vistanotes-network:
    driver: bridge
