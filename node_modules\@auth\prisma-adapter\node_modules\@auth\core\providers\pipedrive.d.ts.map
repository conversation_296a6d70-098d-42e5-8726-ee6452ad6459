{"version": 3, "file": "pipedrive.d.ts", "sourceRoot": "", "sources": ["../src/providers/pipedrive.ts"], "names": [], "mappings": "AAAA;;;;;;;;;GASG;AACH,OAAO,KAAK,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,YAAY,CAAA;AAE9D,MAAM,WAAW,gBAAiB,SAAQ,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAC3D,OAAO,EAAE,OAAO,CAAA;IAChB,IAAI,EAAE;QACJ,EAAE,EAAE,MAAM,CAAA;QACV,IAAI,EAAE,MAAM,CAAA;QACZ,gBAAgB,CAAC,EAAE,MAAM,CAAA;QACzB,MAAM,CAAC,EAAE,MAAM,CAAA;QACf,IAAI,CAAC,EAAE,MAAM,CAAA;QACb,KAAK,EAAE,MAAM,CAAA;QACb,KAAK,CAAC,EAAE,MAAM,CAAA;QACd,SAAS,CAAC,EAAE,OAAO,CAAA;QACnB,UAAU,CAAC,EAAE,IAAI,CAAA;QACjB,OAAO,CAAC,EAAE,IAAI,CAAA;QACd,QAAQ,CAAC,EAAE,IAAI,CAAA;QACf,qBAAqB,CAAC,EAAE,MAAM,CAAA;QAC9B,mBAAmB,CAAC,EAAE,OAAO,CAAA;QAC7B,QAAQ,CAAC,EAAE,MAAM,CAAA;QACjB,WAAW,CAAC,EAAE,OAAO,CAAA;QACrB,aAAa,CAAC,EAAE,MAAM,CAAA;QACtB,eAAe,CAAC,EAAE,MAAM,CAAA;QACxB,OAAO,CAAC,EAAE,MAAM,CAAA;QAChB,QAAQ,CAAC,EAAE,MAAM,CAAA;QACjB,MAAM,CAAC,EAAE,OAAO,CAAA;QAChB,UAAU,CAAC,EAAE,MAAM,CAAA;QACnB,YAAY,CAAC,EAAE,MAAM,CAAA;QACrB,cAAc,CAAC,EAAE,MAAM,CAAA;QACvB,eAAe,CAAC,EAAE,MAAM,CAAA;QACxB,gBAAgB,CAAC,EAAE,MAAM,CAAA;QACzB,QAAQ,CAAC,EAAE;YACT,aAAa,CAAC,EAAE,MAAM,CAAA;YACtB,YAAY,CAAC,EAAE,MAAM,CAAA;SACtB,CAAA;KACF,CAAA;CACF;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmDG;AACH,MAAM,CAAC,OAAO,UAAU,SAAS,CAAC,CAAC,SAAS,gBAAgB,EAC1D,OAAO,EAAE,eAAe,CAAC,CAAC,CAAC,GAC1B,WAAW,CAAC,CAAC,CAAC,CAkBhB"}