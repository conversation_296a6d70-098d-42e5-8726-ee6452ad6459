module.exports = {

"[project]/node_modules/formdata-node/lib/esm/fileFromPath.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/[root-of-the-server]__e14f4b46._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/formdata-node/lib/esm/fileFromPath.js [app-route] (ecmascript)");
    });
});
}}),

};