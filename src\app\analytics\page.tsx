"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  <PERSON>lt<PERSON>, 
  Legend, 
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell
} from "recharts";
import { 
  TrendingUp, 
  Users, 
  FileText, 
  Clock, 
  Activity,
  AlertTriangle,
  CheckCircle,
  BarChart3
} from "lucide-react";

export default function Analytics() {
  const [timeRange, setTimeRange] = useState('month');
  const [isLoading, setIsLoading] = useState(true);
  
  // Mock analytics data - in production, this would come from APIs
  const [analyticsData] = useState({
    overview: {
      totalClients: 24,
      activeClients: 18,
      totalSessions: 156,
      avgSessionDuration: 47,
      completionRate: 94,
      aiUsageRate: 78
    },
    sessionTrends: [
      { month: 'Jan', sessions: 45, duration: 46 },
      { month: 'Feb', sessions: 52, duration: 48 },
      { month: 'Mar', sessions: 48, duration: 45 },
      { month: 'Apr', sessions: 59, duration: 49 },
      { month: 'May', sessions: 61, duration: 47 },
      { month: 'Jun', sessions: 58, duration: 46 }
    ],
    aiUsage: [
      { feature: 'Objective Notes', usage: 85, timeSaved: 12 },
      { feature: 'Assessment', usage: 72, timeSaved: 8 },
      { feature: 'Treatment Plans', usage: 68, timeSaved: 15 },
      { feature: 'Media Analysis', usage: 45, timeSaved: 20 }
    ],
    clientProgress: [
      { metric: 'Emotional Expression', improvement: 23 },
      { metric: 'Creative Engagement', improvement: 18 },
      { metric: 'Self-Awareness', improvement: 31 },
      { metric: 'Coping Skills', improvement: 15 }
    ],
    noteTypes: [
      { name: 'SOAP', value: 78, color: '#4f46e5' },
      { name: 'DAP', value: 15, color: '#06b6d4' },
      { name: 'BIRP', value: 7, color: '#10b981' }
    ],
    performance: {
      avgNoteTime: 8.5, // minutes
      aiAcceptanceRate: 82,
      errorRate: 0.3,
      systemUptime: 99.8
    }
  });

  useEffect(() => {
    // Simulate loading
    setTimeout(() => setIsLoading(false), 1000);
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-8"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="bg-white rounded-lg shadow p-6">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="flex-shrink-0 mr-8">
                <h1 className="text-2xl font-bold text-gray-900">VistaNotes</h1>
              </Link>
              <nav className="hidden md:flex space-x-8">
                <Link href="/dashboard" className="text-gray-500 hover:text-gray-900">
                  Dashboard
                </Link>
                <Link href="/clients" className="text-gray-500 hover:text-gray-900">
                  Clients
                </Link>
                <Link href="/notes" className="text-gray-500 hover:text-gray-900">
                  Session Notes
                </Link>
                <Link href="/analytics" className="text-indigo-600 font-medium">
                  Analytics
                </Link>
              </nav>
            </div>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              <option value="week">Last Week</option>
              <option value="month">Last Month</option>
              <option value="quarter">Last Quarter</option>
              <option value="year">Last Year</option>
            </select>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">Practice Analytics</h2>
          <p className="text-gray-600">Insights into your Creative Arts Therapy practice</p>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Users className="h-8 w-8 text-indigo-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Active Clients</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analyticsData.overview.activeClients}
                </p>
                <p className="text-xs text-gray-500">
                  of {analyticsData.overview.totalClients} total
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FileText className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Sessions This Month</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analyticsData.overview.totalSessions}
                </p>
                <p className="text-xs text-green-600 flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +12% from last month
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Avg Note Time</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analyticsData.performance.avgNoteTime}m
                </p>
                <p className="text-xs text-green-600">
                  -3.2m with AI assistance
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Activity className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">AI Usage Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analyticsData.overview.aiUsageRate}%
                </p>
                <p className="text-xs text-purple-600">
                  Across all features
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Charts Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Session Trends */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Session Trends</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={analyticsData.sessionTrends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line 
                    type="monotone" 
                    dataKey="sessions" 
                    stroke="#4f46e5" 
                    strokeWidth={2}
                    name="Sessions"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="duration" 
                    stroke="#06b6d4" 
                    strokeWidth={2}
                    name="Avg Duration (min)"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Note Types Distribution */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Note Types Distribution</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={analyticsData.noteTypes}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {analyticsData.noteTypes.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>

        {/* AI Usage and Client Progress */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* AI Feature Usage */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">AI Feature Usage</h3>
            <div className="space-y-4">
              {analyticsData.aiUsage.map((feature, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium text-gray-900">
                        {feature.feature}
                      </span>
                      <span className="text-sm text-gray-500">
                        {feature.usage}% usage
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-indigo-600 h-2 rounded-full" 
                        style={{ width: `${feature.usage}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Saves avg {feature.timeSaved} min per note
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Client Progress Metrics */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Client Progress Overview</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={analyticsData.clientProgress}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="metric" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="improvement" fill="#10b981" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>

        {/* System Performance */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">System Performance</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <p className="text-2xl font-bold text-gray-900">
                {analyticsData.performance.systemUptime}%
              </p>
              <p className="text-sm text-gray-500">System Uptime</p>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <BarChart3 className="h-8 w-8 text-blue-600" />
              </div>
              <p className="text-2xl font-bold text-gray-900">
                {analyticsData.performance.aiAcceptanceRate}%
              </p>
              <p className="text-sm text-gray-500">AI Acceptance Rate</p>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <AlertTriangle className="h-8 w-8 text-yellow-600" />
              </div>
              <p className="text-2xl font-bold text-gray-900">
                {analyticsData.performance.errorRate}%
              </p>
              <p className="text-sm text-gray-500">Error Rate</p>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Clock className="h-8 w-8 text-purple-600" />
              </div>
              <p className="text-2xl font-bold text-gray-900">
                {analyticsData.performance.avgNoteTime}m
              </p>
              <p className="text-sm text-gray-500">Avg Note Time</p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
