{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VistaNotes/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const db =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = db\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,KACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VistaNotes/src/app/api/health/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { db } from '@/lib/db';\n\nexport async function GET() {\n  try {\n    // Test database connection\n    await db.user.findFirst();\n    \n    // Check environment variables\n    const hasOpenAI = !!process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'your-openai-api-key';\n    const hasGroq = !!process.env.GROQ_API_KEY && process.env.GROQ_API_KEY.startsWith('gsk_');\n    const hasAWS = !!process.env.AWS_ACCESS_KEY_ID && process.env.AWS_ACCESS_KEY_ID !== 'your-access-key';\n    const aiProvider = process.env.AI_PROVIDER || 'groq';\n\n    return NextResponse.json({\n      status: 'healthy',\n      timestamp: new Date().toISOString(),\n      database: 'connected',\n      services: {\n        ai_provider: aiProvider,\n        groq: hasGroq ? 'configured' : 'missing_api_key',\n        openai: hasOpenAI ? 'configured' : 'missing_api_key',\n        aws: hasAWS ? 'configured' : 'missing_credentials',\n        auth: 'configured'\n      },\n      version: '1.0.0'\n    });\n  } catch (error) {\n    return NextResponse.json({\n      status: 'unhealthy',\n      timestamp: new Date().toISOString(),\n      error: 'Database connection failed',\n      services: {\n        database: 'disconnected'\n      }\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,2BAA2B;QAC3B,MAAM,kHAAA,CAAA,KAAE,CAAC,IAAI,CAAC,SAAS;QAEvB,8BAA8B;QAC9B,MAAM,YAAY,CAAC,CAAC,QAAQ,GAAG,CAAC,cAAc,IAAI,QAAQ,GAAG,CAAC,cAAc,KAAK;QACjF,MAAM,UAAU,CAAC,CAAC,QAAQ,GAAG,CAAC,YAAY,IAAI,QAAQ,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC;QAClF,MAAM,SAAS,CAAC,CAAC,QAAQ,GAAG,CAAC,iBAAiB,IAAI,QAAQ,GAAG,CAAC,iBAAiB,KAAK;QACpF,MAAM,aAAa,QAAQ,GAAG,CAAC,WAAW,IAAI;QAE9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW;YACjC,UAAU;YACV,UAAU;gBACR,aAAa;gBACb,MAAM,UAAU,eAAe;gBAC/B,QAAQ,YAAY,eAAe;gBACnC,KAAK,SAAS,eAAe;gBAC7B,MAAM;YACR;YACA,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW;YACjC,OAAO;YACP,UAAU;gBACR,UAAU;YACZ;QACF,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}