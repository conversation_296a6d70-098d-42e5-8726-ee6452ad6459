var K=Object.defineProperty;var o=(s,e)=>K(s,"name",{value:e,configurable:!0});import{r as Y}from"./get-pipe-path-BHW2eJdv.mjs";import d from"node:module";import p from"node:path";import{fileURLToPath as U}from"node:url";import{parseTsconfig as V,getTsconfig as Z,createFilesMatcher as q,createPathsMatcher as ee}from"get-tsconfig";import se,{writeSync as te}from"node:fs";import{b as ne,i as ae,a as re}from"./index-7AaEi15b.mjs";import{p as W}from"./client-BQVF1NaW.mjs";import{inspect as oe}from"node:util";import{b as ce,a as ie,c as le,d as fe,o as R}from"./index-gbaejti9.mjs";const D=o(s=>{if(!s.startsWith("data:text/javascript,"))return;const e=s.indexOf("?");if(e===-1)return;const n=new URLSearchParams(s.slice(e+1)).get("filePath");if(n)return n},"getOriginalFilePath"),C=o(s=>{const e=D(s);return e&&(d._cache[e]=d._cache[s],delete d._cache[s],s=e),s},"interopCjsExports"),me=o(s=>{const e=s.indexOf(":");if(e!==-1)return s.slice(0,e)},"getScheme"),N=o(s=>s[0]==="."&&(s[1]==="/"||s[1]==="."||s[2]==="/"),"isRelativePath"),j=o(s=>N(s)||p.isAbsolute(s),"isFilePath"),pe=o(s=>{if(j(s))return!0;const e=me(s);return e&&e!=="node"},"requestAcceptsQuery"),x="file://",Q=/\.([cm]?ts|[tj]sx)($|\?)/,he=/[/\\].+\.(?:cts|cjs)(?:$|\?)/,de=/\.json($|\?)/,E=/\/(?:$|\?)/,ue=/^(?:@[^/]+\/)?[^/\\]+$/,w=`${p.sep}node_modules${p.sep}`;let M,_,S=!1;const A=o(s=>{let e=null;if(s){const a=p.resolve(s);e={path:a,config:V(a)}}else{try{e=Z()}catch{}if(!e)return}M=q(e),_=ee(e),S=e?.config.compilerOptions?.allowJs??!1},"loadTsconfig"),T=o(s=>Array.from(s).length>0?`?${s.toString()}`:"","urlSearchParamsStringify"),ge=`
//# sourceMappingURL=data:application/json;base64,`,I=o(()=>process.sourceMapsEnabled??!0,"shouldApplySourceMap"),F=o(({code:s,map:e})=>s+ge+Buffer.from(JSON.stringify(e),"utf8").toString("base64"),"inlineSourceMap"),$=process.env.TSX_DEBUG;$&&(R.enabled=!0,R.supportLevel=3);const J=o(s=>(...e)=>{if(!$)return;const a=`${fe(` tsx P${process.pid} `)} ${s}`,n=e.map(t=>typeof t=="string"?t:oe(t,{colors:!0})).join(" ");te(1,`${a} ${n}
`)},"createLog"),P=J(ce(ie(" CJS "))),Pe=J(le(" ESM ")),je=[".cts",".mts",".ts",".tsx",".jsx"],ye=[".js",".cjs",".mjs"],k=[".ts",".tsx",".jsx"],L=o((s,e,a,n)=>{const t=Object.getOwnPropertyDescriptor(s,e);t?.set?s[e]=a:(!t||t.configurable)&&Object.defineProperty(s,e,{value:a,enumerable:t?.enumerable||n?.enumerable,writable:n?.writable??(t?t.writable:!0),configurable:n?.configurable??(t?t.configurable:!0)})},"safeSet"),be=o((s,e,a)=>{const n=e[".js"],t=o((r,c)=>{if(s.enabled===!1)return n(r,c);const[i,f]=c.split("?");if((new URLSearchParams(f).get("namespace")??void 0)!==a)return n(r,c);P("load",{filePath:c}),r.id.startsWith("data:text/javascript,")&&(r.path=p.dirname(i)),W?.send&&W.send({type:"dependency",path:i});const u=je.some(m=>i.endsWith(m)),g=ye.some(m=>i.endsWith(m));if(!u&&!g)return n(r,i);let h=se.readFileSync(i,"utf8");if(i.endsWith(".cjs")){const m=ne(c,h);m&&(h=I()?F(m):m.code)}else if(u||ae(h)){const m=re(h,c,{tsconfigRaw:M?.(i)});h=I()?F(m):m.code}P("loaded",{filePath:i}),r._compile(h,i)},"transformer");L(e,".js",t);for(const r of k)L(e,r,t,{enumerable:!a,writable:!0,configurable:!0});return L(e,".mjs",t,{writable:!0,configurable:!0}),()=>{e[".js"]===t&&(e[".js"]=n);for(const r of[...k,".mjs"])e[r]===t&&delete e[r]}},"createExtensions"),xe=o(s=>e=>{if((e==="."||e===".."||e.endsWith("/.."))&&(e+="/"),E.test(e)){let a=p.join(e,"index.js");e.startsWith("./")&&(a=`./${a}`);try{return s(a)}catch{}}try{return s(e)}catch(a){const n=a;if(n.code==="MODULE_NOT_FOUND")try{return s(`${e}${p.sep}index.js`)}catch{}throw n}},"createImplicitResolver"),B=[".js",".json"],G=[".ts",".tsx",".jsx"],Ee=[...G,...B],_e=[...B,...G],y=Object.create(null);y[".js"]=[".ts",".tsx",".js",".jsx"],y[".jsx"]=[".tsx",".ts",".jsx",".js"],y[".cjs"]=[".cts"],y[".mjs"]=[".mts"];const X=o(s=>{const e=s.split("?"),a=e[1]?`?${e[1]}`:"",[n]=e,t=p.extname(n),r=[],c=y[t];if(c){const f=n.slice(0,-t.length);r.push(...c.map(l=>f+l+a))}const i=!(s.startsWith(x)||j(n))||n.includes(w)||n.includes("/node_modules/")?_e:Ee;return r.push(...i.map(f=>n+f+a)),r},"mapTsExtensions"),v=o((s,e,a)=>{if(P("resolveTsFilename",{request:e,isDirectory:E.test(e),isTsParent:a,allowJs:S}),E.test(e)||!a&&!S)return;const n=X(e);if(n)for(const t of n)try{return s(t)}catch(r){const{code:c}=r;if(c!=="MODULE_NOT_FOUND"&&c!=="ERR_PACKAGE_PATH_NOT_EXPORTED")throw r}},"resolveTsFilename"),Se=o((s,e)=>a=>{if(P("resolveTsFilename",{request:a,isTsParent:e,isFilePath:j(a)}),j(a)){const n=v(s,a,e);if(n)return n}try{return s(a)}catch(n){const t=n;if(t.code==="MODULE_NOT_FOUND"){if(t.path){const c=t.message.match(/^Cannot find module '([^']+)'$/);if(c){const f=c[1],l=v(s,f,e);if(l)return l}const i=t.message.match(/^Cannot find module '([^']+)'. Please verify that the package.json has a valid "main" entry$/);if(i){const f=i[1],l=v(s,f,e);if(l)return l}}const r=v(s,a,e);if(r)return r}throw t}},"createTsExtensionResolver"),z="at cjsPreparseModuleExports (node:internal",ve=o(s=>{const e=s.stack.split(`
`).slice(1);return e[1].includes(z)||e[2].includes(z)},"isFromCjsLexer"),we=o((s,e)=>{const a=s.split("?"),n=new URLSearchParams(a[1]);if(e?.filename){const t=D(e.filename);let r;if(t){const f=t.split("?"),l=f[0];r=f[1],e.filename=l,e.path=p.dirname(l),e.paths=d._nodeModulePaths(e.path),d._cache[l]=e}r||(r=e.filename.split("?")[1]);const i=new URLSearchParams(r).get("namespace");i&&n.append("namespace",i)}return[a[0],n,(t,r)=>(p.isAbsolute(t)&&!t.endsWith(".json")&&!t.endsWith(".node")&&!(r===0&&ve(new Error))&&(t+=T(n)),t)]},"preserveQuery"),Me=o((s,e,a)=>{if(s.startsWith(x)&&(s=U(s)),_&&!j(s)&&!e?.filename?.includes(w)){const n=_(s);for(const t of n)try{return a(t)}catch{}}return a(s)},"resolveTsPaths"),Te=o((s,e,a)=>(n,t,...r)=>{if(s.enabled===!1)return e(n,t,...r);n=C(n);const[c,i,f]=we(n,t);if((i.get("namespace")??void 0)!==a)return e(n,t,...r);P("resolve",{request:n,parent:t?.filename??t,restOfArgs:r});let l=o(g=>e(g,t,...r),"nextResolveSimple");l=Se(l,!!(a||t?.filename&&Q.test(t.filename))),l=xe(l);const u=f(Me(c,t,l),r.length);return P("resolved",{request:n,resolved:u}),u},"createResolveFilename"),H=o((s,e)=>{if(!e)throw new Error("The current file path (__filename or import.meta.url) must be provided in the second argument of tsx.require()");return s.startsWith(".")?((typeof e=="string"&&e.startsWith(x)||e instanceof URL)&&(e=U(e)),p.resolve(p.dirname(e),s)):s},"resolveContext"),Fe=o(s=>{const{sourceMapsEnabled:e}=process,a={enabled:!0};A(process.env.TSX_TSCONFIG_PATH),process.setSourceMapsEnabled(!0);const n=d._resolveFilename,t=Te(a,n,s?.namespace);d._resolveFilename=t;const r=be(a,d._extensions,s?.namespace),c=o(()=>{e===!1&&process.setSourceMapsEnabled(!1),a.enabled=!1,d._resolveFilename===t&&(d._resolveFilename=n),r()},"unregister");if(s?.namespace){const i=o((l,u)=>{const g=H(l,u),[h,m]=g.split("?"),b=new URLSearchParams(m);return s.namespace&&!h.startsWith("node:")&&b.set("namespace",s.namespace),Y(h+T(b))},"scopedRequire");c.require=i;const f=o((l,u,g)=>{const h=H(l,u),[m,b]=h.split("?"),O=new URLSearchParams(b);return s.namespace&&!m.startsWith("node:")&&O.set("namespace",s.namespace),t(m+T(O),module,!1,g)},"scopedResolve");c.resolve=f,c.unregister=c}return c},"register");export{C as a,M as b,he as c,F as d,de as e,x as f,Pe as g,$ as h,ue as i,pe as j,_ as k,A as l,E as m,X as n,N as o,w as p,S as q,Fe as r,Q as t};
