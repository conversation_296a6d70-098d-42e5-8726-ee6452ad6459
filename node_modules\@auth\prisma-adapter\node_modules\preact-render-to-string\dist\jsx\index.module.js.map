{"version": 3, "file": "index.module.js", "sources": ["../../src/lib/polyfills.js", "../../src/lib/util.js", "../../src/pretty.js", "../../node_modules/pretty-format/printString.js", "../../node_modules/pretty-format/index.js", "../../src/jsx.js"], "sourcesContent": ["if (typeof Symbol !== 'function') {\n\tlet c = 0;\n\t// eslint-disable-next-line\n\tSymbol = function (s) {\n\t\treturn `@@${s}${++c}`;\n\t};\n\tSymbol.for = (s) => `@@${s}`;\n}\n", "export const VOID_ELEMENTS = /^(?:area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/;\nexport const UNSAFE_NAME = /[\\s\\n\\\\/='\"\\0<>]/;\nexport const NAMESPACE_REPLACE_REGEX = /^(xlink|xmlns|xml)([A-Z])/;\nexport const HTML_LOWER_CASE = /^accessK|^auto[A-Z]|^cell|^ch|^col|cont|cross|dateT|encT|form[A-Z]|frame|hrefL|inputM|maxL|minL|noV|playsI|popoverT|readO|rowS|src[A-Z]|tabI|useM|item[A-Z]/;\nexport const SVG_CAMEL_CASE = /^ac|^ali|arabic|basel|cap|clipPath$|clipRule$|color|dominant|enable|fill|flood|font|glyph[^R]|horiz|image|letter|lighting|marker[^WUH]|overline|panose|pointe|paint|rendering|shape|stop|strikethrough|stroke|text[^L]|transform|underline|unicode|units|^v[^i]|^w|^xH/;\n\n// Boolean DOM properties that translate to enumerated ('true'/'false') attributes\nexport const HTML_ENUMERATED = new Set(['draggable', 'spellcheck']);\n\n// DOM properties that should NOT have \"px\" added when numeric\nconst ENCODED_ENTITIES = /[\"&<]/;\n\n/** @param {string} str */\nexport function encodeEntities(str) {\n\t// Skip all work for strings with no entities needing encoding:\n\tif (str.length === 0 || ENCODED_ENTITIES.test(str) === false) return str;\n\n\tlet last = 0,\n\t\ti = 0,\n\t\tout = '',\n\t\tch = '';\n\n\t// Seek forward in str until the next entity char:\n\tfor (; i < str.length; i++) {\n\t\tswitch (str.charCodeAt(i)) {\n\t\t\tcase 34:\n\t\t\t\tch = '&quot;';\n\t\t\t\tbreak;\n\t\t\tcase 38:\n\t\t\t\tch = '&amp;';\n\t\t\t\tbreak;\n\t\t\tcase 60:\n\t\t\t\tch = '&lt;';\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tcontinue;\n\t\t}\n\t\t// Append skipped/buffered characters and the encoded entity:\n\t\tif (i !== last) out = out + str.slice(last, i);\n\t\tout = out + ch;\n\t\t// Start the next seek/buffer after the entity's offset:\n\t\tlast = i + 1;\n\t}\n\tif (i !== last) out = out + str.slice(last, i);\n\treturn out;\n}\n\nexport let indent = (s, char) =>\n\tString(s).replace(/(\\n+)/g, '$1' + (char || '\\t'));\n\nexport let isLargeString = (s, length, ignoreLines) =>\n\tString(s).length > (length || 40) ||\n\t(!ignoreLines && String(s).indexOf('\\n') !== -1) ||\n\tString(s).indexOf('<') !== -1;\n\nconst JS_TO_CSS = {};\n\nconst IS_NON_DIMENSIONAL = new Set([\n\t'animation-iteration-count',\n\t'border-image-outset',\n\t'border-image-slice',\n\t'border-image-width',\n\t'box-flex',\n\t'box-flex-group',\n\t'box-ordinal-group',\n\t'column-count',\n\t'fill-opacity',\n\t'flex',\n\t'flex-grow',\n\t'flex-negative',\n\t'flex-order',\n\t'flex-positive',\n\t'flex-shrink',\n\t'flood-opacity',\n\t'font-weight',\n\t'grid-column',\n\t'grid-row',\n\t'line-clamp',\n\t'line-height',\n\t'opacity',\n\t'order',\n\t'orphans',\n\t'stop-opacity',\n\t'stroke-dasharray',\n\t'stroke-dashoffset',\n\t'stroke-miterlimit',\n\t'stroke-opacity',\n\t'stroke-width',\n\t'tab-size',\n\t'widows',\n\t'z-index',\n\t'zoom'\n]);\n\nconst CSS_REGEX = /[A-Z]/g;\n// Convert an Object style to a CSSText string\nexport function styleObjToCss(s) {\n\tlet str = '';\n\tfor (let prop in s) {\n\t\tlet val = s[prop];\n\t\tif (val != null && val !== '') {\n\t\t\tconst name =\n\t\t\t\tprop[0] == '-'\n\t\t\t\t\t? prop\n\t\t\t\t\t: JS_TO_CSS[prop] ||\n\t\t\t\t\t  (JS_TO_CSS[prop] = prop.replace(CSS_REGEX, '-$&').toLowerCase());\n\n\t\t\tlet suffix = ';';\n\t\t\tif (\n\t\t\t\ttypeof val === 'number' &&\n\t\t\t\t// Exclude custom-attributes\n\t\t\t\t!name.startsWith('--') &&\n\t\t\t\t!IS_NON_DIMENSIONAL.has(name)\n\t\t\t) {\n\t\t\t\tsuffix = 'px;';\n\t\t\t}\n\t\t\tstr = str + name + ':' + val + suffix;\n\t\t}\n\t}\n\treturn str || undefined;\n}\n\n/**\n * Get flattened children from the children prop\n * @param {Array} accumulator\n * @param {any} children A `props.children` opaque object.\n * @returns {Array} accumulator\n * @private\n */\nexport function getChildren(accumulator, children) {\n\tif (Array.isArray(children)) {\n\t\tchildren.reduce(getChildren, accumulator);\n\t} else if (children != null && children !== false) {\n\t\taccumulator.push(children);\n\t}\n\treturn accumulator;\n}\n\nfunction markAsDirty() {\n\tthis.__d = true;\n}\n\nexport function createComponent(vnode, context) {\n\treturn {\n\t\t__v: vnode,\n\t\tcontext,\n\t\tprops: vnode.props,\n\t\t// silently drop state updates\n\t\tsetState: markAsDirty,\n\t\tforceUpdate: markAsDirty,\n\t\t__d: true,\n\t\t// hooks\n\t\t__h: new Array(0)\n\t};\n}\n\n// Necessary for createContext api. Setting this property will pass\n// the context value as `this.context` just for this component.\nexport function getContext(nodeName, context) {\n\tlet cxType = nodeName.contextType;\n\tlet provider = cxType && context[cxType.__c];\n\treturn cxType != null\n\t\t? provider\n\t\t\t? provider.props.value\n\t\t\t: cxType.__\n\t\t: context;\n}\n\n/**\n * @template T\n */\nexport class Deferred {\n\tconstructor() {\n\t\t// eslint-disable-next-line lines-around-comment\n\t\t/** @type {Promise<T>} */\n\t\tthis.promise = new Promise((resolve, reject) => {\n\t\t\tthis.resolve = resolve;\n\t\t\tthis.reject = reject;\n\t\t});\n\t}\n}\n", "import {\n\tencodeEntities,\n\tindent,\n\tisLargeString,\n\tstyleObjTo<PERSON><PERSON>,\n\tgetC<PERSON><PERSON>n,\n\tcreateComponent,\n\tUNSAFE_NAME,\n\tVOID_ELEMENTS,\n\tNAMESPACE_REPLACE_REGEX,\n\tSVG_CAMEL_CASE,\n\tHTML_LOWER_CASE,\n\tgetContext\n} from './lib/util.js';\nimport { COMMIT, DIFF, DIFFED, RENDER, SKIP_EFFECTS } from './lib/constants.js';\nimport { options, Fragment } from 'preact';\n\n// components without names, kept as a hash for later comparison to return consistent UnnamedComponentXX names.\nconst UNNAMED = [];\n\nconst EMPTY_ARR = [];\n\n/**\n * Render Preact JSX + Components to a pretty-printed HTML-like string.\n * @param {VNode} vnode\tJSX Element / VNode to render\n * @param {Object} [context={}] Initial root context object\n * @param {Object} [opts={}] Rendering options\n * @param {Boolean} [opts.shallow=false] Serialize nested Components (`<Foo a=\"b\" />`) instead of rendering\n * @param {Boolean} [opts.xml=false] Use self-closing tags for elements without children\n * @param {Boolean} [opts.pretty=false] Add whitespace for readability\n * @param {RegExp|undefined} [opts.voidElements] RegeEx to define which element types are self-closing\n * @param {boolean} [_inner]\n * @returns {String} a pretty-printed HTML-like string\n */\nexport default function renderToStringPretty(vnode, context, opts, _inner) {\n\t// Performance optimization: `renderToString` is synchronous and we\n\t// therefore don't execute any effects. To do that we pass an empty\n\t// array to `options._commit` (`__c`). But we can go one step further\n\t// and avoid a lot of dirty checks and allocations by setting\n\t// `options._skipEffects` (`__s`) too.\n\tconst previousSkipEffects = options[SKIP_EFFECTS];\n\toptions[SKIP_EFFECTS] = true;\n\n\ttry {\n\t\treturn _renderToStringPretty(vnode, context || {}, opts, _inner);\n\t} finally {\n\t\t// options._commit, we don't schedule any effects in this library right now,\n\t\t// so we can pass an empty queue to this hook.\n\t\tif (options[COMMIT]) options[COMMIT](vnode, EMPTY_ARR);\n\t\toptions[SKIP_EFFECTS] = previousSkipEffects;\n\t\tEMPTY_ARR.length = 0;\n\t}\n}\n\nfunction _renderToStringPretty(\n\tvnode,\n\tcontext,\n\topts,\n\tinner,\n\tisSvgMode,\n\tselectValue\n) {\n\tif (vnode == null || typeof vnode === 'boolean') {\n\t\treturn '';\n\t}\n\n\t// #text nodes\n\tif (typeof vnode !== 'object') {\n\t\tif (typeof vnode === 'function') return '';\n\t\treturn encodeEntities(vnode + '');\n\t}\n\n\tlet pretty = opts.pretty,\n\t\tindentChar = pretty && typeof pretty === 'string' ? pretty : '\\t';\n\n\tif (Array.isArray(vnode)) {\n\t\tlet rendered = '';\n\t\tfor (let i = 0; i < vnode.length; i++) {\n\t\t\tif (pretty && i > 0) rendered = rendered + '\\n';\n\t\t\trendered =\n\t\t\t\trendered +\n\t\t\t\t_renderToStringPretty(\n\t\t\t\t\tvnode[i],\n\t\t\t\t\tcontext,\n\t\t\t\t\topts,\n\t\t\t\t\tinner,\n\t\t\t\t\tisSvgMode,\n\t\t\t\t\tselectValue\n\t\t\t\t);\n\t\t}\n\t\treturn rendered;\n\t}\n\n\t// VNodes have {constructor:undefined} to prevent JSON injection:\n\tif (vnode.constructor !== undefined) return '';\n\n\tif (options[DIFF]) options[DIFF](vnode);\n\n\tlet nodeName = vnode.type,\n\t\tprops = vnode.props,\n\t\tisComponent = false;\n\n\t// components\n\tif (typeof nodeName === 'function') {\n\t\tisComponent = true;\n\t\tif (\n\t\t\topts.shallow &&\n\t\t\t(inner || opts.renderRootComponent === false) &&\n\t\t\tnodeName !== Fragment\n\t\t) {\n\t\t\tnodeName = getComponentName(nodeName);\n\t\t} else if (nodeName === Fragment) {\n\t\t\tconst children = [];\n\t\t\tgetChildren(children, vnode.props.children);\n\t\t\treturn _renderToStringPretty(\n\t\t\t\tchildren,\n\t\t\t\tcontext,\n\t\t\t\topts,\n\t\t\t\topts.shallowHighOrder !== false,\n\t\t\t\tisSvgMode,\n\t\t\t\tselectValue\n\t\t\t);\n\t\t} else {\n\t\t\tlet rendered;\n\n\t\t\tlet c = (vnode.__c = createComponent(vnode, context));\n\n\t\t\tlet renderHook = options[RENDER];\n\n\t\t\tif (\n\t\t\t\t!nodeName.prototype ||\n\t\t\t\ttypeof nodeName.prototype.render !== 'function'\n\t\t\t) {\n\t\t\t\tlet cctx = getContext(nodeName, context);\n\n\t\t\t\t// If a hook invokes setState() to invalidate the component during rendering,\n\t\t\t\t// re-render it up to 25 times to allow \"settling\" of memoized states.\n\t\t\t\t// Note:\n\t\t\t\t//   This will need to be updated for Preact 11 to use internal.flags rather than component._dirty:\n\t\t\t\t//   https://github.com/preactjs/preact/blob/d4ca6fdb19bc715e49fd144e69f7296b2f4daa40/src/diff/component.js#L35-L44\n\t\t\t\tlet count = 0;\n\t\t\t\twhile (c.__d && count++ < 25) {\n\t\t\t\t\tc.__d = false;\n\n\t\t\t\t\tif (renderHook) renderHook(vnode);\n\n\t\t\t\t\t// stateless functional components\n\t\t\t\t\trendered = nodeName.call(vnode.__c, props, cctx);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tlet cctx = getContext(nodeName, context);\n\n\t\t\t\t// c = new nodeName(props, context);\n\t\t\t\tc = vnode.__c = new nodeName(props, cctx);\n\t\t\t\tc.__v = vnode;\n\t\t\t\t// turn off stateful re-rendering:\n\t\t\t\tc._dirty = c.__d = true;\n\t\t\t\tc.props = props;\n\t\t\t\tif (c.state == null) c.state = {};\n\n\t\t\t\tif (c._nextState == null && c.__s == null) {\n\t\t\t\t\tc._nextState = c.__s = c.state;\n\t\t\t\t}\n\n\t\t\t\tc.context = cctx;\n\t\t\t\tif (nodeName.getDerivedStateFromProps)\n\t\t\t\t\tc.state = Object.assign(\n\t\t\t\t\t\t{},\n\t\t\t\t\t\tc.state,\n\t\t\t\t\t\tnodeName.getDerivedStateFromProps(c.props, c.state)\n\t\t\t\t\t);\n\t\t\t\telse if (c.componentWillMount) {\n\t\t\t\t\tc.componentWillMount();\n\n\t\t\t\t\t// If the user called setState in cWM we need to flush pending,\n\t\t\t\t\t// state updates. This is the same behaviour in React.\n\t\t\t\t\tc.state =\n\t\t\t\t\t\tc._nextState !== c.state\n\t\t\t\t\t\t\t? c._nextState\n\t\t\t\t\t\t\t: c.__s !== c.state\n\t\t\t\t\t\t\t? c.__s\n\t\t\t\t\t\t\t: c.state;\n\t\t\t\t}\n\n\t\t\t\tif (renderHook) renderHook(vnode);\n\n\t\t\t\trendered = c.render(c.props, c.state, c.context);\n\t\t\t}\n\n\t\t\tif (c.getChildContext) {\n\t\t\t\tcontext = Object.assign({}, context, c.getChildContext());\n\t\t\t}\n\n\t\t\tconst res = _renderToStringPretty(\n\t\t\t\trendered,\n\t\t\t\tcontext,\n\t\t\t\topts,\n\t\t\t\topts.shallowHighOrder !== false,\n\t\t\t\tisSvgMode,\n\t\t\t\tselectValue\n\t\t\t);\n\n\t\t\tif (options[DIFFED]) options[DIFFED](vnode);\n\n\t\t\treturn res;\n\t\t}\n\t}\n\n\t// render JSX to HTML\n\tlet s = '<' + nodeName,\n\t\tpropChildren,\n\t\thtml;\n\n\tif (props) {\n\t\tlet attrs = Object.keys(props);\n\n\t\t// allow sorting lexicographically for more determinism (useful for tests, such as via preact-jsx-chai)\n\t\tif (opts && opts.sortAttributes === true) attrs.sort();\n\n\t\tfor (let i = 0; i < attrs.length; i++) {\n\t\t\tlet name = attrs[i],\n\t\t\t\tv = props[name];\n\t\t\tif (name === 'children') {\n\t\t\t\tpropChildren = v;\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (UNSAFE_NAME.test(name)) continue;\n\n\t\t\tif (\n\t\t\t\t!(opts && opts.allAttributes) &&\n\t\t\t\t(name === 'key' ||\n\t\t\t\t\tname === 'ref' ||\n\t\t\t\t\tname === '__self' ||\n\t\t\t\t\tname === '__source')\n\t\t\t)\n\t\t\t\tcontinue;\n\n\t\t\tif (name === 'defaultValue') {\n\t\t\t\tname = 'value';\n\t\t\t} else if (name === 'defaultChecked') {\n\t\t\t\tname = 'checked';\n\t\t\t} else if (name === 'defaultSelected') {\n\t\t\t\tname = 'selected';\n\t\t\t} else if (name === 'className') {\n\t\t\t\tif (typeof props.class !== 'undefined') continue;\n\t\t\t\tname = 'class';\n\t\t\t} else if (name === 'acceptCharset') {\n\t\t\t\tname = 'accept-charset';\n\t\t\t} else if (name === 'httpEquiv') {\n\t\t\t\tname = 'http-equiv';\n\t\t\t} else if (NAMESPACE_REPLACE_REGEX.test(name)) {\n\t\t\t\tname = name.replace(NAMESPACE_REPLACE_REGEX, '$1:$2').toLowerCase();\n\t\t\t} else if (isSvgMode) {\n\t\t\t\tif (SVG_CAMEL_CASE.test(name)) {\n\t\t\t\t\tname =\n\t\t\t\t\t\tname === 'panose1'\n\t\t\t\t\t\t\t? 'panose-1'\n\t\t\t\t\t\t\t: name.replace(/([A-Z])/g, '-$1').toLowerCase();\n\t\t\t\t}\n\t\t\t} else if (HTML_LOWER_CASE.test(name)) {\n\t\t\t\tname = name.toLowerCase();\n\t\t\t}\n\n\t\t\tif (name === 'htmlFor') {\n\t\t\t\tif (props.for) continue;\n\t\t\t\tname = 'for';\n\t\t\t}\n\n\t\t\tif (name === 'style' && v && typeof v === 'object') {\n\t\t\t\tv = styleObjToCss(v);\n\t\t\t}\n\n\t\t\t// always use string values instead of booleans for aria attributes\n\t\t\t// also see https://github.com/preactjs/preact/pull/2347/files\n\t\t\tif (name[0] === 'a' && name['1'] === 'r' && typeof v === 'boolean') {\n\t\t\t\tv = String(v);\n\t\t\t}\n\n\t\t\tlet hooked =\n\t\t\t\topts.attributeHook &&\n\t\t\t\topts.attributeHook(name, v, context, opts, isComponent);\n\t\t\tif (hooked || hooked === '') {\n\t\t\t\ts = s + hooked;\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (name === 'dangerouslySetInnerHTML') {\n\t\t\t\thtml = v && v.__html;\n\t\t\t} else if (nodeName === 'textarea' && name === 'value') {\n\t\t\t\t// <textarea value=\"a&b\"> --> <textarea>a&amp;b</textarea>\n\t\t\t\tpropChildren = v;\n\t\t\t} else if ((v || v === 0 || v === '') && typeof v !== 'function') {\n\t\t\t\tif (v === true || v === '') {\n\t\t\t\t\tv = name;\n\t\t\t\t\t// in non-xml mode, allow boolean attributes\n\t\t\t\t\tif (!opts || !opts.xml) {\n\t\t\t\t\t\ts = s + ' ' + name;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (name === 'value') {\n\t\t\t\t\tif (nodeName === 'select') {\n\t\t\t\t\t\tselectValue = v;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t} else if (\n\t\t\t\t\t\t// If we're looking at an <option> and it's the currently selected one\n\t\t\t\t\t\tnodeName === 'option' &&\n\t\t\t\t\t\tselectValue == v &&\n\t\t\t\t\t\t// and the <option> doesn't already have a selected attribute on it\n\t\t\t\t\t\ttypeof props.selected === 'undefined'\n\t\t\t\t\t) {\n\t\t\t\t\t\ts = s + ` selected`;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\ts = s + ` ${name}=\"${encodeEntities(v + '')}\"`;\n\t\t\t}\n\t\t}\n\t}\n\n\t// account for >1 multiline attribute\n\tif (pretty) {\n\t\tlet sub = s.replace(/\\n\\s*/, ' ');\n\t\tif (sub !== s && !~sub.indexOf('\\n')) s = sub;\n\t\telse if (pretty && ~s.indexOf('\\n')) s = s + '\\n';\n\t}\n\n\ts = s + '>';\n\n\tif (UNSAFE_NAME.test(nodeName))\n\t\tthrow new Error(`${nodeName} is not a valid HTML tag name in ${s}`);\n\n\tlet isVoid =\n\t\tVOID_ELEMENTS.test(nodeName) ||\n\t\t(opts.voidElements && opts.voidElements.test(nodeName));\n\tlet pieces = [];\n\n\tlet children;\n\tif (html) {\n\t\t// if multiline, indent.\n\t\tif (pretty && isLargeString(html)) {\n\t\t\thtml = '\\n' + indentChar + indent(html, indentChar);\n\t\t}\n\t\ts = s + html;\n\t} else if (\n\t\tpropChildren != null &&\n\t\tgetChildren((children = []), propChildren).length\n\t) {\n\t\tlet hasLarge = pretty && ~s.indexOf('\\n');\n\t\tlet lastWasText = false;\n\n\t\tfor (let i = 0; i < children.length; i++) {\n\t\t\tlet child = children[i];\n\n\t\t\tif (child != null && child !== false) {\n\t\t\t\tlet childSvgMode =\n\t\t\t\t\t\tnodeName === 'svg'\n\t\t\t\t\t\t\t? true\n\t\t\t\t\t\t\t: nodeName === 'foreignObject'\n\t\t\t\t\t\t\t? false\n\t\t\t\t\t\t\t: isSvgMode,\n\t\t\t\t\tret = _renderToStringPretty(\n\t\t\t\t\t\tchild,\n\t\t\t\t\t\tcontext,\n\t\t\t\t\t\topts,\n\t\t\t\t\t\ttrue,\n\t\t\t\t\t\tchildSvgMode,\n\t\t\t\t\t\tselectValue\n\t\t\t\t\t);\n\n\t\t\t\tif (pretty && !hasLarge && isLargeString(ret)) hasLarge = true;\n\n\t\t\t\t// Skip if we received an empty string\n\t\t\t\tif (ret) {\n\t\t\t\t\tif (pretty) {\n\t\t\t\t\t\tlet isText = ret.length > 0 && ret[0] != '<';\n\n\t\t\t\t\t\t// We merge adjacent text nodes, otherwise each piece would be printed\n\t\t\t\t\t\t// on a new line.\n\t\t\t\t\t\tif (lastWasText && isText) {\n\t\t\t\t\t\t\tpieces[pieces.length - 1] += ret;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tpieces.push(ret);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tlastWasText = isText;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tpieces.push(ret);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (pretty && hasLarge) {\n\t\t\tfor (let i = pieces.length; i--; ) {\n\t\t\t\tpieces[i] = '\\n' + indentChar + indent(pieces[i], indentChar);\n\t\t\t}\n\t\t}\n\t}\n\n\tif (options[DIFFED]) options[DIFFED](vnode);\n\n\tif (pieces.length || html) {\n\t\ts = s + pieces.join('');\n\t} else if (opts && opts.xml) {\n\t\treturn s.substring(0, s.length - 1) + ' />';\n\t}\n\n\tif (isVoid && !children && !html) {\n\t\ts = s.replace(/>$/, ' />');\n\t} else {\n\t\tif (pretty && ~s.indexOf('\\n')) s = s + '\\n';\n\t\ts = s + `</${nodeName}>`;\n\t}\n\n\treturn s;\n}\n\nfunction getComponentName(component) {\n\treturn (\n\t\tcomponent.displayName ||\n\t\t(component !== Function && component.name) ||\n\t\tgetFallbackComponentName(component)\n\t);\n}\n\nfunction getFallbackComponentName(component) {\n\tlet str = Function.prototype.toString.call(component),\n\t\tname = (str.match(/^\\s*function\\s+([^( ]+)/) || '')[1];\n\tif (!name) {\n\t\t// search for an existing indexed name for the given component:\n\t\tlet index = -1;\n\t\tfor (let i = UNNAMED.length; i--; ) {\n\t\t\tif (UNNAMED[i] === component) {\n\t\t\t\tindex = i;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\t// not found, create a new indexed name:\n\t\tif (index < 0) {\n\t\t\tindex = UNNAMED.push(component) - 1;\n\t\t}\n\t\tname = `UnnamedComponent${index}`;\n\t}\n\treturn name;\n}\n", "'use strict';\n\nconst ESCAPED_CHARACTERS = /(\\\\|\\\"|\\')/g;\n\nmodule.exports = function printString(val) {\n  return val.replace(ESCAPED_CHARACTERS, '\\\\$1');\n}\n", "'use strict';\n\nconst printString = require('./printString');\n\nconst toString = Object.prototype.toString;\nconst toISOString = Date.prototype.toISOString;\nconst errorToString = Error.prototype.toString;\nconst regExpToString = RegExp.prototype.toString;\nconst symbolToString = Symbol.prototype.toString;\n\nconst SYMBOL_REGEXP = /^Symbol\\((.*)\\)(.*)$/;\nconst NEWLINE_REGEXP = /\\n/ig;\n\nconst getSymbols = Object.getOwnPropertySymbols || (obj => []);\n\nfunction isToStringedArrayType(toStringed) {\n  return (\n    toStringed === '[object Array]' ||\n    toStringed === '[object ArrayBuffer]' ||\n    toStringed === '[object DataView]' ||\n    toStringed === '[object Float32Array]' ||\n    toStringed === '[object Float64Array]' ||\n    toStringed === '[object Int8Array]' ||\n    toStringed === '[object Int16Array]' ||\n    toStringed === '[object Int32Array]' ||\n    toStringed === '[object Uint8Array]' ||\n    toStringed === '[object Uint8ClampedArray]' ||\n    toStringed === '[object Uint16Array]' ||\n    toStringed === '[object Uint32Array]'\n  );\n}\n\nfunction printNumber(val) {\n  if (val != +val) return 'NaN';\n  const isNegativeZero = val === 0 && (1 / val) < 0;\n  return isNegativeZero ? '-0' : '' + val;\n}\n\nfunction printFunction(val) {\n  if (val.name === '') {\n    return '[Function anonymous]'\n  } else {\n    return '[Function ' + val.name + ']';\n  }\n}\n\nfunction printSymbol(val) {\n  return symbolToString.call(val).replace(SYMBOL_REGEXP, 'Symbol($1)');\n}\n\nfunction printError(val) {\n  return '[' + errorToString.call(val) + ']';\n}\n\nfunction printBasicValue(val) {\n  if (val === true || val === false) return '' + val;\n  if (val === undefined) return 'undefined';\n  if (val === null) return 'null';\n\n  const typeOf = typeof val;\n\n  if (typeOf === 'number') return printNumber(val);\n  if (typeOf === 'string') return '\"' + printString(val) + '\"';\n  if (typeOf === 'function') return printFunction(val);\n  if (typeOf === 'symbol') return printSymbol(val);\n\n  const toStringed = toString.call(val);\n\n  if (toStringed === '[object WeakMap]') return 'WeakMap {}';\n  if (toStringed === '[object WeakSet]') return 'WeakSet {}';\n  if (toStringed === '[object Function]' || toStringed === '[object GeneratorFunction]') return printFunction(val, min);\n  if (toStringed === '[object Symbol]') return printSymbol(val);\n  if (toStringed === '[object Date]') return toISOString.call(val);\n  if (toStringed === '[object Error]') return printError(val);\n  if (toStringed === '[object RegExp]') return regExpToString.call(val);\n  if (toStringed === '[object Arguments]' && val.length === 0) return 'Arguments []';\n  if (isToStringedArrayType(toStringed) && val.length === 0) return val.constructor.name + ' []';\n\n  if (val instanceof Error) return printError(val);\n\n  return false;\n}\n\nfunction printList(list, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min) {\n  let body = '';\n\n  if (list.length) {\n    body += edgeSpacing;\n\n    const innerIndent = prevIndent + indent;\n\n    for (let i = 0; i < list.length; i++) {\n      body += innerIndent + print(list[i], indent, innerIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n\n      if (i < list.length - 1) {\n        body += ',' + spacing;\n      }\n    }\n\n    body += edgeSpacing + prevIndent;\n  }\n\n  return '[' + body + ']';\n}\n\nfunction printArguments(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min) {\n  return (min ? '' : 'Arguments ') + printList(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n}\n\nfunction printArray(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min) {\n  return (min ? '' : val.constructor.name + ' ') + printList(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n}\n\nfunction printMap(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min) {\n  let result = 'Map {';\n  const iterator = val.entries();\n  let current = iterator.next();\n\n  if (!current.done) {\n    result += edgeSpacing;\n\n    const innerIndent = prevIndent + indent;\n\n    while (!current.done) {\n      const key = print(current.value[0], indent, innerIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n      const value = print(current.value[1], indent, innerIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n\n      result += innerIndent + key + ' => ' + value;\n\n      current = iterator.next();\n\n      if (!current.done) {\n        result += ',' + spacing;\n      }\n    }\n\n    result += edgeSpacing + prevIndent;\n  }\n\n  return result + '}';\n}\n\nfunction printObject(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min) {\n  const constructor = min ? '' : (val.constructor ?  val.constructor.name + ' ' : 'Object ');\n  let result = constructor + '{';\n  let keys = Object.keys(val).sort();\n  const symbols = getSymbols(val);\n\n  if (symbols.length) {\n    keys = keys\n      .filter(key => !(typeof key === 'symbol' || toString.call(key) === '[object Symbol]'))\n      .concat(symbols);\n  }\n\n  if (keys.length) {\n    result += edgeSpacing;\n\n    const innerIndent = prevIndent + indent;\n\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i];\n      const name = print(key, indent, innerIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n      const value = print(val[key], indent, innerIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n\n      result += innerIndent + name + ': ' + value;\n\n      if (i < keys.length - 1) {\n        result += ',' + spacing;\n      }\n    }\n\n    result += edgeSpacing + prevIndent;\n  }\n\n  return result + '}';\n}\n\nfunction printSet(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min) {\n  let result = 'Set {';\n  const iterator = val.entries();\n  let current = iterator.next();\n\n  if (!current.done) {\n    result += edgeSpacing;\n\n    const innerIndent = prevIndent + indent;\n\n    while (!current.done) {\n      result += innerIndent + print(current.value[1], indent, innerIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n\n      current = iterator.next();\n\n      if (!current.done) {\n        result += ',' + spacing;\n      }\n    }\n\n    result += edgeSpacing + prevIndent;\n  }\n\n  return result + '}';\n}\n\nfunction printComplexValue(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min) {\n  refs = refs.slice();\n  if (refs.indexOf(val) > -1) {\n    return '[Circular]';\n  } else {\n    refs.push(val);\n  }\n\n  currentDepth++;\n\n  const hitMaxDepth = currentDepth > maxDepth;\n\n  if (!hitMaxDepth && val.toJSON && typeof val.toJSON === 'function') {\n    return print(val.toJSON(), indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n  }\n\n  const toStringed = toString.call(val);\n  if (toStringed === '[object Arguments]') {\n    return hitMaxDepth ? '[Arguments]' : printArguments(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n  } else if (isToStringedArrayType(toStringed)) {\n    return hitMaxDepth ? '[Array]' : printArray(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n  } else if (toStringed === '[object Map]') {\n    return hitMaxDepth ? '[Map]' : printMap(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n  } else if (toStringed === '[object Set]') {\n    return hitMaxDepth ? '[Set]' : printSet(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n  } else if (typeof val === 'object') {\n    return hitMaxDepth ? '[Object]' : printObject(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n  }\n}\n\nfunction printPlugin(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min) {\n  let match = false;\n  let plugin;\n\n  for (let p = 0; p < plugins.length; p++) {\n    plugin = plugins[p];\n\n    if (plugin.test(val)) {\n      match = true;\n      break;\n    }\n  }\n\n  if (!match) {\n    return false;\n  }\n\n  function boundPrint(val) {\n    return print(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n  }\n\n  function boundIndent(str) {\n    const indentation = prevIndent + indent;\n    return indentation + str.replace(NEWLINE_REGEXP, '\\n' + indentation);\n  }\n\n  return plugin.print(val, boundPrint, boundIndent, {\n    edgeSpacing: edgeSpacing,\n    spacing: spacing\n  });\n}\n\nfunction print(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min) {\n  const basic = printBasicValue(val);\n  if (basic) return basic;\n\n  const plugin = printPlugin(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n  if (plugin) return plugin;\n\n  return printComplexValue(val, indent, prevIndent, spacing, edgeSpacing, refs, maxDepth, currentDepth, plugins, min);\n}\n\nconst DEFAULTS = {\n  indent: 2,\n  min: false,\n  maxDepth: Infinity,\n  plugins: []\n};\n\nfunction validateOptions(opts) {\n  Object.keys(opts).forEach(key => {\n    if (!DEFAULTS.hasOwnProperty(key)) {\n      throw new Error('prettyFormat: Invalid option: ' + key);\n    }\n  });\n\n  if (opts.min && opts.indent !== undefined && opts.indent !== 0) {\n    throw new Error('prettyFormat: Cannot run with min option and indent');\n  }\n}\n\nfunction normalizeOptions(opts) {\n  const result = {};\n\n  Object.keys(DEFAULTS).forEach(key =>\n    result[key] = opts.hasOwnProperty(key) ? opts[key] : DEFAULTS[key]\n  );\n\n  if (result.min) {\n    result.indent = 0;\n  }\n\n  return result;\n}\n\nfunction createIndent(indent) {\n  return new Array(indent + 1).join(' ');\n}\n\nfunction prettyFormat(val, opts) {\n  if (!opts) {\n    opts = DEFAULTS;\n  } else {\n    validateOptions(opts)\n    opts = normalizeOptions(opts);\n  }\n\n  let indent;\n  let refs;\n  const prevIndent = '';\n  const currentDepth = 0;\n  const spacing = opts.min ? ' ' : '\\n';\n  const edgeSpacing = opts.min ? '' : '\\n';\n\n  if (opts && opts.plugins.length) {\n    indent = createIndent(opts.indent);\n    refs = [];\n    var pluginsResult = printPlugin(val, indent, prevIndent, spacing, edgeSpacing, refs, opts.maxDepth, currentDepth, opts.plugins, opts.min);\n    if (pluginsResult) return pluginsResult;\n  }\n\n  var basicResult = printBasicValue(val);\n  if (basicResult) return basicResult;\n\n  if (!indent) indent = createIndent(opts.indent);\n  if (!refs) refs = [];\n  return printComplexValue(val, indent, prevIndent, spacing, edgeSpacing, refs, opts.maxDepth, currentDepth, opts.plugins, opts.min);\n}\n\nmodule.exports = prettyFormat;\n", "import './lib/polyfills.js';\nimport renderToString from './pretty.js';\nimport { indent, encodeEntities } from './lib/util.js';\nimport prettyFormat from 'pretty-format';\n\n// we have to patch in Array support, Possible issue in npm.im/pretty-format\nlet preactPlugin = {\n\ttest(object) {\n\t\treturn (\n\t\t\tobject &&\n\t\t\ttypeof object === 'object' &&\n\t\t\t'type' in object &&\n\t\t\t'props' in object &&\n\t\t\t'key' in object\n\t\t);\n\t},\n\tprint(val, print, indent) {\n\t\treturn renderToString(val, preactPlugin.context, preactPlugin.opts, true);\n\t}\n};\n\nlet prettyFormatOpts = {\n\tplugins: [preactPlugin]\n};\n\nfunction attributeHook(name, value, context, opts, isComponent) {\n\tlet type = typeof value;\n\n\t// Use render-to-string's built-in handling for these properties\n\tif (name === 'dangerouslySetInnerHTML') return false;\n\n\t// always skip null & undefined values, skip false DOM attributes, skip functions if told to\n\tif (value == null || (type === 'function' && !opts.functions)) return '';\n\n\tif (\n\t\topts.skipFalseAttributes &&\n\t\t!isComponent &&\n\t\t(value === false ||\n\t\t\t((name === 'class' || name === 'style') && value === ''))\n\t)\n\t\treturn '';\n\n\tlet indentChar = typeof opts.pretty === 'string' ? opts.pretty : '\\t';\n\tif (type !== 'string') {\n\t\tif (type === 'function' && !opts.functionNames) {\n\t\t\tvalue = 'Function';\n\t\t} else {\n\t\t\tpreactPlugin.context = context;\n\t\t\tpreactPlugin.opts = opts;\n\t\t\tvalue = prettyFormat(value, prettyFormatOpts);\n\t\t\tif (~value.indexOf('\\n')) {\n\t\t\t\tvalue = `${indent('\\n' + value, indentChar)}\\n`;\n\t\t\t}\n\t\t}\n\t\treturn indent(`\\n${name}={${value}}`, indentChar);\n\t}\n\treturn `\\n${indentChar}${name}=\"${encodeEntities(value)}\"`;\n}\n\nlet defaultOpts = {\n\tattributeHook,\n\tjsx: true,\n\txml: false,\n\tfunctions: true,\n\tfunctionNames: true,\n\tskipFalseAttributes: true,\n\tpretty: '  '\n};\n\n/**\n * Render Preact JSX + Components to a pretty-printed HTML-like string.\n * @param {VNode} vnode\tJSX Element / VNode to render\n * @param {Object} [context={}] Initial root context object\n * @param {Object} [options={}] Rendering options\n * @param {Boolean} [options.jsx=true] Generate JSX/XML output instead of HTML\n * @param {Boolean} [options.xml=false] Use self-closing tags for elements without children\n * @param {Boolean} [options.shallow=false] Serialize nested Components (`<Foo a=\"b\" />`) instead of rendering\n * @param {Boolean} [options.pretty=false] Add whitespace for readability\n * @param {RegExp|undefined} [options.voidElements] RegeEx to define which element types are self-closing\n * @returns {String} a pretty-printed HTML-like string\n */\nexport default function renderToStringPretty(vnode, context, options) {\n\tconst opts = Object.assign({}, defaultOpts, options || {});\n\tif (!opts.jsx) opts.attributeHook = null;\n\treturn renderToString(vnode, context, opts);\n}\nexport { renderToStringPretty as render };\n\nconst SHALLOW = { shallow: true };\n\n/** Only render elements, leaving Components inline as `<ComponentName ... />`.\n *\tThis method is just a convenience alias for `render(vnode, context, { shallow:true })`\n *\t@name shallow\n *\t@function\n *\t@param {VNode} vnode\tJSX VNode to render.\n *\t@param {Object} [context={}]\tOptionally pass an initial context object through the render path.\n *\t@param {Parameters<typeof renderToStringPretty>[2]} [options]\tOptionally pass an initial context object through the render path.\n */\nexport function shallowRender(vnode, context, options) {\n\tconst opts = Object.assign({}, SHALLOW, options || {});\n\treturn renderToStringPretty(vnode, context, opts);\n}\n"], "names": ["Symbol", "c", "s", "VOID_ELEMENTS", "UNSAFE_NAME", "NAMESPACE_REPLACE_REGEX", "HTML_LOWER_CASE", "SVG_CAMEL_CASE", "ENCODED_ENTITIES", "encodeEntities", "str", "length", "test", "last", "i", "out", "ch", "charCodeAt", "slice", "indent", "char", "String", "replace", "isLargeString", "ignoreLines", "indexOf", "JS_TO_CSS", "IS_NON_DIMENSIONAL", "Set", "CSS_REGEX", "styleObjToCss", "prop", "val", "name", "toLowerCase", "suffix", "startsWith", "has", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "accumulator", "children", "Array", "isArray", "reduce", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "this", "__d", "createComponent", "vnode", "context", "__v", "props", "setState", "forceUpdate", "__h", "getContext", "nodeName", "cxType", "contextType", "provider", "__c", "value", "__", "UNNAMED", "EMPTY_ARR", "renderToStringPretty", "opts", "_inner", "previousSkipEffects", "options", "_renderToStringPretty", "inner", "isSvgMode", "selectValue", "pretty", "indentChar", "rendered", "constructor", "component", "type", "isComponent", "shallow", "renderRootComponent", "Fragment", "shallowHighOrder", "renderHook", "prototype", "render", "cctx", "_dirty", "state", "_nextState", "__s", "getDerivedStateFromProps", "Object", "assign", "componentWillMount", "count", "call", "getChildContext", "res", "displayName", "Function", "getFallbackComponentName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "html", "attrs", "keys", "sortAttributes", "sort", "v", "allAttributes", "hooked", "attributeHook", "__html", "xml", "selected", "sub", "Error", "isVoid", "voidElements", "pieces", "<PERSON><PERSON><PERSON><PERSON>", "lastWasText", "child", "ret", "isText", "join", "substring", "toString", "match", "index", "ESCAPED_CHARACTERS", "toISOString", "Date", "errorToString", "regExpToString", "RegExp", "symbolToString", "SYMBOL_REGEXP", "NEWLINE_REGEXP", "getSymbols", "getOwnPropertySymbols", "obj", "isToStringedArrayType", "toStringed", "printNumber", "printFunction", "printSymbol", "printError", "printBasicValue", "typeOf", "printString", "min", "printList", "list", "prevIndent", "spacing", "edgeSpacing", "refs", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "plugins", "body", "innerIndent", "print", "printArguments", "printArray", "printMap", "result", "iterator", "entries", "current", "next", "done", "printObject", "symbols", "filter", "key", "concat", "printSet", "printComplexValue", "hitMaxDepth", "toJSON", "printPlugin", "plugin", "p", "indentation", "DEFAULTS", "Infinity", "validateOptions", "for<PERSON>ach", "hasOwnProperty", "normalizeOptions", "createIndent", "pluginsResult", "preactPlugin", "object", "renderToString", "prettyFormatOpts", "defaultOpts", "functions", "skipFalseAttributes", "functionNames", "prettyFormat", "jsx", "SHALLOW", "shallowRender"], "mappings": "+CAAA,GAAsB,mBAAXA,OAAuB,CACjC,IAAIC,EAAI,EAERD,OAAS,SAAUE,GAClB,WAAYA,KAAMD,CAClB,EACDD,WAAa,SAACE,cAAWA,CAAZ,CACb,KCPYC,EAAgB,6EAChBC,EAAc,mBACdC,EAA0B,4BAC1BC,EAAkB,8JAClBC,EAAiB,yQAMxBC,EAAmB,iBAGTC,EAAeC,GAE9B,GAAmB,IAAfA,EAAIC,SAA+C,IAA/BH,EAAiBI,KAAKF,GAAgB,OAAOA,EAQrE,IANA,IAAIG,EAAO,EACVC,EAAI,EACJC,EAAM,GACNC,EAAK,GAGCF,EAAIJ,EAAIC,OAAQG,IAAK,CAC3B,OAAQJ,EAAIO,WAAWH,IACtB,QACCE,EAAK,SACL,MACD,QACCA,EAAK,QACL,MACD,QACCA,EAAK,OACL,MACD,QACC,SAGEF,IAAMD,IAAME,GAAYL,EAAIQ,MAAML,EAAMC,IAC5CC,GAAYC,EAEZH,EAAOC,EAAI,CACX,CAED,OADIA,IAAMD,IAAME,GAAYL,EAAIQ,MAAML,EAAMC,IACrCC,CACP,KAEUI,EAAS,SAACjB,EAAGkB,UACvBC,OAAOnB,GAAGoB,QAAQ,SAAU,MAAQF,GAAQ,MADzB,EAGTG,EAAgB,SAACrB,EAAGS,EAAQa,UACtCH,OAAOnB,GAAGS,QAAUA,GAAU,MAC5Ba,IAA4C,IAA7BH,OAAOnB,GAAGuB,QAAQ,QACP,IAA5BJ,OAAOnB,GAAGuB,QAAQ,IAHQ,EAKrBC,EAAY,GAEZC,EAAqB,IAAIC,IAAI,CAClC,4BACA,sBACA,qBACA,qBACA,WACA,iBACA,oBACA,eACA,eACA,OACA,YACA,gBACA,aACA,gBACA,cACA,gBACA,cACA,cACA,WACA,aACA,cACA,UACA,QACA,UACA,eACA,mBACA,oBACA,oBACA,iBACA,eACA,WACA,SACA,UACA,SAGKC,EAAY,kBAEFC,EAAc5B,GAC7B,IAAIQ,EAAM,GACV,IAAK,IAAIqB,KAAQ7B,EAAG,CACnB,IAAI8B,EAAM9B,EAAE6B,GACZ,GAAW,MAAPC,GAAuB,KAARA,EAAY,CAC9B,IAAMC,EACM,KAAXF,EAAK,GACFA,EACAL,EAAUK,KACTL,EAAUK,GAAQA,EAAKT,QAAQO,EAAW,OAAOK,eAElDC,EAAS,IAEG,iBAARH,GAENC,EAAKG,WAAW,OAChBT,EAAmBU,IAAIJ,KAExBE,EAAS,OAEVzB,EAAMA,EAAMuB,EAAO,IAAMD,EAAMG,CAC/B,CACD,CACD,OAAOzB,QAAO4B,CACd,UASeC,EAAYC,EAAaC,GAMxC,OALIC,MAAMC,QAAQF,GACjBA,EAASG,OAAOL,EAAaC,GACP,MAAZC,IAAiC,IAAbA,GAC9BD,EAAYK,KAAKJ,GAEXD,CACP,CAED,SAASM,IACRC,KAAKC,KAAM,CACX,UAEeC,EAAgBC,EAAOC,GACtC,MAAO,CACNC,IAAKF,EACLC,QAAAA,EACAE,MAAOH,EAAMG,MAEbC,SAAUR,EACVS,YAAaT,EACbE,KAAK,EAELQ,IAAK,IAAId,MAAM,GAEhB,UAIee,EAAWC,EAAUP,GACpC,IAAIQ,EAASD,EAASE,YAClBC,EAAWF,GAAUR,EAAQQ,EAAOG,KACxC,OAAiB,MAAVH,EACJE,EACCA,EAASR,MAAMU,MACfJ,EAAOK,GACRb,CACH,KCpJKc,EAAU,GAEVC,EAAY,YAcMC,EAAqBjB,EAAOC,EAASiB,EAAMC,GAMlE,IAAMC,EAAsBC,EAAO,IACnCA,EAAO,KAAiB,EAExB,IACC,OAAOC,EAAsBtB,EAAOC,GAAW,GAAIiB,EAAMC,EAOzD,CARD,QAKKE,EAAO,KAAUA,EAAO,IAASrB,EAAOgB,GAC5CK,EAAO,IAAiBD,EACxBJ,EAAUvD,OAAS,CACnB,CACD,CAED,SAAS6D,EACRtB,EACAC,EACAiB,EACAK,EACAC,EACAC,GAEA,GAAa,MAATzB,GAAkC,kBAAVA,EAC3B,MAAO,GAIR,GAAqB,iBAAVA,EACV,MAAqB,mBAAVA,EAA6B,GACjCzC,EAAeyC,EAAQ,IAG/B,IAAI0B,EAASR,EAAKQ,OACjBC,EAAaD,GAA4B,iBAAXA,EAAsBA,EAAS,KAE9D,GAAIlC,MAAMC,QAAQO,GAAQ,CAEzB,IADA,IAAI4B,EAAW,GACNhE,EAAI,EAAGA,EAAIoC,EAAMvC,OAAQG,IAC7B8D,GAAU9D,EAAI,IAAGgE,GAAsB,MAC3CA,GAECN,EACCtB,EAAMpC,GACNqC,EACAiB,EACAK,EACAC,EACAC,GAGH,OAAOG,CACP,CAGD,QAA0BxC,IAAtBY,EAAM6B,YAA2B,MAAO,GAExCR,EAAO,KAAQA,EAAO,IAAOrB,GAEjC,IAgUyB8B,EAhUrBtB,EAAWR,EAAM+B,KACpB5B,EAAQH,EAAMG,MACd6B,GAAc,EAGf,GAAwB,mBAAbxB,EAAyB,CAEnC,GADAwB,GAAc,GAEbd,EAAKe,UACJV,IAAsC,IAA7BL,EAAKgB,qBACf1B,IAAa2B,MAGH3B,IAAa2B,EAAU,CACjC,IAAM5C,EAAW,GAEjB,OADAF,EAAYE,EAAUS,EAAMG,MAAMZ,UAC3B+B,EACN/B,EACAU,EACAiB,GAC0B,IAA1BA,EAAKkB,iBACLZ,EACAC,EAED,CACA,IAAIG,EAEA7E,EAAKiD,EAAMY,IAAMb,EAAgBC,EAAOC,GAExCoC,EAAahB,EAAO,IAExB,GACEb,EAAS8B,WAC2B,mBAA9B9B,EAAS8B,UAAUC,OAkBpB,CACN,IAAIC,EAAOjC,EAAWC,EAAUP,IAGhClD,EAAIiD,EAAMY,IAAM,IAAIJ,EAASL,EAAOqC,IAClCtC,IAAMF,EAERjD,EAAE0F,OAAS1F,EAAE+C,KAAM,EACnB/C,EAAEoD,MAAQA,EACK,MAAXpD,EAAE2F,QAAe3F,EAAE2F,MAAQ,IAEX,MAAhB3F,EAAE4F,YAA+B,MAAT5F,EAAE6F,MAC7B7F,EAAE4F,WAAa5F,EAAE6F,IAAM7F,EAAE2F,OAG1B3F,EAAEkD,QAAUuC,EACRhC,EAASqC,yBACZ9F,EAAE2F,MAAQI,OAAOC,OAChB,GACAhG,EAAE2F,MACFlC,EAASqC,yBAAyB9F,EAAEoD,MAAOpD,EAAE2F,QAEtC3F,EAAEiG,qBACVjG,EAAEiG,qBAIFjG,EAAE2F,MACD3F,EAAE4F,aAAe5F,EAAE2F,MAChB3F,EAAE4F,WACF5F,EAAE6F,MAAQ7F,EAAE2F,MACZ3F,EAAE6F,IACF7F,EAAE2F,OAGHL,GAAYA,EAAWrC,GAE3B4B,EAAW7E,EAAEwF,OAAOxF,EAAEoD,MAAOpD,EAAE2F,MAAO3F,EAAEkD,QACxC,MA9CA,IARA,IAAIuC,EAAOjC,EAAWC,EAAUP,GAO5BgD,EAAQ,EACLlG,EAAE+C,KAAOmD,IAAU,IACzBlG,EAAE+C,KAAM,EAEJuC,GAAYA,EAAWrC,GAG3B4B,EAAWpB,EAAS0C,KAAKlD,EAAMY,IAAKT,EAAOqC,GA0CzCzF,EAAEoG,kBACLlD,EAAU6C,OAAOC,OAAO,GAAI9C,EAASlD,EAAEoG,oBAGxC,IAAMC,EAAM9B,EACXM,EACA3B,EACAiB,GAC0B,IAA1BA,EAAKkB,iBACLZ,EACAC,GAKD,OAFIJ,EAAO,QAAUA,EAAO,OAASrB,GAE9BoD,CACP,CA/FA5C,GAoTuBsB,EApTKtB,GAsTnB6C,aACTvB,IAAcwB,UAAYxB,EAAU/C,MACrCwE,EAAyBzB,EAxNzB,CAGD,IACC0B,EACAC,EAFGzG,EAAI,IAAMwD,EAId,GAAIL,EAAO,CACV,IAAIuD,EAAQZ,OAAOa,KAAKxD,GAGpBe,IAAgC,IAAxBA,EAAK0C,gBAAyBF,EAAMG,OAEhD,IAAK,IAAIjG,EAAI,EAAGA,EAAI8F,EAAMjG,OAAQG,IAAK,CACtC,IAAImB,EAAO2E,EAAM9F,GAChBkG,EAAI3D,EAAMpB,GACX,GAAa,aAATA,GAKJ,IAAI7B,EAAYQ,KAAKqB,KAGlBmC,GAAQA,EAAK6C,eACL,QAAThF,GACS,QAATA,GACS,WAATA,GACS,aAATA,GALF,CASA,GAAa,iBAATA,EACHA,EAAO,gBACY,mBAATA,EACVA,EAAO,kBACY,oBAATA,EACVA,EAAO,mBACY,cAATA,EAAsB,CAChC,QAA2B,IAAhBoB,QAA6B,SACxCpB,EAAO,OACP,KAAmB,kBAATA,EACVA,EAAO,iBACY,cAATA,EACVA,EAAO,aACG5B,EAAwBO,KAAKqB,GACvCA,EAAOA,EAAKX,QAAQjB,EAAyB,SAAS6B,cAC5CwC,EACNnE,EAAeK,KAAKqB,KACvBA,EACU,YAATA,EACG,WACAA,EAAKX,QAAQ,WAAY,OAAOY,eAE3B5B,EAAgBM,KAAKqB,KAC/BA,EAAOA,EAAKC,eAGb,GAAa,YAATD,EAAoB,CACvB,GAAIoB,MAAW,SACfpB,EAAO,KACP,CAEY,UAATA,GAAoB+E,GAAkB,iBAANA,IACnCA,EAAIlF,EAAckF,IAKH,MAAZ/E,EAAK,IAA4B,MAAdA,EAAK,IAA6B,kBAAN+E,IAClDA,EAAI3F,OAAO2F,IAGZ,IAAIE,EACH9C,EAAK+C,eACL/C,EAAK+C,cAAclF,EAAM+E,EAAG7D,EAASiB,EAAMc,GAC5C,GAAIgC,GAAqB,KAAXA,EACbhH,GAAQgH,OAIT,GAAa,4BAATjF,EACH0E,EAAOK,GAAKA,EAAEI,eACS,aAAb1D,GAAoC,UAATzB,EAErCyE,EAAeM,WACJA,GAAW,IAANA,GAAiB,KAANA,IAA0B,mBAANA,EAAkB,CACjE,MAAU,IAANA,GAAoB,KAANA,IACjBA,EAAI/E,EAECmC,GAASA,EAAKiD,MAAK,CACvBnH,EAAIA,EAAI,IAAM+B,EACd,QACA,CAGF,GAAa,UAATA,EAAkB,CACrB,GAAiB,WAAbyB,EAAuB,CAC1BiB,EAAcqC,EACd,QACA,CAEa,WAAbtD,GACAiB,GAAeqC,QAEW,IAAnB3D,EAAMiE,WAEbpH,eAED,CACDA,EAAIA,MAAQ+B,OAASxB,EAAeuG,EAAI,OACxC,QA9FAN,EAAeM,CA+FhB,CACD,CAGD,GAAIpC,EAAQ,CACX,IAAI2C,EAAMrH,EAAEoB,QAAQ,QAAS,KACzBiG,IAAQrH,IAAOqH,EAAI9F,QAAQ,MACtBmD,IAAW1E,EAAEuB,QAAQ,QAAOvB,GAAQ,MADPA,EAAIqH,CAE1C,CAID,GAFArH,GAAQ,IAEJE,EAAYQ,KAAK8C,GACpB,UAAU8D,MAAS9D,sCAA4CxD,GAEhE,IAKIuC,EALAgF,EACHtH,EAAcS,KAAK8C,IAClBU,EAAKsD,cAAgBtD,EAAKsD,aAAa9G,KAAK8C,GAC1CiE,EAAS,GAGb,GAAIhB,EAEC/B,GAAUrD,EAAcoF,KAC3BA,EAAO,KAAO9B,EAAa1D,EAAOwF,EAAM9B,IAEzC3E,GAAQyG,UAEQ,MAAhBD,GACAnE,EAAaE,EAAW,GAAKiE,GAAc/F,OAC1C,CAID,IAHA,IAAIiH,EAAWhD,IAAW1E,EAAEuB,QAAQ,MAChCoG,GAAc,EAET/G,EAAI,EAAGA,EAAI2B,EAAS9B,OAAQG,IAAK,CACzC,IAAIgH,EAAQrF,EAAS3B,GAErB,GAAa,MAATgH,IAA2B,IAAVA,EAAiB,CACrC,IAMCC,GAAMvD,EACLsD,EACA3E,EACAiB,GACA,EATa,QAAbV,GAEgB,kBAAbA,GAEAgB,EAOHC,GAMF,GAHIC,IAAWgD,GAAYrG,EAAcwG,MAAMH,GAAW,GAGtDG,GACH,GAAInD,EAAQ,CACX,IAAIoD,GAASD,GAAIpH,OAAS,GAAe,KAAVoH,GAAI,GAI/BF,GAAeG,GAClBL,EAAOA,EAAOhH,OAAS,IAAMoH,GAE7BJ,EAAO9E,KAAKkF,IAGbF,EAAcG,EACd,MACAL,EAAO9E,KAAKkF,GAGd,CACD,CACD,GAAInD,GAAUgD,EACb,IAAK,IAAI9G,GAAI6G,EAAOhH,OAAQG,MAC3B6G,EAAO7G,IAAK,KAAO+D,EAAa1D,EAAOwG,EAAO7G,IAAI+D,EAGpD,CAID,GAFIN,EAAO,QAAUA,EAAO,OAASrB,GAEjCyE,EAAOhH,QAAUgG,EACpBzG,GAAQyH,EAAOM,KAAK,YACV7D,GAAQA,EAAKiD,IACvB,OAAOnH,EAAEgI,UAAU,EAAGhI,EAAES,OAAS,GAAK,MAUvC,OAPI8G,GAAWhF,GAAakE,GAGvB/B,IAAW1E,EAAEuB,QAAQ,QAAOvB,GAAQ,MACxCA,EAAIA,OAASwD,OAHbxD,EAAIA,EAAEoB,QAAQ,KAAM,OAMdpB,CACP,CAUD,SAASuG,EAAyBzB,GACjC,IACC/C,GADSuE,SAAShB,UAAU2C,SAAS/B,KAAKpB,GAC9BoD,MAAM,4BAA8B,IAAI,GACrD,IAAKnG,EAAM,CAGV,IADA,IAAIoG,GAAS,EACJvH,EAAImD,EAAQtD,OAAQG,KAC5B,GAAImD,EAAQnD,KAAOkE,EAAW,CAC7BqD,EAAQvH,EACR,KACA,CAGEuH,EAAQ,IACXA,EAAQpE,EAAQpB,KAAKmC,GAAa,GAEnC/C,qBAA0BoG,CAC1B,CACD,OAAOpG,CACP,CC3bD,MAAMqG,EAAqB,cAE3B,MAAiB,SAAqBtG,GACpC,OAAOA,EAAIV,QAAQgH,EAAoB,OACzC,ECFA,MAAMH,EAAWnC,OAAOR,UAAU2C,SAC5BI,EAAcC,KAAKhD,UAAU+C,YAC7BE,EAAgBjB,MAAMhC,UAAU2C,SAChCO,EAAiBC,OAAOnD,UAAU2C,SAClCS,EAAiB5I,OAAOwF,UAAU2C,SAElCU,EAAgB,uBAChBC,EAAiB,OAEjBC,EAAa/C,OAAOgD,wBAA0BC,GAAO,IAE3D,SAASC,EAAsBC,GAC7B,MACiB,mBAAfA,GACe,yBAAfA,GACe,sBAAfA,GACe,0BAAfA,GACe,0BAAfA,GACe,uBAAfA,GACe,wBAAfA,GACe,wBAAfA,GACe,wBAAfA,GACe,+BAAfA,GACe,yBAAfA,GACe,yBAAfA,CAEJ,CAEA,SAASC,EAAYpH,GACnB,OAAIA,IAAQA,EAAY,MACO,IAARA,GAAc,EAAIA,EAAO,EACxB,KAAO,GAAKA,CACtC,CAEA,SAASqH,EAAcrH,GACrB,MAAiB,KAAbA,EAAIC,KACC,uBAEA,aAAeD,EAAIC,KAAO,GAErC,CAEA,SAASqH,EAAYtH,GACnB,OAAO4G,EAAexC,KAAKpE,GAAKV,QAAQuH,EAAe,aACzD,CAEA,SAASU,EAAWvH,GAClB,MAAO,IAAMyG,EAAcrC,KAAKpE,GAAO,GACzC,CAEA,SAASwH,EAAgBxH,GACvB,IAAY,IAARA,IAAwB,IAARA,EAAe,MAAO,GAAKA,EAC/C,QAAYM,IAARN,EAAmB,MAAO,YAC9B,GAAY,OAARA,EAAc,MAAO,OAEzB,MAAMyH,SAAgBzH,EAEtB,GAAe,WAAXyH,EAAqB,OAAOL,EAAYpH,GAC5C,GAAe,WAAXyH,EAAqB,MAAO,IAAMC,EAAY1H,GAAO,IACzD,GAAe,aAAXyH,EAAuB,OAAOJ,EAAcrH,GAChD,GAAe,WAAXyH,EAAqB,OAAOH,EAAYtH,GAE5C,MAAMmH,EAAahB,EAAS/B,KAAKpE,GAEjC,MAAmB,qBAAfmH,EAA0C,aAC3B,qBAAfA,EAA0C,aAC3B,sBAAfA,GAAqD,+BAAfA,EAAoDE,EAAcrH,EAAK2H,KAC9F,oBAAfR,EAAyCG,EAAYtH,GACtC,kBAAfmH,EAAuCZ,EAAYnC,KAAKpE,GACzC,mBAAfmH,EAAwCI,EAAWvH,GACpC,oBAAfmH,EAAyCT,EAAetC,KAAKpE,GAC9C,uBAAfmH,GAAsD,IAAfnH,EAAIrB,OAAqB,eAChEuI,EAAsBC,IAA8B,IAAfnH,EAAIrB,OAAqBqB,EAAI+C,YAAY9C,KAAO,MAErFD,aAAewF,OAAc+B,EAAWvH,EAG9C,CAEA,SAAS4H,EAAUC,EAAM1I,EAAQ2I,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GACxG,IAAIU,EAAO,GAEX,GAAIR,EAAKlJ,OAAQ,CACf0J,GAAQL,EAER,MAAMM,EAAcR,EAAa3I,EAEjC,IAAK,IAAIL,EAAI,EAAGA,EAAI+I,EAAKlJ,OAAQG,IAC/BuJ,GAAQC,EAAcC,EAAMV,EAAK/I,GAAIK,EAAQmJ,EAAaP,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAEnH7I,EAAI+I,EAAKlJ,OAAS,IACpB0J,GAAQ,IAAMN,GAIlBM,GAAQL,EAAcF,EAGxB,MAAO,IAAMO,EAAO,GACtB,CAEA,SAASG,EAAexI,EAAKb,EAAQ2I,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAC5G,OAAQA,EAAM,GAAK,cAAgBC,EAAU5H,EAAKb,EAAQ2I,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,EACrI,CAEA,SAASc,EAAWzI,EAAKb,EAAQ2I,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GACxG,OAAQA,EAAM,GAAK3H,EAAI+C,YAAY9C,KAAO,KAAO2H,EAAU5H,EAAKb,EAAQ2I,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,EACnJ,CAEA,SAASe,EAAS1I,EAAKb,EAAQ2I,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GACtG,IAAIgB,EAAS,QACb,MAAMC,EAAW5I,EAAI6I,UACrB,IAAIC,EAAUF,EAASG,OAEvB,IAAKD,EAAQE,KAAM,CACjBL,GAAUX,EAEV,MAAMM,EAAcR,EAAa3I,EAEjC,MAAQ2J,EAAQE,MAIdL,GAAUL,EAHEC,EAAMO,EAAQ/G,MAAM,GAAI5C,EAAQmJ,EAAaP,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAGxF,OAFhBY,EAAMO,EAAQ/G,MAAM,GAAI5C,EAAQmJ,EAAaP,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAIxHmB,EAAUF,EAASG,OAEdD,EAAQE,OACXL,GAAU,IAAMZ,GAIpBY,GAAUX,EAAcF,EAG1B,OAAOa,EAAS,GAClB,CAEA,SAASM,EAAYjJ,EAAKb,EAAQ2I,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAEzG,IAAIgB,GADgBhB,EAAM,GAAM3H,EAAI+C,YAAe/C,EAAI+C,YAAY9C,KAAO,IAAM,WACrD,IACvB4E,EAAOb,OAAOa,KAAK7E,GAAK+E,OAC5B,MAAMmE,EAAUnC,EAAW/G,GAQ3B,GANIkJ,EAAQvK,SACVkG,EAAOA,EACJsE,OAAOC,KAAwB,iBAARA,GAA2C,oBAAvBjD,EAAS/B,KAAKgF,KACzDC,OAAOH,IAGRrE,EAAKlG,OAAQ,CACfgK,GAAUX,EAEV,MAAMM,EAAcR,EAAa3I,EAEjC,IAAK,IAAIL,EAAI,EAAGA,EAAI+F,EAAKlG,OAAQG,IAAK,CACpC,MAAMsK,EAAMvE,EAAK/F,GAIjB6J,GAAUL,EAHGC,EAAMa,EAAKjK,EAAQmJ,EAAaP,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAG3E,KAFjBY,EAAMvI,EAAIoJ,GAAMjK,EAAQmJ,EAAaP,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAI5G7I,EAAI+F,EAAKlG,OAAS,IACpBgK,GAAU,IAAMZ,GAIpBY,GAAUX,EAAcF,EAG1B,OAAOa,EAAS,GAClB,CAEA,SAASW,EAAStJ,EAAKb,EAAQ2I,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GACtG,IAAIgB,EAAS,QACb,MAAMC,EAAW5I,EAAI6I,UACrB,IAAIC,EAAUF,EAASG,OAEvB,IAAKD,EAAQE,KAAM,CACjBL,GAAUX,EAEV,MAAMM,EAAcR,EAAa3I,EAEjC,MAAQ2J,EAAQE,MACdL,GAAUL,EAAcC,EAAMO,EAAQ/G,MAAM,GAAI5C,EAAQmJ,EAAaP,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAElImB,EAAUF,EAASG,OAEdD,EAAQE,OACXL,GAAU,IAAMZ,GAIpBY,GAAUX,EAAcF,EAG1B,OAAOa,EAAS,GAClB,CAEA,SAASY,EAAkBvJ,EAAKb,EAAQ2I,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAE/G,IADAM,EAAOA,EAAK/I,SACHO,QAAQO,IAAQ,EACvB,MAAO,aAEPiI,EAAKpH,KAAKb,GAKZ,MAAMwJ,IAFNrB,EAEmCD,EAEnC,IAAKsB,GAAexJ,EAAIyJ,QAAgC,mBAAfzJ,EAAIyJ,OAC3C,OAAOlB,EAAMvI,EAAIyJ,SAAUtK,EAAQ2I,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAG9G,MAAMR,EAAahB,EAAS/B,KAAKpE,GACjC,MAAmB,uBAAfmH,EACKqC,EAAc,cAAgBhB,EAAexI,EAAKb,EAAQ2I,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GACjIT,EAAsBC,GACxBqC,EAAc,UAAYf,EAAWzI,EAAKb,EAAQ2I,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAC1G,iBAAfR,EACFqC,EAAc,QAAUd,EAAS1I,EAAKb,EAAQ2I,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GACtG,iBAAfR,EACFqC,EAAc,QAAUF,EAAStJ,EAAKb,EAAQ2I,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GACtG,iBAAR3H,EACTwJ,EAAc,WAAaP,EAAYjJ,EAAKb,EAAQ2I,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,QAD/H,CAGT,CAEA,SAAS+B,EAAY1J,EAAKb,EAAQ2I,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GACzG,IACIgC,EADAvD,GAAQ,EAGZ,IAAK,IAAIwD,EAAI,EAAGA,EAAIxB,EAAQzJ,OAAQiL,IAGlC,GAFAD,EAASvB,EAAQwB,GAEbD,EAAO/K,KAAKoB,GAAM,CACpBoG,GAAQ,EACR,MAIJ,QAAKA,GAaEuD,EAAOpB,MAAMvI,EATpB,SAAoBA,GAClB,OAAOuI,EAAMvI,EAAKb,EAAQ2I,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,IAGrG,SAAqBjJ,GACnB,MAAMmL,EAAc/B,EAAa3I,EACjC,OAAO0K,EAAcnL,EAAIY,QAAQwH,EAAgB,KAAO+C,IAGR,CAChD7B,YAAaA,EACbD,QAASA,GAEb,CAEA,SAASQ,EAAMvI,EAAKb,EAAQ2I,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,GAEnG,OADcH,EAAgBxH,IAGf0J,EAAY1J,EAAKb,EAAQ2I,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,IAG1G4B,EAAkBvJ,EAAKb,EAAQ2I,EAAYC,EAASC,EAAaC,EAAMC,EAAUC,EAAcC,EAAST,EACjH,CAEA,MAAMmC,EAAW,CACf3K,OAAQ,EACRwI,KAAK,EACLO,SAAU6B,SACV3B,QAAS,IAGX,SAAS4B,EAAgB5H,GAOvB,GANA4B,OAAOa,KAAKzC,GAAM6H,QAAQb,IACxB,IAAKU,EAASI,eAAed,GAC3B,MAAM,IAAI5D,MAAM,iCAAmC4D,KAInDhH,EAAKuF,UAAuBrH,IAAhB8B,EAAKjD,QAAwC,IAAhBiD,EAAKjD,OAChD,MAAM,IAAIqG,MAAM,sDAEpB,CAEA,SAAS2E,EAAiB/H,GACxB,MAAMuG,EAAS,GAUf,OARA3E,OAAOa,KAAKiF,GAAUG,QAAQb,GAC5BT,EAAOS,GAAOhH,EAAK8H,eAAed,GAAOhH,EAAKgH,GAAOU,EAASV,IAG5DT,EAAOhB,MACTgB,EAAOxJ,OAAS,GAGXwJ,CACT,CAEA,SAASyB,EAAajL,GACpB,OAAO,IAAIuB,MAAMvB,EAAS,GAAG8G,KAAK,IACpC,CAgCA,OA9BA,SAAsBjG,EAAKoC,GAQzB,IAAIjD,EACA8I,EARC7F,GAGH4H,EAAgB5H,GAChBA,EAAO+H,EAAiB/H,IAHxBA,EAAO0H,EAQT,MAEM/B,EAAU3F,EAAKuF,IAAM,IAAM,KAC3BK,EAAc5F,EAAKuF,IAAM,GAAK,KAEpC,GAAIvF,GAAQA,EAAKgG,QAAQzJ,OAAQ,CAC/BQ,EAASiL,EAAahI,EAAKjD,QAC3B8I,EAAO,GACP,IAAIoC,EAAgBX,EAAY1J,EAAKb,EARpB,GAQwC4I,EAASC,EAAaC,EAAM7F,EAAK8F,SAPvE,EAO+F9F,EAAKgG,QAAShG,EAAKuF,KACrI,GAAI0C,EAAe,OAAOA,EAI5B,OADkB7C,EAAgBxH,KAG7Bb,IAAQA,EAASiL,EAAahI,EAAKjD,SACnC8I,IAAMA,EAAO,IACXsB,EAAkBvJ,EAAKb,EAjBX,GAiB+B4I,EAASC,EAAaC,EAAM7F,EAAK8F,SAhB9D,EAgBsF9F,EAAKgG,QAAShG,EAAKuF,KAChI,EC9UI2C,GAAe,CAClB1L,cAAK2L,GACJ,OACCA,GACkB,iBAAXA,GACP,SAAUA,GACV,UAAWA,GACX,QAASA,CAEV,EACDhC,eAAMvI,EAAKuI,EAAOpJ,GACjB,OAAOqL,EAAexK,EAAKsK,GAAanJ,QAASmJ,GAAalI,MAAM,EACpE,GAGEqI,GAAmB,CACtBrC,QAAS,CAACkC,KAqCPI,GAAc,CACjBvF,cAnCD,SAAuBlF,EAAM8B,EAAOZ,EAASiB,EAAMc,GAClD,IAAID,SAAclB,EAGlB,GAAa,4BAAT9B,EAAoC,SAGxC,GAAa,MAAT8B,GAA2B,aAATkB,IAAwBb,EAAKuI,UAAY,MAAO,GAEtE,GACCvI,EAAKwI,sBACJ1H,KACU,IAAVnB,IACW,UAAT9B,GAA6B,UAATA,IAA+B,KAAV8B,GAE5C,MAAO,GAER,IAAIc,EAAoC,iBAAhBT,EAAKQ,OAAsBR,EAAKQ,OAAS,KACjE,MAAa,WAATK,GACU,aAATA,GAAwBb,EAAKyI,eAGhCP,GAAanJ,QAAUA,EACvBmJ,GAAalI,KAAOA,IACpBL,EAAQ+I,GAAa/I,EAAO0I,KACjBhL,QAAQ,QAClBsC,EAAW5C,EAAO,KAAO4C,EAAOc,UANjCd,EAAQ,WASF5C,OAAYc,OAAS8B,MAAUc,SAE3BA,EAAa5C,OAASxB,EAAesD,MACjD,EAIAgJ,KAAK,EACL1F,KAAK,EACLsF,WAAW,EACXE,eAAe,EACfD,qBAAqB,EACrBhI,OAAQ,eAeeT,GAAqBjB,EAAOC,EAASoB,GAC5D,IAAMH,EAAO4B,OAAOC,OAAO,GAAIyG,GAAanI,GAAW,IAEvD,OADKH,EAAK2I,MAAK3I,EAAK+C,cAAgB,MAC7BqF,EAAetJ,EAAOC,EAASiB,EACtC,CAGD,IAAM4I,GAAU,CAAE7H,SAAS,YAUX8H,GAAc/J,EAAOC,EAASoB,GAE7C,OAAOJ,GAAqBjB,EAAOC,EADtB6C,OAAOC,OAAO,GAAI+G,GAASzI,GAAW,IAEnD"}