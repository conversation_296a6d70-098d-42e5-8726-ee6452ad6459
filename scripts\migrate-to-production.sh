#!/bin/bash

# VistaNotes Production Migration Script
# This script migrates from SQLite development to PostgreSQL production

set -e

echo "🚀 VistaNotes Production Migration Starting..."

# Check if required environment variables are set
if [ -z "$DATABASE_URL" ]; then
    echo "❌ ERROR: DATABASE_URL environment variable is not set"
    exit 1
fi

if [ -z "$POSTGRES_PASSWORD" ]; then
    echo "❌ ERROR: POSTGRES_PASSWORD environment variable is not set"
    exit 1
fi

echo "✅ Environment variables validated"

# Update Prisma schema for PostgreSQL
echo "📝 Updating Prisma schema for PostgreSQL..."
sed -i 's/provider = "sqlite"/provider = "postgresql"/' prisma/schema.prisma

# Generate Prisma client for PostgreSQL
echo "🔧 Generating Prisma client..."
npx prisma generate

# Run database migrations
echo "🗄️ Running database migrations..."
npx prisma db push

# Seed production database with initial data
echo "🌱 Seeding production database..."
npm run db:seed

# Create database backup
echo "💾 Creating initial database backup..."
mkdir -p backups
pg_dump $DATABASE_URL > backups/initial_backup_$(date +%Y%m%d_%H%M%S).sql

echo "✅ Production migration completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Update your environment variables in production"
echo "2. Deploy the application using Docker Compose"
echo "3. Configure SSL certificates"
echo "4. Set up monitoring and logging"
echo ""
echo "🔗 Useful commands:"
echo "  - Start production: docker-compose up -d"
echo "  - View logs: docker-compose logs -f app"
echo "  - Database backup: docker-compose exec backup pg_dump -h db -U vistanotes vistanotes > backup.sql"
echo "  - Scale app: docker-compose up -d --scale app=3"
