import { NextResponse } from 'next/server';
import { db } from '@/lib/db';

export async function GET() {
  try {
    // Test database connection
    await db.user.findFirst();
    
    // Check environment variables
    const hasOpenAI = !!process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'your-openai-api-key';
    const hasGroq = !!process.env.GROQ_API_KEY && process.env.GROQ_API_KEY.startsWith('gsk_');
    const hasAWS = !!process.env.AWS_ACCESS_KEY_ID && process.env.AWS_ACCESS_KEY_ID !== 'your-access-key';
    const aiProvider = process.env.AI_PROVIDER || 'groq';

    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      database: 'connected',
      services: {
        ai_provider: aiProvider,
        groq: hasGroq ? 'configured' : 'missing_api_key',
        openai: hasOpenAI ? 'configured' : 'missing_api_key',
        aws: hasAWS ? 'configured' : 'missing_credentials',
        auth: 'configured'
      },
      version: '1.0.0'
    });
  } catch (error) {
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Database connection failed',
      services: {
        database: 'disconnected'
      }
    }, { status: 500 });
  }
}
