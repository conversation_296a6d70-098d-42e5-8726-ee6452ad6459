{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VistaNotes/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const db =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = db\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,KACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VistaNotes/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from \"next-auth\"\nimport Cred<PERSON><PERSON><PERSON><PERSON>ider from \"next-auth/providers/credentials\"\nimport { PrismaAdapter } from \"@auth/prisma-adapter\"\nimport { db } from \"@/lib/db\"\nimport bcrypt from \"bcryptjs\"\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(db) as any,\n  session: {\n    strategy: \"jwt\",\n  },\n  pages: {\n    signIn: \"/auth/signin\",\n    signUp: \"/auth/signup\",\n  },\n  providers: [\n    CredentialsProvider({\n      name: \"credentials\",\n      credentials: {\n        email: { label: \"Email\", type: \"email\" },\n        password: { label: \"Password\", type: \"password\" }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await db.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.passwordHash\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          role: user.role,\n        }\n      }\n    })\n  ],\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        return {\n          ...token,\n          role: user.role,\n        }\n      }\n      return token\n    },\n    async session({ session, token }) {\n      return {\n        ...session,\n        user: {\n          ...session.user,\n          id: token.sub,\n          role: token.role,\n        }\n      }\n    },\n  },\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,kHAAA,CAAA,KAAE;IACzB,SAAS;QACP,UAAU;IACZ;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;IACV;IACA,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,kHAAA,CAAA,KAAE,CAAC,IAAI,CAAC,UAAU,CAAC;oBACpC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,YAAY;gBAGnB,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,OAAO;oBACL,GAAG,KAAK;oBACR,MAAM,KAAK,IAAI;gBACjB;YACF;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,OAAO;gBACL,GAAG,OAAO;gBACV,MAAM;oBACJ,GAAG,QAAQ,IAAI;oBACf,IAAI,MAAM,GAAG;oBACb,MAAM,MAAM,IAAI;gBAClB;YACF;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VistaNotes/src/app/api/notes/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth';\nimport { db } from '@/lib/db';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n    \n    if (!session?.user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    }\n\n    const { searchParams } = new URL(request.url);\n    const clientId = searchParams.get('clientId');\n    const status = searchParams.get('status');\n    const limit = parseInt(searchParams.get('limit') || '50');\n\n    const where: any = {\n      therapistId: session.user.id,\n    };\n\n    if (clientId) {\n      where.clientId = clientId;\n    }\n\n    if (status) {\n      where.isFinalized = status === 'completed';\n    }\n\n    const notes = await db.sessionNote.findMany({\n      where,\n      include: {\n        client: {\n          select: {\n            id: true,\n            firstName: true,\n            lastName: true,\n          }\n        },\n        multimedia: {\n          select: {\n            id: true,\n            fileType: true,\n            fileName: true,\n          }\n        },\n        _count: {\n          select: {\n            multimedia: true,\n            aiSuggestions: true,\n          }\n        }\n      },\n      orderBy: {\n        sessionDate: 'desc'\n      },\n      take: limit,\n    });\n\n    return NextResponse.json(notes);\n  } catch (error) {\n    console.error('Error fetching session notes:', error);\n    return NextResponse.json(\n      { error: 'Failed to fetch session notes' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n    \n    if (!session?.user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    }\n\n    const body = await request.json();\n    const {\n      clientId,\n      sessionDate,\n      sessionDuration,\n      noteType,\n      subjective,\n      objective,\n      assessment,\n      plan,\n      interventions,\n      clientMood,\n      isFinalized = false\n    } = body;\n\n    // Validate required fields\n    if (!clientId || !sessionDate) {\n      return NextResponse.json(\n        { error: 'Missing required fields: clientId and sessionDate' },\n        { status: 400 }\n      );\n    }\n\n    // Create the session note\n    const note = await db.sessionNote.create({\n      data: {\n        clientId,\n        therapistId: session.user.id,\n        sessionDate: new Date(sessionDate),\n        sessionDuration: sessionDuration ? parseInt(sessionDuration) : null,\n        noteType: noteType || 'SOAP',\n        subjective,\n        objective,\n        assessment,\n        plan,\n        interventions,\n        clientMood,\n        isFinalized,\n      },\n      include: {\n        client: {\n          select: {\n            id: true,\n            firstName: true,\n            lastName: true,\n          }\n        },\n        multimedia: true,\n      }\n    });\n\n    // Log the action for audit trail\n    await db.auditLog.create({\n      data: {\n        userId: session.user.id,\n        action: 'CREATE',\n        resourceType: 'SessionNote',\n        resourceId: note.id,\n        details: {\n          clientId,\n          noteType: note.noteType,\n          isFinalized,\n        },\n        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',\n        userAgent: request.headers.get('user-agent') || 'unknown',\n      }\n    });\n\n    return NextResponse.json(note, { status: 201 });\n  } catch (error) {\n    console.error('Error creating session note:', error);\n    return NextResponse.json(\n      { error: 'Failed to create session note' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QAEpD,MAAM,QAAa;YACjB,aAAa,QAAQ,IAAI,CAAC,EAAE;QAC9B;QAEA,IAAI,UAAU;YACZ,MAAM,QAAQ,GAAG;QACnB;QAEA,IAAI,QAAQ;YACV,MAAM,WAAW,GAAG,WAAW;QACjC;QAEA,MAAM,QAAQ,MAAM,kHAAA,CAAA,KAAE,CAAC,WAAW,CAAC,QAAQ,CAAC;YAC1C;YACA,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,WAAW;wBACX,UAAU;oBACZ;gBACF;gBACA,YAAY;oBACV,QAAQ;wBACN,IAAI;wBACJ,UAAU;wBACV,UAAU;oBACZ;gBACF;gBACA,QAAQ;oBACN,QAAQ;wBACN,YAAY;wBACZ,eAAe;oBACjB;gBACF;YACF;YACA,SAAS;gBACP,aAAa;YACf;YACA,MAAM;QACR;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgC,GACzC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,QAAQ,EACR,WAAW,EACX,eAAe,EACf,QAAQ,EACR,UAAU,EACV,SAAS,EACT,UAAU,EACV,IAAI,EACJ,aAAa,EACb,UAAU,EACV,cAAc,KAAK,EACpB,GAAG;QAEJ,2BAA2B;QAC3B,IAAI,CAAC,YAAY,CAAC,aAAa;YAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoD,GAC7D;gBAAE,QAAQ;YAAI;QAElB;QAEA,0BAA0B;QAC1B,MAAM,OAAO,MAAM,kHAAA,CAAA,KAAE,CAAC,WAAW,CAAC,MAAM,CAAC;YACvC,MAAM;gBACJ;gBACA,aAAa,QAAQ,IAAI,CAAC,EAAE;gBAC5B,aAAa,IAAI,KAAK;gBACtB,iBAAiB,kBAAkB,SAAS,mBAAmB;gBAC/D,UAAU,YAAY;gBACtB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;YACA,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,IAAI;wBACJ,WAAW;wBACX,UAAU;oBACZ;gBACF;gBACA,YAAY;YACd;QACF;QAEA,iCAAiC;QACjC,MAAM,kHAAA,CAAA,KAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;YACvB,MAAM;gBACJ,QAAQ,QAAQ,IAAI,CAAC,EAAE;gBACvB,QAAQ;gBACR,cAAc;gBACd,YAAY,KAAK,EAAE;gBACnB,SAAS;oBACP;oBACA,UAAU,KAAK,QAAQ;oBACvB;gBACF;gBACA,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB;gBACrD,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;YAClD;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAAE,QAAQ;QAAI;IAC/C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgC,GACzC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}