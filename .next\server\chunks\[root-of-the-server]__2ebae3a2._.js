module.exports = {

"[project]/.next-internal/server/app/api/ai/suggestions/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/ai.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "analyzeImageWithAI": (()=>analyzeImageWithAI),
    "generateAiSuggestion": (()=>generateAiSuggestion),
    "generateOpenAISuggestion": (()=>generateOpenAISuggestion),
    "getPromptTemplate": (()=>getPromptTemplate)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
;
// Initialize OpenAI client
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: process.env.OPENAI_API_KEY
});
async function generateAiSuggestion(request) {
    try {
        const response = await fetch('/api/ai/suggestions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(request)
        });
        if (!response.ok) {
            throw new Error('Failed to generate AI suggestion');
        }
        return await response.json();
    } catch (error) {
        console.error('Error generating AI suggestion:', error);
        throw new Error('AI service temporarily unavailable');
    }
}
async function generateOpenAISuggestion(request) {
    try {
        const prompt = buildPrompt(request);
        const completion = await openai.chat.completions.create({
            model: "gpt-4",
            messages: [
                {
                    role: "system",
                    content: "You are an expert Creative Arts Therapist assistant. Generate professional, clinical documentation that follows healthcare standards. Be concise, objective, and therapeutically appropriate. Focus on observable behaviors, emotional expression, and therapeutic progress."
                },
                {
                    role: "user",
                    content: prompt
                }
            ],
            max_tokens: 300,
            temperature: 0.7
        });
        const suggestion = completion.choices[0]?.message?.content || "Unable to generate suggestion";
        return {
            suggestion: suggestion.trim(),
            confidence: 0.85,
            alternatives: []
        };
    } catch (error) {
        console.error('OpenAI API error:', error);
        // Fallback to mock response if OpenAI fails
        return generateMockResponse(request);
    }
}
async function analyzeImageWithAI(imageBase64, context) {
    try {
        const completion = await openai.chat.completions.create({
            model: "gpt-4-vision-preview",
            messages: [
                {
                    role: "system",
                    content: "You are an expert Creative Arts Therapist analyzing client artwork. Provide professional, clinical observations about the visual elements, emotional expression, and therapeutic significance. Focus on colors, forms, composition, technique, and potential symbolic meaning."
                },
                {
                    role: "user",
                    content: [
                        {
                            type: "text",
                            text: `Analyze this artwork created during a Creative Arts Therapy session. ${context ? `Context: ${context}` : ''} Provide a clinical description suitable for therapy documentation.`
                        },
                        {
                            type: "image_url",
                            image_url: {
                                url: `data:image/jpeg;base64,${imageBase64}`
                            }
                        }
                    ]
                }
            ],
            max_tokens: 400,
            temperature: 0.6
        });
        const analysis = completion.choices[0]?.message?.content || "Unable to analyze image";
        return {
            suggestion: analysis.trim(),
            confidence: 0.80,
            alternatives: []
        };
    } catch (error) {
        console.error('OpenAI Vision API error:', error);
        // Fallback to text-based analysis
        return {
            suggestion: "Artwork demonstrates client's creative engagement and emotional expression through visual media. Further analysis available with image processing capabilities.",
            confidence: 0.60,
            alternatives: []
        };
    }
}
function buildPrompt(request) {
    const { type, context, mediaType, clientAge, sessionGoals, clientHistory, sessionDuration } = request;
    let prompt = `Generate a ${type.toLowerCase()} section for a Creative Arts Therapy SOAP note.\n\n`;
    if (context) {
        prompt += `Session Context: ${context}\n`;
    }
    if (mediaType) {
        prompt += `Media Type: ${mediaType} therapy session\n`;
    }
    if (clientAge) {
        prompt += `Client Age: ${clientAge}\n`;
    }
    if (sessionDuration) {
        prompt += `Session Duration: ${sessionDuration} minutes\n`;
    }
    if (sessionGoals) {
        prompt += `Session Goals: ${sessionGoals}\n`;
    }
    if (clientHistory) {
        prompt += `Relevant History: ${clientHistory}\n`;
    }
    prompt += `\nPlease provide a professional ${type.toLowerCase()} entry that is:\n`;
    switch(type){
        case 'OBJECTIVE':
            prompt += "- Factual and observable\n- Describes client behaviors and engagement\n- Notes creative process and artistic choices\n- Includes duration and participation level";
            break;
        case 'ASSESSMENT':
            prompt += "- Clinical interpretation of observations\n- Progress toward therapeutic goals\n- Emotional and psychological insights\n- Therapeutic relationship quality";
            break;
        case 'PLAN':
            prompt += "- Specific next steps and interventions\n- Goals for upcoming sessions\n- Recommendations for treatment\n- Any referrals or consultations needed";
            break;
        case 'DESCRIPTION':
            prompt += "- Detailed description of creative work\n- Colors, forms, techniques used\n- Artistic process and engagement\n- Observable emotional expression";
            break;
        case 'INTERPRETATION':
            prompt += "- Therapeutic meaning of creative expression\n- Symbolic or emotional significance\n- Connection to treatment goals\n- Clinical insights";
            break;
    }
    return prompt;
}
function generateMockResponse(request) {
    const mockResponses = {
        OBJECTIVE: "Client engaged actively in art-making process, demonstrating sustained attention for 30 minutes. Selected vibrant colors and created abstract forms with confident brushstrokes. Maintained appropriate eye contact and responded verbally to therapeutic prompts throughout session.",
        ASSESSMENT: "Client shows continued progress in emotional expression through creative media. Increased confidence in artistic choices and willingness to discuss emotional content of artwork. Mood appears stable with positive engagement in therapeutic process.",
        PLAN: "Continue weekly art therapy sessions focusing on emotional expression through color and form. Introduce new media (clay work) to explore tactile processing. Schedule follow-up assessment in 4 weeks to evaluate progress toward treatment goals.",
        DESCRIPTION: "Client created mixed-media artwork featuring bold, expressive brushstrokes in warm colors. The composition shows dynamic movement with overlapping forms and varied textures, suggesting emotional engagement and creative exploration.",
        INTERPRETATION: "The client's use of warm, vibrant colors may indicate improved mood and emotional accessibility. Bold, confident brushstrokes suggest increased self-assurance and willingness to take creative risks in the therapeutic space."
    };
    return {
        suggestion: mockResponses[request.type] || "AI-generated clinical suggestion based on session context.",
        confidence: 0.80,
        alternatives: []
    };
}
function getPromptTemplate(type, mediaType) {
    const templates = {
        DESCRIPTION: {
            IMAGE: "Describe this artwork created during a creative arts therapy session. Focus on visual elements like colors, shapes, composition, and artistic techniques used. Keep the description clinical and objective.",
            VIDEO: "Describe the movement and creative expression observed in this video from a creative arts therapy session. Focus on body language, movement patterns, and creative process.",
            AUDIO: "Describe the musical or vocal expression captured in this audio recording from a creative arts therapy session. Focus on rhythm, tone, emotional expression, and creative elements.",
            DEFAULT: "Provide a clinical description of the creative expression observed during this therapy session."
        },
        INTERPRETATION: {
            IMAGE: "Provide a clinical interpretation of this artwork, considering potential emotional expression, therapeutic themes, and symbolic elements that may be relevant for treatment planning.",
            VIDEO: "Analyze the movement and creative expression for therapeutic insights, considering emotional regulation, self-expression, and progress indicators.",
            AUDIO: "Interpret the musical/vocal expression for therapeutic significance, considering emotional state, communication patterns, and creative engagement.",
            DEFAULT: "Provide a clinical interpretation of the creative expression and its therapeutic significance."
        },
        OBJECTIVE: "Generate objective observations for a SOAP note based on the creative arts therapy session described. Focus on observable behaviors, creative process, and client engagement.",
        ASSESSMENT: "Provide a clinical assessment based on the creative arts therapy session observations. Consider therapeutic progress, emotional regulation, and treatment goals.",
        PLAN: "Suggest treatment plan elements based on the creative arts therapy session. Include specific interventions, goals, and recommendations for future sessions."
    };
    if (type in templates && typeof templates[type] === 'object') {
        return templates[type][mediaType || 'DEFAULT'];
    }
    return templates[type] || templates.DESCRIPTION.DEFAULT;
}
}}),
"[project]/src/app/api/ai/suggestions/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai.ts [app-route] (ecmascript)");
;
;
async function POST(request) {
    try {
        const body = await request.json();
        const { type, context, mediaType, clientAge, sessionGoals, clientHistory, sessionDuration } = body;
        // Validate required fields
        if (!type) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Missing required field: type'
            }, {
                status: 400
            });
        }
        // Build the AI request
        const aiRequest = {
            type,
            context: context || '',
            mediaType,
            clientAge,
            sessionGoals,
            clientHistory,
            sessionDuration
        };
        // Use real OpenAI API (with fallback to mock)
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateOpenAISuggestion"])(aiRequest);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            suggestion: response.suggestion,
            confidence: response.confidence,
            alternatives: response.alternatives
        });
    } catch (error) {
        console.error('Error generating AI suggestion:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to generate AI suggestion'
        }, {
            status: 500
        });
    }
}
async function generateMockAIResponse(type, context, mediaType) {
    // Simulate API delay
    await new Promise((resolve)=>setTimeout(resolve, 1000));
    const responses = {
        DESCRIPTION: {
            IMAGE: {
                suggestion: "Client created a mixed-media artwork featuring bold, expressive brushstrokes in warm colors (reds, oranges, yellows). The composition shows dynamic movement with overlapping forms and varied textures. The client demonstrated sustained focus and deliberate color choices throughout the 25-minute creative process.",
                confidence: 0.85,
                alternatives: [
                    "Artwork displays vibrant color palette with energetic brushwork, suggesting positive emotional engagement and creative expression.",
                    "Client's painting demonstrates confident mark-making and intentional composition, indicating therapeutic progress in self-expression."
                ]
            },
            VIDEO: {
                suggestion: "Client engaged in fluid movement therapy, demonstrating progressive relaxation and increased range of motion. Initial movements were tentative and restricted, evolving to more expansive and confident gestures. Client maintained appropriate spatial awareness and responded positively to musical cues.",
                confidence: 0.82,
                alternatives: [
                    "Movement session showed gradual increase in client's comfort level and willingness to explore physical expression.",
                    "Client demonstrated improved body awareness and emotional regulation through structured movement activities."
                ]
            },
            AUDIO: {
                suggestion: "Client's musical expression included rhythmic drumming with steady tempo and occasional dynamic variations. Demonstrated improved coordination and timing compared to previous sessions. Client verbalized positive feelings about the musical experience and showed increased confidence in creative expression.",
                confidence: 0.88,
                alternatives: [
                    "Audio recording captures client's growing musical confidence and improved rhythmic stability.",
                    "Musical session demonstrates client's enhanced focus and emotional regulation through sound exploration."
                ]
            }
        },
        INTERPRETATION: {
            IMAGE: {
                suggestion: "The client's use of warm, vibrant colors may indicate improved mood and emotional accessibility. The bold, confident brushstrokes suggest increased self-assurance and willingness to take creative risks. The central composition with radiating elements could represent the client's growing sense of personal agency and expanding social connections.",
                confidence: 0.78,
                alternatives: [
                    "Artwork suggests positive therapeutic progress with increased emotional expression and creative confidence.",
                    "Color choices and composition indicate client's developing sense of empowerment and emotional regulation."
                ]
            },
            DEFAULT: {
                suggestion: "Client's creative expression demonstrates therapeutic progress in emotional regulation and self-awareness. The willingness to engage in creative risk-taking suggests increased confidence and trust in the therapeutic process. Observable improvements in focus and sustained attention indicate positive response to creative arts interventions.",
                confidence: 0.80
            }
        },
        OBJECTIVE: {
            suggestion: "Client arrived punctually and engaged cooperatively in session activities. Demonstrated sustained attention for 35 minutes during art-making process. Made appropriate eye contact and responded verbally to therapeutic prompts. Exhibited calm demeanor with no observable signs of distress. Completed creative task independently with minimal guidance.",
            confidence: 0.90,
            alternatives: [
                "Client showed active participation and positive engagement throughout the 45-minute session with appropriate social interaction.",
                "Observable behaviors included focused attention, cooperative attitude, and willingness to discuss creative process and emotional content."
            ]
        },
        ASSESSMENT: {
            suggestion: "Client continues to demonstrate progress in emotional expression and self-regulation through creative arts interventions. Increased confidence in artistic choices and willingness to discuss emotional content of creative work indicates positive therapeutic alliance. Current presentation suggests stable mood with continued engagement in treatment goals.",
            confidence: 0.85,
            alternatives: [
                "Client shows sustained improvement in creative self-expression and emotional processing capabilities.",
                "Therapeutic progress evident in client's increased openness and willingness to explore emotional themes through art."
            ]
        },
        PLAN: {
            suggestion: "Continue weekly creative arts therapy sessions focusing on emotional expression through visual media. Introduce new artistic materials (clay, pastels) to expand creative vocabulary. Schedule follow-up assessment in 4 weeks to evaluate progress toward treatment goals. Consider group therapy referral to enhance social skills development.",
            confidence: 0.87,
            alternatives: [
                "Maintain current treatment frequency with gradual introduction of more challenging creative tasks to build confidence.",
                "Continue individual sessions while exploring opportunities for peer interaction through structured creative activities."
            ]
        }
    };
    // Get response based on type and media type
    const typeResponses = responses[type];
    if (!typeResponses) {
        return {
            suggestion: "AI-generated clinical suggestion based on session context and observations.",
            confidence: 0.75
        };
    }
    if (typeof typeResponses === 'object' && 'suggestion' in typeResponses) {
        return typeResponses;
    }
    const mediaResponse = typeResponses[mediaType] || typeResponses['DEFAULT'];
    return mediaResponse || {
        suggestion: "AI-generated clinical suggestion based on session context and observations.",
        confidence: 0.75
    };
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__2ebae3a2._.js.map