import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');

    const where: any = {
      isActive: status === 'inactive' ? false : true,
    };

    if (search) {
      where.OR = [
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } },
      ];
    }

    const clients = await db.client.findMany({
      where,
      include: {
        sessionNotes: {
          select: {
            id: true,
            sessionDate: true,
            isFinalized: true,
          },
          orderBy: {
            sessionDate: 'desc'
          },
          take: 1, // Get most recent session
        },
        _count: {
          select: {
            sessionNotes: true,
          }
        }
      },
      orderBy: [
        { lastName: 'asc' },
        { firstName: 'asc' }
      ],
      take: limit,
    });

    // Transform the data to include computed fields
    const clientsWithStats = clients.map(client => ({
      ...client,
      lastSession: client.sessionNotes[0]?.sessionDate || null,
      totalSessions: client._count.sessionNotes,
      status: client.isActive ? 'Active' : 'Inactive',
    }));

    return NextResponse.json(clientsWithStats);
  } catch (error) {
    console.error('Error fetching clients:', error);
    return NextResponse.json(
      { error: 'Failed to fetch clients' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { firstName, lastName, dateOfBirth, ehrId } = body;

    // Validate required fields
    if (!firstName || !lastName) {
      return NextResponse.json(
        { error: 'Missing required fields: firstName and lastName' },
        { status: 400 }
      );
    }

    // Check if EHR ID is unique (if provided)
    if (ehrId) {
      const existingClient = await db.client.findUnique({
        where: { ehrId }
      });

      if (existingClient) {
        return NextResponse.json(
          { error: 'Client with this EHR ID already exists' },
          { status: 400 }
        );
      }
    }

    // Create the client
    const client = await db.client.create({
      data: {
        firstName,
        lastName,
        dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : null,
        ehrId,
      }
    });

    // Log the action for audit trail
    await db.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'CREATE',
        resourceType: 'Client',
        resourceId: client.id,
        details: {
          firstName,
          lastName,
          ehrId,
        },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      }
    });

    return NextResponse.json(client, { status: 201 });
  } catch (error) {
    console.error('Error creating client:', error);
    return NextResponse.json(
      { error: 'Failed to create client' },
      { status: 500 }
    );
  }
}
