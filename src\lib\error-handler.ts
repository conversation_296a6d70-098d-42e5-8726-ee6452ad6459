import { NextResponse } from 'next/server';
import { trackError } from './monitoring';

export interface ApiError {
  message: string;
  code?: string;
  statusCode: number;
  details?: any;
  userId?: string;
}

export class VistaNoteError extends Error {
  public statusCode: number;
  public code?: string;
  public details?: any;
  public userId?: string;

  constructor(message: string, statusCode: number = 500, code?: string, details?: any, userId?: string) {
    super(message);
    this.name = 'VistaNoteError';
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.userId = userId;
  }
}

// Predefined error types
export class ValidationError extends VistaNoteError {
  constructor(message: string, details?: any, userId?: string) {
    super(message, 400, 'VALIDATION_ERROR', details, userId);
  }
}

export class AuthenticationError extends VistaNoteError {
  constructor(message: string = 'Authentication required', userId?: string) {
    super(message, 401, 'AUTHENTICATION_ERROR', undefined, userId);
  }
}

export class AuthorizationError extends VistaNoteError {
  constructor(message: string = 'Access denied', userId?: string) {
    super(message, 403, 'AUTHORIZATION_ERROR', undefined, userId);
  }
}

export class NotFoundError extends VistaNoteError {
  constructor(resource: string, userId?: string) {
    super(`${resource} not found`, 404, 'NOT_FOUND_ERROR', { resource }, userId);
  }
}

export class ConflictError extends VistaNoteError {
  constructor(message: string, details?: any, userId?: string) {
    super(message, 409, 'CONFLICT_ERROR', details, userId);
  }
}

export class RateLimitError extends VistaNoteError {
  constructor(message: string = 'Rate limit exceeded', userId?: string) {
    super(message, 429, 'RATE_LIMIT_ERROR', undefined, userId);
  }
}

export class ExternalServiceError extends VistaNoteError {
  constructor(service: string, originalError?: any, userId?: string) {
    super(`External service error: ${service}`, 502, 'EXTERNAL_SERVICE_ERROR', { service, originalError }, userId);
  }
}

// Error handler for API routes
export function handleApiError(error: any, userId?: string): NextResponse {
  let apiError: ApiError;

  if (error instanceof VistaNoteError) {
    apiError = {
      message: error.message,
      code: error.code,
      statusCode: error.statusCode,
      details: error.details,
      userId: error.userId || userId,
    };
  } else if (error.name === 'PrismaClientKnownRequestError') {
    // Handle Prisma database errors
    apiError = handlePrismaError(error, userId);
  } else if (error.name === 'ValidationError') {
    // Handle validation errors (e.g., from Zod)
    apiError = {
      message: 'Validation failed',
      code: 'VALIDATION_ERROR',
      statusCode: 400,
      details: error.errors || error.message,
      userId,
    };
  } else {
    // Generic error
    apiError = {
      message: process.env.NODE_ENV === 'production' 
        ? 'An unexpected error occurred' 
        : error.message || 'Unknown error',
      code: 'INTERNAL_ERROR',
      statusCode: 500,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      userId,
    };
  }

  // Track the error for monitoring
  trackError({
    message: apiError.message,
    stack: error.stack,
    userId: apiError.userId,
    severity: getSeverityFromStatusCode(apiError.statusCode),
    metadata: {
      code: apiError.code,
      statusCode: apiError.statusCode,
      details: apiError.details,
    },
  });

  // Return appropriate response
  return NextResponse.json(
    {
      error: {
        message: apiError.message,
        code: apiError.code,
        ...(apiError.details && { details: apiError.details }),
      },
    },
    { status: apiError.statusCode }
  );
}

function handlePrismaError(error: any, userId?: string): ApiError {
  switch (error.code) {
    case 'P2002':
      return {
        message: 'A record with this information already exists',
        code: 'DUPLICATE_RECORD',
        statusCode: 409,
        details: { field: error.meta?.target },
        userId,
      };
    case 'P2025':
      return {
        message: 'Record not found',
        code: 'RECORD_NOT_FOUND',
        statusCode: 404,
        userId,
      };
    case 'P2003':
      return {
        message: 'Foreign key constraint failed',
        code: 'FOREIGN_KEY_ERROR',
        statusCode: 400,
        details: { field: error.meta?.field_name },
        userId,
      };
    case 'P2014':
      return {
        message: 'Invalid data provided',
        code: 'INVALID_DATA',
        statusCode: 400,
        details: error.meta,
        userId,
      };
    default:
      return {
        message: 'Database operation failed',
        code: 'DATABASE_ERROR',
        statusCode: 500,
        details: process.env.NODE_ENV === 'development' ? error.message : undefined,
        userId,
      };
  }
}

function getSeverityFromStatusCode(statusCode: number): 'low' | 'medium' | 'high' | 'critical' {
  if (statusCode >= 500) return 'critical';
  if (statusCode >= 400) return 'medium';
  return 'low';
}

// Async wrapper for API route handlers
export function withErrorHandling(
  handler: (request: Request, context?: any) => Promise<NextResponse>
) {
  return async (request: Request, context?: any): Promise<NextResponse> => {
    try {
      return await handler(request, context);
    } catch (error) {
      console.error('API Error:', error);
      return handleApiError(error);
    }
  };
}

// Client-side error boundary helper
export function handleClientError(error: any, context?: string) {
  const errorInfo = {
    message: error.message || 'An unexpected error occurred',
    stack: error.stack,
    severity: 'medium' as const,
    metadata: {
      context,
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
    },
  };

  trackError(errorInfo);

  // In development, log to console
  if (process.env.NODE_ENV === 'development') {
    console.error(`[CLIENT ERROR] ${context}:`, error);
  }

  // Return user-friendly error message
  return {
    message: 'Something went wrong. Please try again.',
    canRetry: error.name !== 'ValidationError',
  };
}

// Validation helper
export function validateRequired(data: any, fields: string[]): void {
  const missing = fields.filter(field => !data[field]);
  if (missing.length > 0) {
    throw new ValidationError(
      `Missing required fields: ${missing.join(', ')}`,
      { missingFields: missing }
    );
  }
}

// File validation helper
export function validateFile(file: File, options: {
  maxSize?: number;
  allowedTypes?: string[];
  maxFiles?: number;
}): void {
  if (options.maxSize && file.size > options.maxSize) {
    throw new ValidationError(
      `File size exceeds limit of ${options.maxSize / 1024 / 1024}MB`,
      { fileSize: file.size, maxSize: options.maxSize }
    );
  }

  if (options.allowedTypes && !options.allowedTypes.includes(file.type)) {
    throw new ValidationError(
      `File type ${file.type} is not allowed`,
      { fileType: file.type, allowedTypes: options.allowedTypes }
    );
  }
}
