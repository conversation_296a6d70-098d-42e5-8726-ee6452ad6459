// Test real OpenAI API integration
const baseUrl = 'http://localhost:3000';

async function testRealAI() {
  console.log('🤖 Testing Real OpenAI Integration\n');
  
  try {
    // Test 1: Basic AI suggestion
    console.log('1. Testing basic AI text suggestion...');
    const basicResponse = await fetch(`${baseUrl}/api/ai/suggestions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'OBJECTIVE',
        context: 'Client created a vibrant painting using bold red and orange colors during today\'s art therapy session. They worked for 45 minutes and seemed very focused.',
        sessionInfo: {
          clientAge: 28,
          sessionType: 'Art Therapy',
          duration: 45
        }
      })
    });
    
    const basicData = await basicResponse.json();
    console.log(`   Status: ${basicResponse.status}`);
    console.log(`   AI Response: ${basicData.suggestion?.substring(0, 150)}...`);
    console.log(`   Confidence: ${basicData.confidence}`);
    
    // Test 2: Assessment suggestion
    console.log('\n2. Testing assessment AI suggestion...');
    const assessmentResponse = await fetch(`${baseUrl}/api/ai/suggestions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'ASSESSMENT',
        context: 'Client showed increased emotional expression through color choices. Used warm colors predominantly. Engaged well with therapeutic process and asked thoughtful questions about their artwork.',
        sessionInfo: {
          clientAge: 35,
          sessionType: 'Art Therapy',
          goals: ['emotional expression', 'self-awareness']
        }
      })
    });
    
    const assessmentData = await assessmentResponse.json();
    console.log(`   Status: ${assessmentResponse.status}`);
    console.log(`   AI Response: ${assessmentData.suggestion?.substring(0, 150)}...`);
    
    // Test 3: Treatment plan suggestion
    console.log('\n3. Testing treatment plan suggestion...');
    const planResponse = await fetch(`${baseUrl}/api/ai/suggestions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'PLAN',
        context: 'Client has made good progress with emotional expression through art. Ready to explore more challenging themes. Interested in trying new media.',
        sessionInfo: {
          sessionNumber: 8,
          clientGoals: ['emotional regulation', 'creative confidence'],
          recentProgress: 'improved emotional expression, increased engagement'
        }
      })
    });
    
    const planData = await planResponse.json();
    console.log(`   Status: ${planResponse.status}`);
    console.log(`   AI Response: ${planData.suggestion?.substring(0, 150)}...`);
    
    // Test 4: Check system health
    console.log('\n4. Checking system health...');
    const healthResponse = await fetch(`${baseUrl}/api/health`);
    const healthData = await healthResponse.json();
    console.log(`   Health Status: ${healthResponse.status}`);
    console.log(`   OpenAI Status: ${healthData.services.openai}`);
    
    console.log('\n✅ Real AI Integration Test Complete');
    
    // Summary
    console.log('\n📊 Test Results:');
    console.log(`- Basic AI suggestions: ${basicResponse.status === 200 ? '✅ Working' : '❌ Failed'}`);
    console.log(`- Assessment suggestions: ${assessmentResponse.status === 200 ? '✅ Working' : '❌ Failed'}`);
    console.log(`- Treatment plan suggestions: ${planResponse.status === 200 ? '✅ Working' : '❌ Failed'}`);
    console.log(`- OpenAI API: ${healthData.services.openai === 'configured' ? '✅ Configured' : '❌ Not configured'}`);
    
    // Check if we're getting real AI responses vs fallbacks
    const isRealAI = basicData.suggestion && !basicData.suggestion.includes('Client engaged actively in art-making process');
    console.log(`- Real AI responses: ${isRealAI ? '✅ Getting real OpenAI responses' : '⚠️ Still using fallbacks'}`);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testRealAI();
