{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VistaNotes/src/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { \n  Plus, \n  FileText, \n  Users, \n  Calendar,\n  Clock,\n  TrendingUp,\n  Image as ImageIcon,\n  Video,\n  Music\n} from \"lucide-react\";\n\nexport default function Dashboard() {\n  const [recentNotes] = useState([\n    {\n      id: \"1\",\n      clientName: \"<PERSON> M.\",\n      sessionDate: \"2024-01-15\",\n      noteType: \"SOAP\",\n      hasMultimedia: true,\n      mediaTypes: [\"image\", \"audio\"],\n      status: \"completed\"\n    },\n    {\n      id: \"2\", \n      clientName: \"Michael R.\",\n      sessionDate: \"2024-01-14\",\n      noteType: \"SOAP\",\n      hasMultimedia: true,\n      mediaTypes: [\"video\"],\n      status: \"draft\"\n    },\n    {\n      id: \"3\",\n      clientName: \"Emma L.\",\n      sessionDate: \"2024-01-12\",\n      noteType: \"SOAP\", \n      hasMultimedia: false,\n      mediaTypes: [],\n      status: \"completed\"\n    }\n  ]);\n\n  const [stats] = useState({\n    totalClients: 24,\n    notesThisWeek: 12,\n    avgSessionTime: 45,\n    completionRate: 94\n  });\n\n  const getMediaIcon = (type: string) => {\n    switch (type) {\n      case \"image\":\n        return <ImageIcon className=\"h-4 w-4 text-green-600\" />;\n      case \"video\":\n        return <Video className=\"h-4 w-4 text-blue-600\" />;\n      case \"audio\":\n        return <Music className=\"h-4 w-4 text-purple-600\" />;\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"flex-shrink-0\">\n                <h1 className=\"text-2xl font-bold text-gray-900\">VistaNotes</h1>\n              </Link>\n            </div>\n            <nav className=\"hidden md:flex space-x-8\">\n              <Link href=\"/dashboard\" className=\"text-indigo-600 font-medium\">\n                Dashboard\n              </Link>\n              <Link href=\"/clients\" className=\"text-gray-500 hover:text-gray-900\">\n                Clients\n              </Link>\n              <Link href=\"/notes\" className=\"text-gray-500 hover:text-gray-900\">\n                Session Notes\n              </Link>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Welcome Section */}\n        <div className=\"mb-8\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            Welcome back, Dr. Thompson\n          </h2>\n          <p className=\"text-gray-600\">\n            Here's an overview of your creative arts therapy practice\n          </p>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <Users className=\"h-8 w-8 text-indigo-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Total Clients</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{stats.totalClients}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <FileText className=\"h-8 w-8 text-green-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Notes This Week</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{stats.notesThisWeek}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <Clock className=\"h-8 w-8 text-blue-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Avg Session Time</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{stats.avgSessionTime}m</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <TrendingUp className=\"h-8 w-8 text-purple-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Completion Rate</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{stats.completionRate}%</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8\">\n          <div className=\"lg:col-span-2\">\n            {/* Recent Session Notes */}\n            <div className=\"bg-white rounded-lg shadow\">\n              <div className=\"px-6 py-4 border-b border-gray-200\">\n                <div className=\"flex items-center justify-between\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">Recent Session Notes</h3>\n                  <Link\n                    href=\"/notes\"\n                    className=\"text-sm text-indigo-600 hover:text-indigo-500\"\n                  >\n                    View all\n                  </Link>\n                </div>\n              </div>\n              <div className=\"divide-y divide-gray-200\">\n                {recentNotes.map((note) => (\n                  <div key={note.id} className=\"px-6 py-4 hover:bg-gray-50\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center space-x-3\">\n                          <h4 className=\"text-sm font-medium text-gray-900\">\n                            {note.clientName}\n                          </h4>\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                            note.status === 'completed' \n                              ? 'bg-green-100 text-green-800'\n                              : 'bg-yellow-100 text-yellow-800'\n                          }`}>\n                            {note.status}\n                          </span>\n                        </div>\n                        <div className=\"mt-1 flex items-center space-x-4\">\n                          <p className=\"text-sm text-gray-500\">\n                            {new Date(note.sessionDate).toLocaleDateString()}\n                          </p>\n                          <p className=\"text-sm text-gray-500\">{note.noteType} Note</p>\n                          {note.hasMultimedia && (\n                            <div className=\"flex items-center space-x-1\">\n                              {note.mediaTypes.map((type, index) => (\n                                <span key={index}>{getMediaIcon(type)}</span>\n                              ))}\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                      <div className=\"flex-shrink-0\">\n                        <Link\n                          href={`/notes/${note.id}`}\n                          className=\"text-indigo-600 hover:text-indigo-500 text-sm font-medium\"\n                        >\n                          View\n                        </Link>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          <div>\n            {/* Quick Actions */}\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Quick Actions</h3>\n              <div className=\"space-y-3\">\n                <Link\n                  href=\"/notes/new\"\n                  className=\"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700\"\n                >\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  New Session Note\n                </Link>\n                <Link\n                  href=\"/clients/new\"\n                  className=\"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n                >\n                  <Users className=\"h-4 w-4 mr-2\" />\n                  Add Client\n                </Link>\n                <Link\n                  href=\"/schedule\"\n                  className=\"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n                >\n                  <Calendar className=\"h-4 w-4 mr-2\" />\n                  View Schedule\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAgBe,SAAS;IACtB,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7B;YACE,IAAI;YACJ,YAAY;YACZ,aAAa;YACb,UAAU;YACV,eAAe;YACf,YAAY;gBAAC;gBAAS;aAAQ;YAC9B,QAAQ;QACV;QACA;YACE,IAAI;YACJ,YAAY;YACZ,aAAa;YACb,UAAU;YACV,eAAe;YACf,YAAY;gBAAC;aAAQ;YACrB,QAAQ;QACV;QACA;YACE,IAAI;YACJ,YAAY;YACZ,aAAa;YACb,UAAU;YACV,eAAe;YACf,YAAY,EAAE;YACd,QAAQ;QACV;KACD;IAED,MAAM,CAAC,MAAM,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvB,cAAc;QACd,eAAe;QACf,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAS;oBAAC,WAAU;;;;;;YAC9B,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CACvB,cAAA,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;;;;;;;;;;;0CAGrD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAA8B;;;;;;kDAGhE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAoC;;;;;;kDAGpE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS1E,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAM/B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAoC,MAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;;0CAKzE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAoC,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;0CAK1E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;;wDAAoC,MAAM,cAAc;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAK5E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;;wDAAoC,MAAM,cAAc;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO9E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAEb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;kEACX;;;;;;;;;;;;;;;;;sDAKL,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC;oDAAkB,WAAU;8DAC3B,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAG,WAAU;0FACX,KAAK,UAAU;;;;;;0FAElB,8OAAC;gFAAK,WAAW,CAAC,wEAAwE,EACxF,KAAK,MAAM,KAAK,cACZ,gCACA,iCACJ;0FACC,KAAK,MAAM;;;;;;;;;;;;kFAGhB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;0FACV,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB;;;;;;0FAEhD,8OAAC;gFAAE,WAAU;;oFAAyB,KAAK,QAAQ;oFAAC;;;;;;;4EACnD,KAAK,aAAa,kBACjB,8OAAC;gFAAI,WAAU;0FACZ,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC;kGAAkB,aAAa;uFAArB;;;;;;;;;;;;;;;;;;;;;;0EAMrB,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEACH,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;oEACzB,WAAU;8EACX;;;;;;;;;;;;;;;;;mDAjCG,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;0CA4CzB,8OAAC;0CAEC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGnC,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGpC,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvD", "debugId": null}}]}