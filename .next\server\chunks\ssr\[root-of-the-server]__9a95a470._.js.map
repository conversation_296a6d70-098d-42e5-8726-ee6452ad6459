{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VistaNotes/src/app/notes/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { \n  Plus, \n  Search, \n  Filter,\n  FileText,\n  Image as ImageIcon,\n  Video,\n  Music,\n  Calendar,\n  User\n} from \"lucide-react\";\n\nexport default function SessionNotes() {\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [statusFilter, setStatusFilter] = useState(\"\");\n  \n  // Mock session notes data\n  const [notes] = useState([\n    {\n      id: \"1\",\n      clientName: \"<PERSON> M.\",\n      clientId: \"1\",\n      sessionDate: \"2024-01-15\",\n      noteType: \"SOAP\",\n      status: \"completed\",\n      hasMultimedia: true,\n      mediaTypes: [\"image\", \"audio\"],\n      duration: 45,\n      lastModified: \"2024-01-15T14:30:00Z\"\n    },\n    {\n      id: \"2\",\n      clientName: \"<PERSON>.\",\n      clientId: \"2\", \n      sessionDate: \"2024-01-14\",\n      noteType: \"SOAP\",\n      status: \"draft\",\n      hasMultimedia: true,\n      mediaTypes: [\"video\"],\n      duration: 50,\n      lastModified: \"2024-01-14T16:45:00Z\"\n    },\n    {\n      id: \"3\",\n      clientName: \"Emma L.\",\n      clientId: \"3\",\n      sessionDate: \"2024-01-12\",\n      noteType: \"SOAP\",\n      status: \"completed\",\n      hasMultimedia: false,\n      mediaTypes: [],\n      duration: 40,\n      lastModified: \"2024-01-12T11:20:00Z\"\n    },\n    {\n      id: \"4\",\n      clientName: \"David K.\",\n      clientId: \"4\",\n      sessionDate: \"2024-01-10\",\n      noteType: \"DAP\",\n      status: \"completed\",\n      hasMultimedia: true,\n      mediaTypes: [\"image\", \"video\", \"audio\"],\n      duration: 60,\n      lastModified: \"2024-01-10T15:15:00Z\"\n    },\n    {\n      id: \"5\",\n      clientName: \"Sarah M.\",\n      clientId: \"1\",\n      sessionDate: \"2024-01-08\",\n      noteType: \"SOAP\",\n      status: \"completed\",\n      hasMultimedia: true,\n      mediaTypes: [\"image\"],\n      duration: 45,\n      lastModified: \"2024-01-08T13:30:00Z\"\n    }\n  ]);\n\n  const filteredNotes = notes.filter(note => {\n    const matchesSearch = note.clientName.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = !statusFilter || note.status === statusFilter;\n    return matchesSearch && matchesStatus;\n  });\n\n  const getMediaIcon = (type: string) => {\n    switch (type) {\n      case \"image\":\n        return <ImageIcon className=\"h-4 w-4 text-green-600\" />;\n      case \"video\":\n        return <Video className=\"h-4 w-4 text-blue-600\" />;\n      case \"audio\":\n        return <Music className=\"h-4 w-4 text-purple-600\" />;\n      default:\n        return null;\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  const formatTime = (dateString: string) => {\n    return new Date(dateString).toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"flex-shrink-0 mr-8\">\n                <h1 className=\"text-2xl font-bold text-gray-900\">VistaNotes</h1>\n              </Link>\n              <nav className=\"hidden md:flex space-x-8\">\n                <Link href=\"/dashboard\" className=\"text-gray-500 hover:text-gray-900\">\n                  Dashboard\n                </Link>\n                <Link href=\"/clients\" className=\"text-gray-500 hover:text-gray-900\">\n                  Clients\n                </Link>\n                <Link href=\"/notes\" className=\"text-indigo-600 font-medium\">\n                  Session Notes\n                </Link>\n              </nav>\n            </div>\n            <Link\n              href=\"/notes/new\"\n              className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700\"\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              New Session Note\n            </Link>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Page Header */}\n        <div className=\"mb-8\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">Session Notes</h2>\n          <p className=\"text-gray-600\">View and manage all therapy session documentation</p>\n        </div>\n\n        {/* Search and Filters */}\n        <div className=\"bg-white rounded-lg shadow p-6 mb-8\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search by client name...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n                />\n              </div>\n            </div>\n            <div className=\"flex space-x-3\">\n              <select \n                value={statusFilter}\n                onChange={(e) => setStatusFilter(e.target.value)}\n                className=\"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n              >\n                <option value=\"\">All Status</option>\n                <option value=\"completed\">Completed</option>\n                <option value=\"draft\">Draft</option>\n              </select>\n              <button className=\"inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\">\n                <Filter className=\"h-4 w-4 mr-2\" />\n                More Filters\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Notes List */}\n        <div className=\"bg-white rounded-lg shadow overflow-hidden\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <h3 className=\"text-lg font-medium text-gray-900\">\n              Session Notes ({filteredNotes.length})\n            </h3>\n          </div>\n          \n          <div className=\"divide-y divide-gray-200\">\n            {filteredNotes.map((note) => (\n              <div key={note.id} className=\"px-6 py-4 hover:bg-gray-50\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center space-x-3 mb-2\">\n                      <div className=\"flex items-center space-x-2\">\n                        <User className=\"h-4 w-4 text-gray-400\" />\n                        <h4 className=\"text-sm font-medium text-gray-900\">\n                          {note.clientName}\n                        </h4>\n                      </div>\n                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                        note.status === 'completed' \n                          ? 'bg-green-100 text-green-800'\n                          : 'bg-yellow-100 text-yellow-800'\n                      }`}>\n                        {note.status}\n                      </span>\n                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                        {note.noteType}\n                      </span>\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-6 text-sm text-gray-500\">\n                      <div className=\"flex items-center space-x-1\">\n                        <Calendar className=\"h-4 w-4\" />\n                        <span>{formatDate(note.sessionDate)}</span>\n                      </div>\n                      <div>\n                        Duration: {note.duration}min\n                      </div>\n                      <div>\n                        Modified: {formatTime(note.lastModified)}\n                      </div>\n                      {note.hasMultimedia && (\n                        <div className=\"flex items-center space-x-1\">\n                          <span className=\"text-xs\">Media:</span>\n                          {note.mediaTypes.map((type, index) => (\n                            <span key={index}>{getMediaIcon(type)}</span>\n                          ))}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-center space-x-3\">\n                    <Link\n                      href={`/notes/${note.id}`}\n                      className=\"text-indigo-600 hover:text-indigo-900 text-sm font-medium\"\n                    >\n                      View\n                    </Link>\n                    <Link\n                      href={`/notes/${note.id}/edit`}\n                      className=\"text-gray-600 hover:text-gray-900 text-sm font-medium\"\n                    >\n                      Edit\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {filteredNotes.length === 0 && (\n            <div className=\"text-center py-12\">\n              <FileText className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No session notes found</h3>\n              <p className=\"text-gray-600 mb-4\">\n                {searchTerm ? 'Try adjusting your search terms.' : 'Get started by creating your first session note.'}\n              </p>\n              <Link\n                href=\"/notes/new\"\n                className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700\"\n              >\n                <Plus className=\"h-4 w-4 mr-2\" />\n                New Session Note\n              </Link>\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAgBe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,0BAA0B;IAC1B,MAAM,CAAC,MAAM,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvB;YACE,IAAI;YACJ,YAAY;YACZ,UAAU;YACV,aAAa;YACb,UAAU;YACV,QAAQ;YACR,eAAe;YACf,YAAY;gBAAC;gBAAS;aAAQ;YAC9B,UAAU;YACV,cAAc;QAChB;QACA;YACE,IAAI;YACJ,YAAY;YAC<PERSON>,UAAU;YACV,aAAa;YACb,UAAU;YACV,QAAQ;YACR,eAAe;YACf,YAAY;gBAAC;aAAQ;YACrB,UAAU;YACV,cAAc;QAChB;QACA;YACE,IAAI;YACJ,YAAY;YACZ,UAAU;YACV,aAAa;YACb,UAAU;YACV,QAAQ;YACR,eAAe;YACf,YAAY,EAAE;YACd,UAAU;YACV,cAAc;QAChB;QACA;YACE,IAAI;YACJ,YAAY;YACZ,UAAU;YACV,aAAa;YACb,UAAU;YACV,QAAQ;YACR,eAAe;YACf,YAAY;gBAAC;gBAAS;gBAAS;aAAQ;YACvC,UAAU;YACV,cAAc;QAChB;QACA;YACE,IAAI;YACJ,YAAY;YACZ,UAAU;YACV,aAAa;YACb,UAAU;YACV,QAAQ;YACR,eAAe;YACf,YAAY;gBAAC;aAAQ;YACrB,UAAU;YACV,cAAc;QAChB;KACD;IAED,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,MAAM,gBAAgB,KAAK,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACnF,MAAM,gBAAgB,CAAC,gBAAgB,KAAK,MAAM,KAAK;QACvD,OAAO,iBAAiB;IAC1B;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAS;oBAAC,WAAU;;;;;;YAC9B,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,QAAQ;QACV;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDACvB,cAAA,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;;;;;;kDAEnD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAoC;;;;;;0DAGtE,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAoC;;;;;;0DAGpE,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;0CAKhE,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;0BAQzC,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAI/B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAC/C,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,8OAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;;;;;;;sDAExB,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;kCAQ3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;;wCAAoC;wCAChC,cAAc,MAAM;wCAAC;;;;;;;;;;;;0CAIzC,8OAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;wCAAkB,WAAU;kDAC3B,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,8OAAC;4EAAG,WAAU;sFACX,KAAK,UAAU;;;;;;;;;;;;8EAGpB,8OAAC;oEAAK,WAAW,CAAC,wEAAwE,EACxF,KAAK,MAAM,KAAK,cACZ,gCACA,iCACJ;8EACC,KAAK,MAAM;;;;;;8EAEd,8OAAC;oEAAK,WAAU;8EACb,KAAK,QAAQ;;;;;;;;;;;;sEAIlB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,0MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;sFACpB,8OAAC;sFAAM,WAAW,KAAK,WAAW;;;;;;;;;;;;8EAEpC,8OAAC;;wEAAI;wEACQ,KAAK,QAAQ;wEAAC;;;;;;;8EAE3B,8OAAC;;wEAAI;wEACQ,WAAW,KAAK,YAAY;;;;;;;gEAExC,KAAK,aAAa,kBACjB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAU;;;;;;wEACzB,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC;0FAAkB,aAAa;+EAArB;;;;;;;;;;;;;;;;;;;;;;;8DAOrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;4DACzB,WAAU;sEACX;;;;;;sEAGD,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;4DAC9B,WAAU;sEACX;;;;;;;;;;;;;;;;;;uCAtDG,KAAK,EAAE;;;;;;;;;;4BA+DpB,cAAc,MAAM,KAAK,mBACxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAE,WAAU;kDACV,aAAa,qCAAqC;;;;;;kDAErD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjD", "debugId": null}}]}