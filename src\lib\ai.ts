import OpenAI from 'openai'
import Groq from 'groq-sdk'

export interface AiSuggestionRequest {
  type: 'DESCRIPTION' | 'INTERPRETATION' | 'OBJECTIVE' | 'ASSESSMENT' | 'PLAN'
  context: string
  mediaType?: 'IMAGE' | 'VIDEO' | 'AUDIO'
  clientAge?: number
  sessionGoals?: string
  clientHistory?: string
  sessionDuration?: number
}

export interface AiSuggestionResponse {
  suggestion: string
  confidence: number
  alternatives?: string[]
}

// Initialize AI clients
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

const groq = new Groq({
  apiKey: process.env.GROQ_API_KEY,
})

// AI Provider configuration
const AI_PROVIDER = process.env.AI_PROVIDER || 'groq'

export async function generateAiSuggestion(
  request: AiSuggestionRequest
): Promise<AiSuggestionResponse> {
  try {
    const response = await fetch('/api/ai/suggestions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    })

    if (!response.ok) {
      throw new Error('Failed to generate AI suggestion')
    }

    return await response.json()
  } catch (error) {
    console.error('Error generating AI suggestion:', error)
    throw new Error('AI service temporarily unavailable')
  }
}

export async function generateAISuggestion(
  request: AiSuggestionRequest
): Promise<AiSuggestionResponse> {
  if (AI_PROVIDER === 'groq') {
    return generateGroqSuggestion(request)
  } else {
    return generateOpenAISuggestion(request)
  }
}

export async function generateGroqSuggestion(
  request: AiSuggestionRequest
): Promise<AiSuggestionResponse> {
  try {
    const prompt = buildPrompt(request)

    const completion = await groq.chat.completions.create({
      model: "llama3-8b-8192", // Current fast and reliable model
      messages: [
        {
          role: "system",
          content: "You are an expert Creative Arts Therapist assistant. Generate professional, clinical documentation that follows healthcare standards. Be concise, objective, and therapeutically appropriate. Focus on observable behaviors, emotional expression, and therapeutic progress."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 300,
      temperature: 0.7,
    })

    const suggestion = completion.choices[0]?.message?.content || "Unable to generate suggestion"

    return {
      suggestion: suggestion.trim(),
      confidence: 0.88, // Groq models are very good
      alternatives: []
    }
  } catch (error) {
    console.error('Groq API error:', error)
    // Fallback to mock response if Groq fails
    return generateMockResponse(request)
  }
}

export async function generateOpenAISuggestion(
  request: AiSuggestionRequest
): Promise<AiSuggestionResponse> {
  try {
    const prompt = buildPrompt(request)

    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are an expert Creative Arts Therapist assistant. Generate professional, clinical documentation that follows healthcare standards. Be concise, objective, and therapeutically appropriate. Focus on observable behaviors, emotional expression, and therapeutic progress."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 300,
      temperature: 0.7,
    })

    const suggestion = completion.choices[0]?.message?.content || "Unable to generate suggestion"

    return {
      suggestion: suggestion.trim(),
      confidence: 0.85,
      alternatives: []
    }
  } catch (error) {
    console.error('OpenAI API error:', error)
    // Fallback to mock response if OpenAI fails
    return generateMockResponse(request)
  }
}

export async function analyzeImageWithAI(
  imageBase64: string,
  context?: string
): Promise<AiSuggestionResponse> {
  try {
    // Note: GPT-4 Vision requires special access. For now, using text-based analysis
    // When GPT-4 Vision access is available, uncomment the vision API call below

    /*
    const completion = await openai.chat.completions.create({
      model: "gpt-4-vision-preview",
      messages: [
        {
          role: "system",
          content: "You are an expert Creative Arts Therapist analyzing client artwork. Provide professional, clinical observations about the visual elements, emotional expression, and therapeutic significance. Focus on colors, forms, composition, technique, and potential symbolic meaning."
        },
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `Analyze this artwork created during a Creative Arts Therapy session. ${context ? `Context: ${context}` : ''} Provide a clinical description suitable for therapy documentation.`
            },
            {
              type: "image_url",
              image_url: {
                url: `data:image/jpeg;base64,${imageBase64}`
              }
            }
          ]
        }
      ],
      max_tokens: 400,
      temperature: 0.6,
    })

    const analysis = completion.choices[0]?.message?.content || "Unable to analyze image"

    return {
      suggestion: analysis.trim(),
      confidence: 0.80,
      alternatives: []
    }
    */

    // For now, use context-based analysis with GPT-3.5-turbo
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are an expert Creative Arts Therapist. Based on the provided context about client artwork, generate professional clinical observations about the creative process, emotional expression, and therapeutic significance."
        },
        {
          role: "user",
          content: `Analyze artwork created during a Creative Arts Therapy session. Context: ${context || 'Client created visual artwork during therapy session'}. Provide a clinical description focusing on the creative process, potential emotional expression, and therapeutic significance for documentation purposes.`
        }
      ],
      max_tokens: 300,
      temperature: 0.6,
    })

    const analysis = completion.choices[0]?.message?.content || "Unable to analyze artwork"

    return {
      suggestion: analysis.trim(),
      confidence: 0.75,
      alternatives: []
    }
  } catch (error) {
    console.error('OpenAI Vision API error:', error)
    // Fallback to text-based analysis
    return {
      suggestion: "Artwork demonstrates client's creative engagement and emotional expression through visual media. The creative process shows therapeutic value and potential for emotional processing. Further detailed analysis available with enhanced image processing capabilities.",
      confidence: 0.60,
      alternatives: []
    }
  }
}

function buildPrompt(request: AiSuggestionRequest): string {
  const { type, context, mediaType, clientAge, sessionGoals, clientHistory, sessionDuration } = request

  let prompt = `Generate a ${type.toLowerCase()} section for a Creative Arts Therapy SOAP note.\n\n`

  if (context) {
    prompt += `Session Context: ${context}\n`
  }

  if (mediaType) {
    prompt += `Media Type: ${mediaType} therapy session\n`
  }

  if (clientAge) {
    prompt += `Client Age: ${clientAge}\n`
  }

  if (sessionDuration) {
    prompt += `Session Duration: ${sessionDuration} minutes\n`
  }

  if (sessionGoals) {
    prompt += `Session Goals: ${sessionGoals}\n`
  }

  if (clientHistory) {
    prompt += `Relevant History: ${clientHistory}\n`
  }

  prompt += `\nPlease provide a professional ${type.toLowerCase()} entry that is:\n`

  switch (type) {
    case 'OBJECTIVE':
      prompt += "- Factual and observable\n- Describes client behaviors and engagement\n- Notes creative process and artistic choices\n- Includes duration and participation level"
      break
    case 'ASSESSMENT':
      prompt += "- Clinical interpretation of observations\n- Progress toward therapeutic goals\n- Emotional and psychological insights\n- Therapeutic relationship quality"
      break
    case 'PLAN':
      prompt += "- Specific next steps and interventions\n- Goals for upcoming sessions\n- Recommendations for treatment\n- Any referrals or consultations needed"
      break
    case 'DESCRIPTION':
      prompt += "- Detailed description of creative work\n- Colors, forms, techniques used\n- Artistic process and engagement\n- Observable emotional expression"
      break
    case 'INTERPRETATION':
      prompt += "- Therapeutic meaning of creative expression\n- Symbolic or emotional significance\n- Connection to treatment goals\n- Clinical insights"
      break
  }

  return prompt
}

function generateMockResponse(request: AiSuggestionRequest): AiSuggestionResponse {
  const mockResponses = {
    OBJECTIVE: "Client engaged actively in art-making process, demonstrating sustained attention for 30 minutes. Selected vibrant colors and created abstract forms with confident brushstrokes. Maintained appropriate eye contact and responded verbally to therapeutic prompts throughout session.",
    ASSESSMENT: "Client shows continued progress in emotional expression through creative media. Increased confidence in artistic choices and willingness to discuss emotional content of artwork. Mood appears stable with positive engagement in therapeutic process.",
    PLAN: "Continue weekly art therapy sessions focusing on emotional expression through color and form. Introduce new media (clay work) to explore tactile processing. Schedule follow-up assessment in 4 weeks to evaluate progress toward treatment goals.",
    DESCRIPTION: "Client created mixed-media artwork featuring bold, expressive brushstrokes in warm colors. The composition shows dynamic movement with overlapping forms and varied textures, suggesting emotional engagement and creative exploration.",
    INTERPRETATION: "The client's use of warm, vibrant colors may indicate improved mood and emotional accessibility. Bold, confident brushstrokes suggest increased self-assurance and willingness to take creative risks in the therapeutic space."
  }

  return {
    suggestion: mockResponses[request.type] || "AI-generated clinical suggestion based on session context.",
    confidence: 0.80,
    alternatives: []
  }
}

export function getPromptTemplate(type: string, mediaType?: string): string {
  const templates = {
    DESCRIPTION: {
      IMAGE: "Describe this artwork created during a creative arts therapy session. Focus on visual elements like colors, shapes, composition, and artistic techniques used. Keep the description clinical and objective.",
      VIDEO: "Describe the movement and creative expression observed in this video from a creative arts therapy session. Focus on body language, movement patterns, and creative process.",
      AUDIO: "Describe the musical or vocal expression captured in this audio recording from a creative arts therapy session. Focus on rhythm, tone, emotional expression, and creative elements.",
      DEFAULT: "Provide a clinical description of the creative expression observed during this therapy session."
    },
    INTERPRETATION: {
      IMAGE: "Provide a clinical interpretation of this artwork, considering potential emotional expression, therapeutic themes, and symbolic elements that may be relevant for treatment planning.",
      VIDEO: "Analyze the movement and creative expression for therapeutic insights, considering emotional regulation, self-expression, and progress indicators.",
      AUDIO: "Interpret the musical/vocal expression for therapeutic significance, considering emotional state, communication patterns, and creative engagement.",
      DEFAULT: "Provide a clinical interpretation of the creative expression and its therapeutic significance."
    },
    OBJECTIVE: "Generate objective observations for a SOAP note based on the creative arts therapy session described. Focus on observable behaviors, creative process, and client engagement.",
    ASSESSMENT: "Provide a clinical assessment based on the creative arts therapy session observations. Consider therapeutic progress, emotional regulation, and treatment goals.",
    PLAN: "Suggest treatment plan elements based on the creative arts therapy session. Include specific interventions, goals, and recommendations for future sessions."
  }

  if (type in templates && typeof templates[type] === 'object') {
    return templates[type][mediaType || 'DEFAULT']
  }
  
  return templates[type] || templates.DESCRIPTION.DEFAULT
}
