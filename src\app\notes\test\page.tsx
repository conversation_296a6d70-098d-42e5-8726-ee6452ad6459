"use client";

import Link from "next/link";

export default function TestPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <h1 className="text-2xl font-bold text-gray-900">Test Page</h1>
            <Link href="/dashboard" className="text-indigo-600">
              Back to Dashboard
            </Link>
          </div>
        </div>
      </header>
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            Test Page Working
          </h2>
          <p className="text-gray-600">
            This is a simple test page to verify the application is working correctly.
          </p>
        </div>
      </main>
    </div>
  );
}
